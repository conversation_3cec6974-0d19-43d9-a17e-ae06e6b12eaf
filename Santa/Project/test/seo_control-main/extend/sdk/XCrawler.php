<?php

namespace sdk;

class XCrawler
{
    // 传递的配置项目
    private static array $config = array();
    private static array $default_config = array(
        'section_number'=>0,
        'section_table' => 'default',
        'news_number' => 0,
        'news_table' => 'default',
    );
    public static function verifyGoogleSite($config = array()) : string
    {
        // 是否开启 gsc 验证
        if ($config['seo_option']['gsc_switch'] === 'on') {
            $verify_content = "google-site-verification: " . $config['seo_option']['gsc_filename']; // 验证内容 正式版直接写在配置文件
            $verify_rule = "/" . $config['seo_option']['gsc_filename']; // 验证规则 需要判断url和
            if (strlen($config['path']) == 28 && strcasecmp($config['path'], $verify_rule) == 0) {
                return $verify_content;
            } else {
                return '';
            }

        }else{
            return '';
        }
    }


    public static function generate_sitemap($config = array()): string
    {
        self::$config = $config;


        $domain = self::$config['domain'];
        $generate_number = intval(self::$config['seo_option']['sitemapItemLimit']);
        $currentURL = self::$config['seo_option']['current_url'];
        $link_rules = !is_array(unserialize(base64_decode($config['ilink_rules']))) ? explode("\n",unserialize(base64_decode($config['ilink_rules']))) : unserialize(base64_decode($config['ilink_rules']));
        $sitemapContent = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemapContent .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">' . "\n";
        $sitemapContent .= "\t<url>\n";
        $sitemapContent .= "\t\t<loc>" . $currentURL  . "</loc>\n";
        $sitemapContent .= "\t\t<lastmod>" . date('Y-m-d') . "</lastmod>\n";
        $sitemapContent .= "\t</url>\n";
        // TL类型
        if(self::$config['type'] == 1){
            $ContentModel = new XContents(self::$default_config);
            $keyword_table = str_replace('seo_','',self::$config['keyword']);
            $tl_data = $ContentModel->getTL_wd($keyword_table, $generate_number);// var_dump($link_rules);

            foreach ($tl_data as $datum=>$data_val){
                $tp = $currentURL . randArr($link_rules);
                $currentLink = str_replace('{tl}',rawurlencode($data_val['link']),$tp);
                $sitemapContent .= "\t<url>\n";
                $sitemapContent .= "\t\t<loc>" . escapeXmlSpecialChars($currentLink) . "</loc>\n";
                $sitemapContent .= "\t</url>\n";
            }
        }

        // 普通模型
        if(self::$config['type'] == 0){
            for($i=0;$i<$generate_number;$i++){
                $currentLink = $currentURL . parseRules(randArr($link_rules));
                $sitemapContent .= "\t<url>\n";
                $sitemapContent .= "\t\t<loc>" . escapeXmlSpecialChars($currentLink) . "</loc>\n";
                $sitemapContent .= "\t</url>\n";

            }
        }

        $sitemapContent .= '</urlset>';
        return $sitemapContent;

    }


    protected static function isExpire($filename = ''):bool{
        if (!file_exists($filename)) {
            return false;
        }

        // 获取当前时间
        $currentTime = time();

        // 获取文件的修改时间
        $fileModificationTime = filemtime($filename);

        // 计算文件的过期时间（1天 = 24小时 = 86400秒）
        $expirationTime = 86400; // 1天的秒数

        // 判断文件是否过期
        if (($currentTime - $fileModificationTime) > $expirationTime) {
            // 文件过期，删除文件
            unlink($filename);
            return false;
        } else {
            // 文件未过期，返回文件内容
            return true;
        }
    }


    /**
     * @param $type 1 生成类型 1-3
     * @param $link_prefix array 传递的链接前缀
     * @param $limit integer 生成的数量
     * @return array|bool 返回一个生成后的索引数组
     */
    private static function generate_links($type = 0, $link_prefix = array(), $limit = 100) : array{
        $links = array();
        switch ($type){
            // 热词
            case 1:
                if (!isset($link_prefix) && empty($link_prefix[0])){
                    return false;
                }
                $keyword =  self::fetch_hotkey($limit);
                foreach ($keyword as $key){
                    $random_dirs = $link_prefix[array_rand($link_prefix)];
                    $links[] = $random_dirs . rawurlencode(str_replace(array("\r\t","\n","\r",PHP_EOL),"",$key)) . '/';
                }
                break;

        }

        return $links;

    }
}