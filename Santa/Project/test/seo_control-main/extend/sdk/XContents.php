<?php


namespace sdk;
use app\base\model\Contents;
use app\base\model\Base;
use think\Db;

class XContents
{
    private $Api_Article_list = array();
    protected $article_url = '';
    private $Api_title_Article = '';
    protected $article_link = 0;
    protected $Api_Article_xiaoshuo = '';
    protected $Api_Article_news = '';
    protected $section_number = 0;
    protected $news_number = 0;
    protected $section_table;
    protected $news_table;

    public function __construct($content_config = array())
    {
        $this->section_number = $content_config['section_number'];
        $this->section_table = $content_config['section_table'];
        $this->news_number = $content_config['news_number'];
        $this->news_table = $content_config['news_table'];
    }

    public function sitemap_xml()
    {

    }

    public function match_keyword($table,$link){
        $BaseModel = new Base();
        $base_result =  $BaseModel->match($table,$link);
        return $base_result;
    }

    public function must_tl_match_keyword($table,$link){
        $BaseModel = new Base();
        $base_result =  $BaseModel->must_match($table,$link);
        return $base_result;
    }

    public function get_contents($types = '')
    {
//        $request_list = array(
//            $this->Api_Article_news,
//            $this->Api_title_Article,
//            $this->Api_Article_xiaoshuo
//        );
//
//        $lists = $this->get_contents_multi($request_list);
        $lists = array();
        $lists[0] = "";
        $re_news = $this->s_contents();
        $list['news'] = $re_news;
        $list['xiaoshuo'] = $lists[0];

        $list['titles'] = $this->get_title($this->news_table,$this->news_number);
//        $list['title'] = json_decode($lists[1], true);
//
//        foreach ($list['title'] as $key => $val) {
//            $list['titles'][$key] = $list['title'][$key]['article_title'];
//        }

        $list['keywords'] = $this->get_keywords($types);

        unset($list['title']);
        return $list;
    }

    public function get_title($title_table = '', $limit = 0)
    {
        $Model = new Contents();
        return $Model->get_title($title_table,$limit);
    }


    public function get_snapshot(){
        $Model = new Contents();
        return $Model->get_contents_by();
    }

    public function new_juzi(){
        $Module = new Contents();
        return $Module->get_contents_bys();
    }

    protected function s_contents()
    {
        $sModel = new Contents();
        $mm = '';
        $iLove = $sModel->get_contents($this->section_table,$this->section_number);
        if(count($iLove) == 1){
            $insert_adder = 0;
        }else{
            $insert_adder = mt_rand(1, count($iLove) - 1); // 获取随机插入行
        }

        foreach ($iLove as $u => $m) {
            if ($u === $insert_adder) {
                $m = rand_in_str($m, "%keywords%", "UTF-8");
                //$m = bd_encode($m);
            }
//                $mm .= "{$m}" . PHP_EOL;
                 $mm .= "{$m}|";

        }
                    return $mm;

    }

    public function getN_wd($types = '',$limit = 0){
        $dBase = new Base();
        return $dBase->model($types,$limit);
    }

    public function getTL_wd($types = '',$limit = 0){
        $dBase = new Base();
        return $dBase->tl_model($types,$limit);
    }

    public function get_section($article_source = '',$limit = 0){
        if($limit == 0){
            return false;
        }
        $Model = new Contents();
        $iLoveu = $Model->get_contents($article_source,$limit);
        return $iLoveu;
    }

    private function get_tl_keyword($tb_name,$limit)
    {
        if ($tb_name !== '') {
            $Model = new Base();
            return $Model->tl_model($tb_name,$limit);
        }
    }

    private function get_keywords($types)
        {
            if ($types !== '') {
                $Model = new Base();
                return $Model->Log($types);
                //return \app\base\model\Base::Log($types);
            }
        }

        function get_contents_multi($urls = array())
        {
            if (!is_array($urls) or count($urls) == 0) {
                return false;
            }
            $curl = $curl2 = $text = array();

            function _createCurl($url)
            {

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36');//设置头部
                curl_setopt($ch, CURLOPT_ENCODING, "gzip"); // 编码压缩
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_MAXREDIRS, 5);//查找次数，防止查找太深
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); // 对认证证书来源的检查
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); // 从证书中检查SSL加密算法是否存在
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_HEADER, 0);//输出头部
                return $ch;
            }

            $handle = curl_multi_init();
            foreach ($urls as $keys => $value) {
                $curl[$keys] = _createCurl($urls[$keys]);
                curl_multi_add_handle($handle, $curl[$keys]);
            }
            $active = null;
            do {
                $mrc = curl_multi_exec($handle, $active);
            } while ($mrc == CURLM_CALL_MULTI_PERFORM);

            while ($active && $mrc == CURLM_OK) {
                // 等待子线程全部返回
                if (curl_multi_select($handle) == -1) {
                    usleep(10);
                }
                // 进程socket连接
                do {
                    $mrc = curl_multi_exec($handle, $active);
                } while ($mrc == CURLM_CALL_MULTI_PERFORM);
            }
            // 获取每条CURL返回的结果集
            foreach ($curl as $k => $v) {
                if (curl_error($curl[$k]) == "") {
                    $contents = (string)curl_multi_getcontent($curl[$k]);
                    $text[$k] = $contents;
                }
                // 移除CURL请求栈
                curl_multi_remove_handle($handle, $curl[$k]);
                curl_close($curl[$k]);
            }
            curl_multi_close($handle);

            return $text;
        }
    }
