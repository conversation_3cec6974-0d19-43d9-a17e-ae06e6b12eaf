<?php


namespace sdk;

use MongoDB\Driver\Exception\Exception;
use think\Config;
use think\Db;
use MongoDB\Client;


class XContainer
{
    public static function getTable($siteId = ''){
        return 'cache_' . strtolower($siteId);
    }


    public static function getExpiringHash($siteId = '',$hash = '',$expiryDate = 0){
        $map = [
            'hash' => $hash
        ];
        $content = Db::connect(Config::get('XContainer'))->name(self::getTable($siteId))->where($map)->field('source,hash,time')->find();

        // var_dump($content);

        // 没有这条缓存
        if(!$content){
            return false;
        }
        // 0 表示永不过期
        if($expiryDate === 0){
            return true;
        }

        $currentTime = time(); // 获取当前时间

        // 判断缓存是否过期
        if (($currentTime - $content['time']) > $expiryDate) {
            // 如果当前缓存过期了 那么直接删除
            self::deleteDataByFieldValue(self::getTable($siteId),'hash',$hash);
            return false;
        }else{
            return true;
        }


    }

    /**
     * 读取缓存
     * @param string $siteId
     * @param string $hash
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function get($siteId='',$hash = ''){
        $map = [
            'hash' => $hash
        ];

        $content = Db::connect(Config::get('XContainer'))->name(self::getTable($siteId))->where($map)->field('source')->find();
        return $content['source'];
    }

    public static function deleteDataByFieldValue($collectionName, $field, $value) {
        try {
            // 创建 MongoDB Manager 连接
            $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
            $db = Config::get('XContainer.database');
            $manager = new \MongoDB\Driver\Manager($dsn);

            // 构建查询条件
            $query = ['$and' => [[$field => $value]]];

            // 创建删除操作
            $bulk = new \MongoDB\Driver\BulkWrite;
            $bulk->delete($query);  // delete语句，删除匹配条件的数据

            // 执行删除操作
            $result = $manager->executeBulkWrite("$db.$collectionName", $bulk);

            // 返回删除结果
            if ($result->getDeletedCount() > 0) {
                return "删除成功";
            } else {
                return "没有找到匹配的数据";
            }
        } catch (\MongoDB\Driver\Exception\Exception $e) {
            return "错误: " . $e->getMessage();
        }
    }


    public static function total($siteId = ''){
        // $content = Db::connect(Config::get('XContainer'))->name(self::getTable($siteId))->where('_id','>','0')->count('_id');
        $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
        $manager = new \MongoDB\Driver\Manager($dsn);
        $command = new \MongoDB\Driver\Command(array("collStats" => self::getTable($siteId)));
        $count = $manager->executeCommand(Config::get('XContainer.database') ,$command)->toArray()[0]->count;
        return $count;
    }


    public static function creates($siteId = '', $data = [])
    {
        // 检测当前集合是否存在索引
        if (!self::hasIndex($siteId, 'hashName')) {
            // 如果索引不存在，创建索引
            self::createIndex($siteId);
        }

        // 插入数据
        $insert = Db::connect(Config::get('XContainer'))
            ->name(self::getTable($siteId))
            ->insert($data);
        return $insert;
    }

    /**  页面缓存是否存在
     * @param string $siteId
     * @param string $hash
     * @return bool
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function exits($siteId = '', $hash = '', $form = '')
    {
        $map = [
            'hash' => $hash
        ];

        $db = Db::connect(Config::get('XContainer'));
        $table = self::getTable($siteId);

        $exits = $db->name($table)->where($map)->find();

        if ($exits === null) {
            return false;
        }

        // 检查是否存在 form 字段
        if (!isset($exits['form']) || empty($exits['form'])) {
            // 更新文档，添加 form 字段
            $db->name($table)->where($map)->update(['form' => $form]);


        }

        return true;
    }


    /**
     * 检测集合是否存在指定索引
     * @param string $siteId 集合名称
     * @param string $indexName 索引名称
     * @return bool
     */
    public static function hasIndex($siteId = '', $indexName = '')
    {
        $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
        $db = Config::get('XContainer.database');
        $manager = new \MongoDB\Driver\Manager($dsn);

        // 获取当前集合的索引信息
        $command = new \MongoDB\Driver\Command([
            "listIndexes" => self::getTable($siteId)
        ]);

        try {
            $cursor = $manager->executeCommand($db, $command);
            $indexes = $cursor->toArray();

            // 检查是否存在指定名称的索引
            foreach ($indexes as $index) {
                if ($index->name === $indexName) {
                    return true; // 索引存在
                }
            }
        } catch (\Exception $e) {
            // 如果集合不存在或查询失败，返回 false
            return false;
        }

        return false; // 索引不存在
    }


    /**
     * 创建集合索引
     * @param string $siteId 集合名称
     * @return bool
     * @throws \MongoDB\Driver\Exception\Exception
     */
    public static function createIndex($siteId = '')
    {
        $value = self::getTable($siteId);
        $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
        $db = Config::get('XContainer.database');
        $manager = new \MongoDB\Driver\Manager($dsn);

        // 定义两个索引：一个是唯一索引（hash），一个是普通索引（form）
        $command = new \MongoDB\Driver\Command([
            "createIndexes" => "{$value}",
            "indexes"       => [
                [
                    "name"   => "hashName",       // 索引名称
                    "key"    => ["hash" => 1],    // 索引字段
                    "unique" => true              // 唯一索引
                ],
                [
                    "name"   => "formIndex",      // 索引名称
                    "key"    => ["form" => 1],    // 普通索引
                    "background" => true          // 建议在后台创建，避免阻塞
                ]
            ],
        ]);

        try {
            $manager->executeCommand($db, $command);
            return true;
        } catch (\Exception $e) {
            // 记录异常信息（可选）
            // error_log("MongoDB Index Creation Failed: " . $e->getMessage());
            return false;
        }
    }



//    /**
//     * 创建索引
//     * @param string $siteId
//     * @return bool
//     * @throws \MongoDB\Driver\Exception\Exception
//     */
//    public static function createIndex($siteId = '')
//    {
//        $value = self::getTable($siteId);
//        $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
//        $db = Config::get('XContainer.database');
//        $manager = new \MongoDB\Driver\Manager($dsn);
//
//        $command = new \MongoDB\Driver\Command([
//            "createIndexes" => "{$value}",
//            "indexes"       => [[
//                "name" => "hashName",
//                "key"  => [ "hash" => 1],
//                "ns"   => "{$db}.{$value}",
//            ]],
//        ]);
//        $result = $manager->executeCommand("seo_control", $command);
//
//        $data = $result->toArray();
//
//        return $data;
//
//    }

    public static function getStorage()
    {
        $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
        $db = Config::get('XContainer.database');
        $manager = new \MongoDB\Driver\Manager($dsn);

        $command = new \MongoDB\Driver\Command(
            ['dbStats' => 1]
        );
        try {
            return $manager->executeCommand("seo_control", $command)->toArray()[0];
        } catch (Exception $e) {
            return [];
        }
    }

    public static function getCollectionStorage($collectionName = '')
    {
        if (empty($collectionName)) {
            return [];
        }

        // MongoDB 连接配置
        $dsn = "mongodb://" . Config::get('XContainer.hostname') . ':27017';
        $db = Config::get('XContainer.database');
        $manager = new \MongoDB\Driver\Manager($dsn);

        // 构建 collStats 命令
        $command = new \MongoDB\Driver\Command(
            [
                'collStats' => self::getTable($collectionName),
            ]
        );

        try {
            // 执行命令并返回结果
            return $manager->executeCommand("seo_control", $command)->toArray()[0]->storageSize;
        } catch (Exception $e) {
            // 异常处理，返回空数组
            return [];
        }
    }
}