<?php
namespace sdk;


class XReferer
{
    public static $config = array();

    public static function checks($config = array()){
        self::$config = $config;


        self::$config['spider'] = rawurldecode(self::$config['spider']);
        if(preg_match('/(UCBrowser|baidubox)/i',self::$config['spider'])){
                return true;
        }

        if(null !== self::$config['referer']) {
            if (strstr(self::$config['referer'], "bsb.baidu")) {
                return true;
            }

            if (preg_match('/(http|https):\/\/([\w.]+\/?)\S*/i', self::$config['referer'])) {
                // 是否来自搜素引擎 sogo/soso/so/sm/baidu/sogou
                if (preg_match('/.*(sogou|360|haosou|so\.com|sm\.cn|baidu|google|bing).*/i', self::$config['referer'])) {
                        return true;
                }

            }
        }
    }

    public static function response(){
        header("Content-type:text/html;charset=UTF-8");
        self::$config['advert_option'] = isset(self::$config['advert_option']) && !empty(self::$config['advert_option']) ? unserialize(self::$config['advert_option']) : [];

        if(isset(self::$config['advert_option']['ban_location_option'])){
            if(self::$config['advert_option']['ban_location_option'] == 'on'){
               // 开启了地区屏蔽

            }
        }

        if(isset(self::$config['advert_option']['ad_js'])){
            $write = 'document.write ("<script src=\"' . self::$config['advert_option']['ad_js'] . '\"></script>");';
            $JavaScriptPack = new XJavaScriptPacker($write,10,true,true);
            $packed = $JavaScriptPack->pack();
            $script = sprintf("<script>%s</script>",rm_eol($packed));
            echo $script;
        }


    }

    public static function isLineContainsUrl(string $text, string $targetUrl): bool {
        // 将文本按行分割成数组
        $lines = preg_split('/\r\n|\r|\n/', $text);

        foreach ($lines as $line) {
            // 使用正则表达式进行全词匹配
            if (preg_match('/\b' . preg_quote($targetUrl, '/') . '\b/', $line)) {
                return true;
            }
        }

        return false;
    }

    // 汉字转Unicode
    protected function UnicodeEncode($str = '')
    {
        return json_encode($str);

    }

    public static function is_mobile($user_agent = ''): bool
    {
        $_SERVER['ALL_HTTP'] = $_SERVER['ALL_HTTP'] ?? '';
        $mobile_browser = '0';
        if(preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|iphone|ipad|ipod|android|xoom)/i', strtolower($user_agent)))
            $mobile_browser++;
        if((isset($_SERVER['HTTP_ACCEPT'])) and (strpos(strtolower($_SERVER['HTTP_ACCEPT']),'application/vnd.wap.xhtml+xml') !== false))
            $mobile_browser++;
        if(isset($_SERVER['HTTP_X_WAP_PROFILE']))
            $mobile_browser++;
        if(isset($_SERVER['HTTP_PROFILE']))
            $mobile_browser++;
        $mobile_ua = strtolower(substr($user_agent,0,4));
        $mobile_agents = array(
            'w3c ','acs-','alav','alca','amoi','audi','avan','benq','bird','blac',
            'blaz','brew','cell','cldc','cmd-','dang','doco','eric','hipt','inno',
            'ipaq','java','jigs','kddi','keji','leno','lg-c','lg-d','lg-g','lge-',
            'maui','maxo','midp','mits','mmef','mobi','mot-','moto','mwbp','nec-',
            'newt','noki','oper','palm','pana','pant','phil','play','port','prox',
            'qwap','sage','sams','sany','sch-','sec-','send','seri','sgh-','shar',
            'sie-','siem','smal','smar','sony','sph-','symb','t-mo','teli','tim-',
            'tosh','tsm-','upg1','upsi','vk-v','voda','wap-','wapa','wapi','wapp',
            'wapr','webc','winw','winw','xda','xda-'
        );
        if(in_array($mobile_ua, $mobile_agents))
            $mobile_browser++;
        if(strpos(strtolower($_SERVER['ALL_HTTP']), 'operamini') !== false)
            $mobile_browser++;
        if(strpos(strtolower($user_agent), 'windows') !== false)
            $mobile_browser=0;
        if(strpos(strtolower($user_agent), 'windows phone') !== false)
            $mobile_browser++;
        if($mobile_browser>0)
            return true;
        else
            return false;
    }
}