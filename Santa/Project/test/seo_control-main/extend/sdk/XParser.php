<?php

/**
 *  重写解析器
 */
namespace sdk;
use addons\HanziConvert;
use think\Config;
use think\Db;

class XParser
{
    private static string $template = '';  // 模板源
    private static array $section_tag = array(); // 文章句子数组
    private static array $r_section_tag = array(); // 随机句子
    private static array $title_tag = array(); // 随机标题数组
    private static string $keyword = ''; // 关键词库
    private static array $page_keyword = array(); // 页面关键词
    private static array $iflnk_rules = array(); //轮链
    private static $encode_style = ''; // 模板混淆
    private static array $images_list = array(); // 随机图片
    private static string $domain = ''; // 当前域名
    private static array $slink = array(); // 外链列表
    private static array $config = array(); //配置
    private static string $kw_section = ''; // 关键词句子
    private static array $section_length = array(); // 句子长度
    private static array $r_section_length = array(); // r句子长度
    private static string $explain_url = ''; // 解析的URL
    private static array $dynamic_config = array(); // 动态渲染时的配置
    private static array $txt_section_tag = array(); // 匹配的文章句子标签
    private static array $txt_section_number = array(); // 匹配的包含关键词的索引
    private static array $txt_keyword_number = array(); // 在句子中的随机关键词序号


    private static function _recommend(){
        $template = "<p>คุณอาจสนใจ:</p>
<div>\t\n";
        $template .= self::getTags() ."\t";
        $template .= "</div>";
        return $template;
    }

    private static function getTextByLines($filename = '',$limit = 1){

        if(is_file($filename)){;
            $file_count = getCountLine($filename);
            if($limit > $file_count){
                return false;
            }

            $startLine = mt_rand(1,$file_count - $limit);
            $endLine = $startLine + ($limit - 1);

            return getFileLines($filename,$startLine,$endLine,'rb');
        }
    }


    public static function raw($config = array()){

        self::$config = $config;

        $template = explode(",",$config['seo_option']['project_template']);
        if(count($template) > 1){
            $template_id = mt_rand(0,count($template) - 1);
            shuffle($template);
            $template_name = $template[$template_id];
            if(!file_exists(Config::get('tpl_dir') . $template_name)){
                exit('ERR: 当前模板' . $template_name . '未找到!');
            }

            self::$template = file_get_contents(realpath(Config::get('tpl_dir') . $template_name));
        }else{
            $template_name = Config::get('tpl_dir') . $template[0];
            if(!file_exists(realpath(Config::get('tpl_dir') . $template[0]))){
                exit('ERR: 当前模板' . $template[0] . '未找到!');
            }
            self::$template = file_get_contents(realpath(Config::get('tpl_dir') . $template[0]));
        }


        // 判断当前内容模型类型 == 1 则是tl对应

        if($config['type'] == 1){
            self::$keyword = str_replace('seo_','',$config['keyword']);
        }else{
            $keyword = explode(",",$config['keyword']);
            if(count($keyword) > 1){
                // 多关键词,随机一个关键词类型
                $keyword_id = mt_rand(0,count($keyword) - 1);
                // 打乱数组
                shuffle($keyword);
                $keyword_class = $keyword[$keyword_id];
                self::$keyword = str_replace('seo_','',$keyword_class);
            }else{
                self::$keyword = str_replace('seo_','',$config['keyword']);
            }
        }

        if($config['type'] == 1) {
            self::$iflnk_rules = !is_array(unserialize(base64_decode($config['ilink_rules']))) ? explode("\n", unserialize(base64_decode($config['ilink_rules']))) : unserialize(base64_decode($config['ilink_rules']));
            $matched = false;
            foreach (self::$iflnk_rules as $rule) {
                // 将 {tl} 替换为捕获组 [^\/]+，并使用 preg_quote 转义其他特殊字符
                $regex = preg_quote($rule, '/');
                $regex = str_replace('\{tl\}', '([^\/]+)', $regex);  // 将转义的 {tl} 替换为捕获组
                // 使用不同的分隔符
                $pattern = '#^' . $regex . '$#u';
                // 用生成的正则进行匹配
                if (preg_match($pattern, $config['path'], $matches)) {
                    $matched = true;
                    self::$explain_url = $matches[1];
                    break;  // 找到匹配就停止
                }
            }

            if (!$matched) {
                self::$explain_url = "";
            }


        }
        // 这里要处理使用了什么前缀

        // 在配置文件中获取 配置的前缀 或者 后缀
        // 前缀模式 /app/{tl}/
        // 后缀模式 /{tl}-vod.html

        // 静态标签先处理
        // 为了节省资源 匹配到标签在处理
        if(preg_match_all('/\{\:|标题|描述|关键词\}/',self::$template)){
            self::_parserStyle();
        }

        self::$template = str_replace('{作者}',self::get_profile('author.txt'),self::$template);

        self::$template = str_replace('{阅读数}',rand_number(4000,999999),self::$template);

        self::$template = str_replace('{副标题}',self::get_profile('subtitle.txt'),self::$template);

        self::$template = str_replace('{本地描述}',self::get_profile('description.txt'),self::$template);


        // 兼容影视模板

//        self::$template = str_replace('{分类}',rm_eol(getTextByline('class.txt')),self::$template);
//
//        self::$template = str_replace('{地区}',rm_eol(getTextByline('local.txt')),self::$template);


        // 获取随机句子标签 文章里用 包含了关键词 支持字符截取
        if(preg_match_all("/\{(文章句子|文章段落)(\d+)\:(\d+)\}/",self::$template,$section_number)){
            if(!empty($section_number[0])){
                self::$section_tag = $section_number[0];
                self::$section_length = $section_number[3];
            }
        }

        if (preg_match_all("/\{文章句子(\d{1,2})\|{随机关键词(\d{1,2})}\}/", self::$template, $txt_number)) {
            // 初始化用于存储已使用数字的数组
            $usedSectionNumbers = array();
            $usedKeywordNumbers = array();

            if (!empty($txt_number[0])) {
                $uniqueSectionTags = array();
                $uniqueSectionNumbers = array();
                $uniqueKeywordNumbers = array();

                foreach ($txt_number[0] as $index => $tag) {
                    $sectionNumber = $txt_number[1][$index];
                    $keywordNumber = $txt_number[2][$index];

                    // 检查文章句子的数字是否已经存在
                    if (!in_array($sectionNumber, $usedSectionNumbers)) {
                        $usedSectionNumbers[] = $sectionNumber;

                        // 检查随机关键词的数字是否已经存在
                        if (!in_array($keywordNumber, $usedKeywordNumbers)) {
                            $usedKeywordNumbers[] = $keywordNumber;

                            // 将唯一的匹配项添加到数组中
                            $uniqueSectionTags[] = $tag;
                            $uniqueSectionNumbers[] = $sectionNumber;
                            $uniqueKeywordNumbers[] = $keywordNumber;
                        }
                    }
                }

                // 赋值给相应的变量
                self::$txt_section_tag = $uniqueSectionTags;
                self::$txt_section_number = $uniqueSectionNumbers;
                self::$txt_keyword_number = $uniqueKeywordNumbers;
            }
        }


        if(preg_match_all("/\{(随机标题|随机新闻|标题|新闻|文章标题|热词|新闻标题)(\d+)\}|\{#news_(\d+)#\}/",self::$template,$title_number)){
            if(!empty($title_number[0])){
                self::$title_tag = $title_number[0];
            }
        }

        if(preg_match_all("/\{(随机句子|句子)(\d+)\:(\d+)\}/",self::$template,$r_section)){
            if(!empty($r_section[0])){
                self::$section_tag = $r_section[0];
                self::$section_length = $r_section[3];
            }
        }

        if(preg_match_all("/\{(图片|随机图片名)(\d+)\}|\{#images_(\d+)#\}/",self::$template,$images)){
            if(!empty($images[0])){
                self::$images_list = $images[0];
            }
        }

        // 由于两个标签作用不同，现在对标签数据源进行划分

        //$XContents = new XContents(count(self::$section_tag),count(self::$title_tag));


        $XContents = new XContents([
            'section_number'=> count(self::$section_tag) > 0 ? count(self::$section_tag) : 1,
            'section_table'=> self::$config['seo_option']['article_source'],
            'news_number'=>count(self::$title_tag),
            'news_table'=> self::$config['seo_option']['title_source'],
        ]);

//        if ($config['type'] == 1) {
//            if (self::$config['seo_option']['tl_url_must_verify'] === 'on') {
//                $words = $XContents->must_tl_match_keyword(self::$keyword, self::$explain_url);
//                if (!$words) {
//                    @header('HTTP/1.1 404 Not Found');
//                    exit;
//                }
//            } else {
//                // 未开启强验证继续获取内容
//                $words = $XContents->match_keyword(self::$keyword, self::$explain_url);
//            }
//            self::$page_keyword = [
//                'title' => $words,
//                'text'  => $words
//            ];
//        } else {
//            $Xctest_reset = $XContents->get_contents(self::$keyword);
//            self::$page_keyword = $Xctest_reset['keywords'];
//        }
//
//// 获取内容（为了后续 titles、news 使用）
//        if (!isset($Xctest_reset)) {
//            $Xctest_reset = $XContents->get_contents(self::$keyword);
//        }
////        }else{
////            $Xctest_reset = $XContents->get_contents(self::$keyword);
////            self::$page_keyword = $Xctest_reset['keywords'];
////        }
//            $Xctest_reset = $XContents->get_contents(self::$keyword);
//            self::$page_keyword = $Xctest_reset['keywords'];


        if ($config['type'] == 1) {
            // 是否开启强验证
            if (self::$config['seo_option']['tl_url_must_verify'] === 'on') {
                $words = $XContents->must_tl_match_keyword(self::$keyword, self::$explain_url);
                if (!$words) {
                    @header('HTTP/1.1 404 Not Found');
                    exit;
                }
            } else {
                $words = $XContents->match_keyword(self::$keyword, self::$explain_url);
            }

            // 设置关键词内容
            if (!is_array($words)) {
                self::$page_keyword = [
                    'title' => $words,
                    'text'  => $words
                ];
            } else {
                self::$page_keyword = $words;
            }

            // 验证通过后再调用 get_contents
            $Xctest_reset = $XContents->get_contents(self::$keyword);
        } else {
            // 非 type=1，直接获取完整内容和关键词
            $Xctest_reset = $XContents->get_contents(self::$keyword);
            self::$page_keyword = $Xctest_reset['keywords'];
        }

        if(count(self::$txt_section_tag) > 0) {
            $r_section_list = $XContents->get_section(self::$config['seo_option']['article_source'], count(self::$txt_section_number));
        }

        $img_data = self::get_pic(self::$config['seo_option']['pic_source'],count(self::$images_list));
        $slink = $Xctest_reset['titles'];
        $xw_contents = $Xctest_reset['news'];


        // self::$page_keyword = $Xctest_reset['keywords'];

        $xw_content = explode("|",$xw_contents);

        // 查找带有关键词的句子
        for($i=0;$i<count($xw_content);$i++)
        {
            if(strpos($xw_content[$i],'%keywords%') > 0){
                self::$kw_section = rm_eol($xw_content[$i]);
                break;
            }
        }

        // 替换随机标题
        for ($i=0;$i<count(self::$title_tag);$i++){
            self::$template = str_replace(self::$title_tag[$i],htmlspecialchars(rm_eol($slink[$i])),self::$template);
        }

        for($i=0;$i<count(self::$txt_section_number);$i++){
                $style = '{文章句子' . self::$txt_section_number[$i] . '|{随机关键词' . self::$txt_keyword_number[$i] . '}}';
                $keyword_style = '<strong> {随机关键词' . self::$txt_keyword_number[$i] .'} </strong> ';

                self::$template = str_replace($style,
                    rand_in_str(rm_eol($r_section_list[$i]),$keyword_style,'utf-8'),
                    self::$template
                );
        }

        // 替换文章段落
        foreach(self::$section_tag as $item=>$value){
            self::$template = str_replace($value,htmlspecialchars(subText(rm_eol($xw_content[$item]),self::$section_length[$item])),self::$template);
        }

        // 替换随机句子
        foreach(self::$r_section_tag as $item=>$value){
            self::$template = str_replace($value,htmlspecialchars(subText(rm_eol($xw_content[$item]),self::$r_section_length[$item])),self::$template);
        }

        // 替换图片
        foreach(self::$images_list as $item=>$value){
            self::$template = str_replace($value,rm_eol($img_data[$item]),self::$template);
        }

        // Beta 版本更新 可能随时会取消
//        $dir_prefix = array("/book/", "/bet/", "/slot/", "/thnews/", "/android/", "/ios/");
//        self::$template = str_replace('{PATH关键词}', htmlspecialchars(strtoupper(rtrim(str_replace($dir_prefix, "", self::$config['path']), "/"))), self::$template);

        // self::$template = str_replace('{相关推荐}', self::_recommend(), self::$template);

        // ----------------------------


        self::$kw_section = str_replace('%keywords%',self::$page_keyword['title'],self::$kw_section);

        self::$template = str_replace('{关键词句子}',htmlspecialchars(self::$kw_section),self::$template);

        self::$template = str_replace('%keywords%','<b>' . self::$page_keyword['title'] . '</b>',self::$template);

        self::$template = str_replace('{关键词}',htmlspecialchars(strtoupper(self::$page_keyword['title'])),self::$template);

        self::$template = str_replace('{关联关键词}',htmlspecialchars(strtoupper(self::$page_keyword['text'])),self::$template);

//        self::$template = str_replace('{格式化时间}',date('Y-m-d\TH:i:sP'),self::$template);
        self::$template = str_replace('{格式化时间}',getFormatDate($config['seo_option']['page_language']),self::$template);

        // 在某些模板里 有评论
        self::$template = preg_replace_callback('/\{随机昵称\}/',function (){
            return rm_eol(getTextByline('author.txt'));
        },self::$template);

        // 处理内链 论链
        self::$iflnk_rules = !is_array(unserialize(base64_decode($config['ilink_rules']))) ? explode("\n",unserialize(base64_decode($config['ilink_rules']))) : unserialize(base64_decode($config['ilink_rules']));

        if($config['type'] == 1) {
            if(preg_match_all("/\{随机关键词(\d+)\}/",self::$template,$kw_list)){

                $tl_count = array_values(array_unique($kw_list[1]));


                $tl_data = $XContents->getTL_wd(self::$keyword, count($tl_count));



                $link_index = 0;
                $link_title = 1;

                for($i=0;$i<count($tl_count);$i++){
                    $keyword_text = rm_eol($tl_data[$link_index]['text']);
                    $link = rawurlencode($tl_data[$link_index]['link']); // 需要对URL进行非ASCII进行转码

                    self::$template = str_replace('{随机关键词' . $link_title . '}', $keyword_text, self::$template);
                    // var_dump($link);
                    self::$template = preg_replace('/\{轮链\}/', str_replace('{tl}',$link,parseRules(randArr(self::$iflnk_rules))), self::$template, 1);
                    $link_index++;
                    $link_title++;

                }
            }
        }else{
            self::$template = preg_replace_callback('/\{轮链\}/', function () {
                return parseRules(randArr(self::$iflnk_rules));
            }, self::$template);

            if(preg_match_all("/\{随机关键词(\d+)\}/",self::$template,$kw_list)){
                $r_kw = $XContents->getN_wd(self::$keyword,count($kw_list[0]));
                foreach ($kw_list[0] as $item => $value){
                    self::$template = str_replace($value,rm_eol($r_kw[$item]),self::$template);
                }
            }
        }
        // Lite模式下project_url为空 获取GET的domain作为域名
        self::$domain = !isset($config['project_url']) ?
            ((isset($config['protocol']) ? $config['protocol'] . '://' : 'http://') . $config['domain']) :
            $config['project_url'];


        // 提供给PATH查询使用
        self::$template = str_replace('{当前LINK}',self::$explain_url,self::$template);

        self::$template = str_replace('{当前URL}',self::$domain . $config['path'],self::$template);

        self::$template = preg_replace_callback('/\{随机数字(\d{1,2})\}/',function ($mathCase){
            return createRandomStr($mathCase[1],true,false);
        },self::$template);

        self::$template = preg_replace_callback('/\{中文时间(\d{1,2})\}/',function ($math){
            return date_cn($math[1]);
        },self::$template);

        self::$template = preg_replace_callback('/\{英文时间(\d{1,2})\}/',function ($math){
            return date_en($math[1],self::$config['seo_option']['page_language']);
        },self::$template);


        self::$template = preg_replace_callback('/\{随机字符\}/',function ($math){
            return createRandomStr(5);
        },self::$template);

        if(preg_match_all("/<简转繁>(.*?)<\/简转繁\>/",self::$template,$e_coding)){
            require ADDOND_PATH . 'zh-to-cht/HanziConvert.php';
            foreach($e_coding[0] as $item){
                $items = str_replace(array('<简转繁>','</简转繁>'),'',$item);
                self::$template = str_replace($item,HanziConvert::convert($items,true),self::$template);
            }
        }

        // 拼音编码
        if(preg_match_all("/<pinyin编码>(.*?)<\/pinyin编码\>/",self::$template,$e_coding)){
            foreach($e_coding[0] as $item){
                $items = str_replace(array('<pinyin编码>','</pinyin编码>'),'',$item);
                self::$template = str_replace($item,split_chinese($items),self::$template);
            }
        }

        // 支持截取函数
        if(preg_match_all("/<截取\:(\d+)>(.*?)<\/截取\>/",self::$template,$sub_coding)){
            foreach($sub_coding[0] as $k=>$item){
                $items = preg_replace("/<截取\:(\d+)>/",'',$item);
                $items = preg_replace("/<\/截取\>/",'',$items);
                self::$template = str_replace($item,subText($items,$sub_coding[1][$k]),self::$template);
            }
        }

        // 特殊字符编码
        if(preg_match_all("/<e编码>(.*?)<\/e编码\>/",self::$template,$e_coding)){
            foreach($e_coding[0] as $item){
                $items = str_replace(array('<e编码>','</e编码>'),'',$item);
                self::$template = str_replace($item,special_encode($items),self::$template);
            }
        }

        // 中括号字符编码
        if(preg_match_all("/<k编码>(.*?)<\/k编码\>/",self::$template,$k_coding)){
            foreach($k_coding[0] as $item){
                $items = str_replace(array('<k编码>','</k编码>'),'',$item);
                self::$template = str_replace($item,k_encode($items),self::$template);
            }
        }

        // k2字符编码
        if(preg_match_all("/<k2编码>(.*?)<\/k2编码\>/",self::$template,$k_coding)){
            foreach($k_coding[0] as $item){
                $items = str_replace(array('<k2编码>','</k2编码>'),'',$item);
                self::$template = str_replace($item,k2_encode($items),self::$template);
            }
        }

        // 空格字符编码
        if(preg_match_all("/<s编码>(.*?)<\/s编码\>/",self::$template,$e_coding)){
            foreach($e_coding[0] as $item){
                $items = str_replace(array('<s编码>','</s编码>'),'',$item);
                self::$template = str_replace($item,space_encode($items),self::$template);
            }
        }


        // HTML实体 ANSI 编码
        if(preg_match_all("/<a编码>(.*?)<\/a编码\>/",self::$template,$a_coding)){
            foreach($a_coding[0] as $item){
                $items = str_replace(array('<a编码>','</a编码>'),'',$item);
                self::$template = str_replace($item,ansi_encode($items),self::$template);
            }
        }


        // HTML Unicode字符编码
        if(preg_match_all("/<u编码>(.*?)<\/u编码\>/",self::$template,$u_coding)){
            foreach($u_coding[0] as $item){
                $items = str_replace(array('<u编码>','</u编码>'),'',$item);
                self::$template = str_replace($item,escape($items),self::$template);
            }
        }

        // URL编码
        if(preg_match_all("/<ue编码>(.*?)<\/ue编码\>/",self::$template,$ue_coding)){
            foreach($ue_coding[0] as $item){
                $items = str_replace(array('<ue编码>','</ue编码>'),'',$item);
                self::$template = str_replace($item,urlencode($items),self::$template);
            }
        }
        $pattern = '/<html\b(.*?)>/i';
        $page_language = $config['seo_option']['page_language'];

        // 替换页面language
        self::$template = preg_replace_callback($pattern, function ($matches) use ($page_language) {
            $tag = $matches[0];
            $attributes = $matches[1];

            // 检查是否存在lang属性
            if (preg_match('/\blang="([^"]*)"/i', $attributes, $langMatches)) {
                // 有lang属性，替换其值
                $newAttributes = str_replace($langMatches[0], "lang=\"{$page_language}\"", $attributes);
            } else {
                // 没有lang属性，添加lang属性
                $newAttributes = $attributes . " lang=\"{$page_language}\"";
            }

            return str_replace($attributes, $newAttributes, $tag);
        }, self::$template);


        // 模板混淆
        self::$encode_style = isset($config['seo_option']['encode_style']) ? explode(',',$config['seo_option']['encode_style']) : $config['seo_option']['encode_style'];

        // 设置了模板混淆才执行
        if(is_array(self::$encode_style)) {
            if (count(self::$encode_style) > 1) {
                self::_parsermultEncode(); // 复合混淆
            } else {
                self::_parserEncode(); // 单次混淆
            }
        }

        self::$template = preg_replace_callback('/\{随机字符\}/',function ($math){
            return createRandomStr(5);
        },self::$template);

        return self::$template;
    }

    // 动态处理
    public static function response($source = '', $config = array()){
        $html = empty($source) ? self::$template : $source;
        self::$dynamic_config = $config;

        $html = preg_replace_callback('/\{动态中文时间(\d)\}/',function ($math){
            return date_cn($math[1]);
        },$html);

        $html = preg_replace_callback('/\{动态英文时间(\d)\}/',function ($math){
            return date_en($math[1],self::$dynamic_config['seo_option']['page_language']);
        },$html);

        $html = preg_replace_callback('/\{随机数字(\d{1,2})\}/',function ($mathCase){
            return createRandomStr($mathCase[1],true,false);
        },$html);

        $html = preg_replace_callback('/\{动态数字(\d{1,2})\}/',function ($mathCase){
            return createRandomStr($mathCase[1],true,false);
        },$html);

        if(preg_match_all('/\{外链\}|\{外链href\d+\}|\{随机外链\}/i',$html,$slink_match)) {
            // 系统外链
            if($config['elink_option']['elink_class'] == '0') {
                // 处理外链 获取外连列表的数据
                if (!file_exists(OUT_PATH . 'cache' . DS . 'href.json')) {
                    $result = Db::connect('ProjectDB')->table('seo_slink')->field('sdomain,srules')->order('sid', 'desc')->select();
                    if ($result) {
                        file_put_contents(OUT_PATH . 'cache' . DS . 'href.json', json_encode($result));
                        self::$slink = $result;
                    } else {
                        exception('没有外链数据。');
                    }
                } else {
                    self::$slink = json_decode(file_get_contents(OUT_PATH . 'cache' . DS . 'href.json'), true);
                }

                // 判断泛域名文件是否存在
                $Subdomain_file = realpath(OUT_PATH . 'cache' . DS . 'Subdomain.txt');
                if(file_exists($Subdomain_file)){
                    $Subdomain_file = realpath(OUT_PATH . 'cache' . DS . 'Subdomain.txt');
                    $file_count = getCountLine($Subdomain_file);
                    if($file_count > 0){
                        for($i=0;$i<2;$i++) {
                            $html = preg_replace('/\{外链\}|\{外链href\d+\}|\{随机外链\}/i', str_replace('*', createRandomStr(5,false,true), rm_eol(getTxtByRandom($Subdomain_file))), $html, 1);
                        }
                    }

                }
                // 执行替换
                $html = preg_replace_callback('/\{外链\}|\{外链href\d+\}|\{随机外链\}/i',function ($match){
                    return self::_parserSlink(self::$slink);
                },$html);
            }

            // 自定义外链
            if($config['elink_option']['elink_class'] == '1') {
                self::$slink =  explode("\n",unserialize(base64_decode($config['elink_option']['elink_rules'])));

                // 执行替换
                $html = preg_replace_callback('/\{外链\}|\{外链href\d+\}|\{随机外链\}/i',function (){
                    return self::_parserCostSlink(self::$slink);
                },$html);
            }
        }
                return $html;
    }

    /** 压缩HTML标签
     * @param string $str 钉钉
     * @return string|string[]
     */
    public static function compress(string $str = ''){
//        if(preg_match_all('/<html[^>]*>(.*?)<\/html>/is',$str,$body)){
//            $html = ltrim(rtrim(preg_replace(array("/> *([^ ]*) *</","//","'/\*[^*]*\*/'","/\r\n/","/\n/","/\t/",'/>[ ]+</'),array(">\\1<",'','','','','','><'),$body[0][0])));
//            return str_replace($body[0][0],$html,$str);
//        }
        $html = preg_replace('/<!--(.|\s)*?-->/', '', $str);
        // 删除空格、制表符和换行符
        $html = str_replace(array("\r\n", "\r", "\n", "\t", '  ', '    ', '    '), '', $html);

        return $html;
    }

    /**
     *
     */

    private static function _parserEncode(){
        switch (self::$encode_style[0]){
            case 'class':
                // 随机CLASS
                if(preg_match_all("/class=\"([^\"]+)\"/",self::$template,$match_class)){
                    self::$template = preg_replace_callback("/class=\"([^\"]+)\"/",function($match) {
                        return 'class="' . $match[1] . ' data-' . createRandomStr(10,false,true) . '"';
                    },self::$template);
                }
                break;
            case 'id':
                if(preg_match_all("/id=\"([^\"]+)\"/",self::$template,$match_class)){
                    self::$template = preg_replace_callback("/id=\"([^\"]+)\"/",function($match) {
                        return 'id="' . $match[1] . ' ' . createRandomStr(8) . '"';
                    },self::$template,15);
                }
                break;
            case 'tag':
                $tag = ['div','ul'];
                shuffle($tag);
                $rand_tag = mt_rand(0,count($tag) - 1);
                $tags = $tag[$rand_tag];
                $reg = <<< EOF
#<{$tags}(?=\s)(?!(?:[^>"\']|"[^"]*"|\'[^\']*\')*?(?<=\s)(?:term|range)\s*=)(?!\s*/?>)\s+(?:".*?"|\'.*?\'|[^>]*?)+>(.*?)#i
EOF;
                if(preg_match_all($reg,self::$template)){
                    self::$template = preg_replace_callback($reg,function($match){
                        return $match[0] . PHP_EOL . read_txt('tag.txt');
                    },self::$template,1);
                }
                break;

        }
    }

    private static function _parsermultEncode(){
        for($i=0;$i<count(self::$encode_style);$i++){
            switch (self::$encode_style[$i]){
                case 'class':
                    // 随机CLASS
                    if(preg_match_all("/class=\"([^\"]+)\"/",self::$template,$match_class)){
                        self::$template = preg_replace_callback("/class=\"([^\"]+)\"/",function($match) {
                            return 'class="' . $match[1] . ' ' . createRandomStr(10,false,true) . '"';
                        },self::$template);
                    }
                    break;
                case 'id':
                    if(preg_match_all("/id=\"([^\"]+)\"/",self::$template,$match_class)){
                        self::$template = preg_replace_callback("/id=\"([^\"]+)\"/",function($match) {
                            return 'id="' . $match[1] . ' ' . createRandomStr(8) . '"';
                        },self::$template,15);
                    }
                    break;
                case 'tag':
                    $tag = ['div','ul'];
                    shuffle($tag);
                    $rand_tag = mt_rand(0,count($tag) - 1);
                    $tags = $tag[$rand_tag];
                    $reg = <<< EOF
#<{$tags}(?=\s)(?!(?:[^>"\']|"[^"]*"|\'[^\']*\')*?(?<=\s)(?:term|range)\s*=)(?!\s*/?>)\s+(?:".*?"|\'.*?\'|[^>]*?)+>(.*?)#i
EOF;
                    if(preg_match_all($reg,self::$template)){
                        self::$template = preg_replace_callback($reg,function($match){
                            return $match[0] . PHP_EOL . read_txt('tag.txt');
                        },self::$template,1);
                    }
                    break;

            }
        }
    }

    /**
     * 解析自定义外链规则
     * @param array $slink
     * @return string
     */

    public static function _parserCostSlink(array $slink = array()): string
    {
        shuffle($slink);
        $slink_tree = array_rand($slink);
        return rm_eol(parseRules($slink[$slink_tree]));
    }



    /**  解析系统外链规则
     * @param array $slink
     * @return string
     */
    public static function _parserSlink(array $slink = array()): string
    {
        shuffle($slink);
        $slink_tree = array_rand($slink);
        $url = $slink[$slink_tree]['sdomain'];
        $rules = unserialize($slink[$slink_tree]['srules']);
        shuffle($rules);
        $rules_tree = array_rand($rules);
        return rm_eol($url . parseRules($rules[$rules_tree]));
    }


    /**
     *  TKD全局编码解析规则
     * @param string $style 编码样式
     * @param string $encode 编码内容
     * @param string $position 编码位置
     * @param string $keepstr 编码位置
     * @return string
     */

    private static function _parserEncodeRule(string $style = '',string $encode = '', string $position = '',string $keepstr = '') : string
    {

        // self::$config['seo_option']['tdk_encode_style']
        $tdk_encode_style = explode(",",self::$config['seo_option']['tdk_encode_style']);
        if(!in_array($position,$tdk_encode_style)){
            return $style . $keepstr;
        }
        $result = '';
                switch ($encode) {
                    // ASIN编码
                    case 'ascii':
                        $result = '<a编码>' . $style . '</a编码>' . $keepstr;
                        break;
                    // unicode 编码
                    case 'unicode':
                        $result = '<u编码>' . $style . '</u编码>'. $keepstr;
                        break;
                    default:
                        $result = $style . $keepstr;
                        break;

        }

        return $result;
    }

    /**
     *  解析header规则
     */
    private static function _parserStyle(){
        if(isset(self::$config['seo_option']) && is_array(self::$config['seo_option'])){
                $title_style = explode(",",self::$config['seo_option']['title_style']);
                shuffle($title_style);
                $t_rands = mt_rand(0,count($title_style) - 1);
                switch ($title_style[$t_rands]){
                    case '1':
                        //self::$template = str_replace('{:标题}','<a编码>{关键词}</a编码>',self::$template);
                        //self::$template = str_replace('{:标题}','<a编码>{关键词}</a编码>',self::$template);
                        self::$template = str_replace('{:标题}',self::_parserEncodeRule('{关键词}',self::$config['seo_option']['tdk_encode'],'title'),self::$template);

                        break;
                    case '2':
                        //self::$template = str_replace('{:标题}','<a编码>{关键词}</a编码> -{副标题}',self::$template);
                        self::$template = str_replace('{:标题}',self::_parserEncodeRule('{关键词}',self::$config['seo_option']['tdk_encode'],'title',' -{副标题}'),self::$template);

                        break;
                    case '3':
                        //self::$template = str_replace('{:标题}','<a编码>{关键词} -{关联关键词}</a编码>',self::$template);
                        self::$template = str_replace('{:标题}',self::_parserEncodeRule('{关键词} -{关联关键词}',self::$config['seo_option']['tdk_encode'],'title'),self::$template);

                        break;
                    case '4':
                        // self::$template = str_replace('{:标题}','<a编码>{关键词}-{随机关键词1}-{随机关键词2}</a编码>',self::$template);
                        self::$template = str_replace('{:标题}',self::_parserEncodeRule('{关键词}-{随机关键词1}-{随机关键词2}',self::$config['seo_option']['tdk_encode'],'title'),self::$template);

                        break;
                    case '5':
                        // self::$template = str_replace('{:标题}','<a编码>{关键词}</a编码>-{新闻标题1}',self::$template);
                        self::$template = str_replace('{:标题}',self::_parserEncodeRule('{关键词}-{新闻标题1}',self::$config['seo_option']['tdk_encode'],'title'),self::$template);

                        break;
                }

                $keyword_style = explode(",",self::$config['seo_option']['keyword_style']);
                shuffle($keyword_style);
                $k_rands = mt_rand(0,count($keyword_style) - 1);
                switch ($keyword_style[$k_rands]){
                case '1':
                    //self::$template = str_replace('{:关键词}','<a编码>{关键词}</a编码>',self::$template);
                    self::$template = str_replace('{:关键词}',self::_parserEncodeRule('{关键词}',self::$config['seo_option']['tdk_encode'],'keyword'),self::$template);

                    break;
                case '2':
                    //self::$template = str_replace('{:关键词}','<a编码>{关键词},{关联关键词}</a编码>',self::$template);
                    self::$template = str_replace('{:关键词}',self::_parserEncodeRule('{关键词},{关联关键词}',self::$config['seo_option']['tdk_encode'],'keyword'),self::$template);

                    break;
                case '3':
                    //self::$template = str_replace('{:关键词}','<a编码>{关键词},{随机关键词1},{随机关键词2}</a编码>',self::$template);
                    self::$template = str_replace('{:关键词}',self::_parserEncodeRule('{关键词},{随机关键词1},{随机关键词2}',self::$config['seo_option']['tdk_encode'],'keyword'),self::$template);

                    break;
                case '4':
                    //self::$template = str_replace('{:关键词}','<a编码>{随机关键词1}</a编码>',self::$template);
                    self::$template = str_replace('{:关键词}',self::_parserEncodeRule('{随机关键词1}',self::$config['seo_option']['tdk_encode'],'keyword'),self::$template);

                    break;
            }

            $description_style = explode(",",self::$config['seo_option']['description_style']);
                shuffle($description_style);
            $d_rands = mt_rand(0,count($description_style) - 1);
            switch ($description_style[$d_rands]){
                case '1':
                    // self::$template = str_replace('{:描述}','<a编码>{关键词}</a编码>，<截取:70>{随机句子1}</截取>',self::$template);
                    self::$template = str_replace('{:描述}',self::_parserEncodeRule('{关键词}',self::$config['seo_option']['tdk_encode'],'description',',{随机句子1:70}'),self::$template);

                    break;
                case '2':
                    //self::$template = str_replace('{:描述}','<a编码>{关键词句子}</a编码>',self::$template);
                    self::$template = str_replace('{:描述}',self::_parserEncodeRule('{关键词句子}',self::$config['seo_option']['tdk_encode'],'description',),self::$template);

                    break;
                case '3':
                    //self::$template = str_replace('{:描述}','<a编码>{关键词}</a编码>',self::$template);
                    self::$template = str_replace('{:描述}',self::_parserEncodeRule('{关键词}',self::$config['seo_option']['tdk_encode'],'description',),self::$template);

                    break;
                case '4':
                    //self::$template = str_replace('{:描述}','<a编码>{随机关键词1}-{随机关键词2}-{随机关键词3}-{随机关键词4}-{随机关键词5}-{随机关键词6}-{随机关键词7}</a编码>',self::$template);
                    self::$template = str_replace('{:描述}',self::_parserEncodeRule('{随机关键词1}-{随机关键词2}-{随机关键词3}-{随机关键词4}-{随机关键词5}-{随机关键词6}-{随机关键词7}',self::$config['seo_option']['tdk_encode'],'title',),self::$template);

                    break;
                case '5':
                    //self::$template = str_replace('{:描述}','<a编码>{本地描述}</a编码>',self::$template);
                    self::$template = str_replace('{:描述}',self::_parserEncodeRule('{本地描述}',self::$config['seo_option']['tdk_encode'],'description',),self::$template);

                    break;
            }
        }
    }

    /**
     * 获取配置文件
     * @param string $name
     * @return string
     */
    protected static function get_profile(string $name = ''): string
    {
         return rm_eol(getTextByline(self::$config['seo_option']['profile_path'],$name));
    }



    /** 获取图片链接
     * @param $pic_name
     * @param $limit
     * @return array|false|string|void
     */
    protected static function get_pic($pic_name = '',$limit = 0){
            if(is_file(OUT_PATH . 'data/txt/pic/' . $pic_name)){
                $faz = OUT_PATH . 'data/txt/pic/' . $pic_name;
                $file_count = getCountLine($faz);
                if($limit > $file_count){
                    return false;
                }

                $startLine = mt_rand(1,$file_count - $limit);
                $endLine = $startLine + ($limit - 1);

                return getFileLines($faz,$startLine,$endLine,'rb');
            }
    }
}