<?php

namespace sdk;
use app\base\controller\Links;
use app\base\model\Base;
use think\Config;
use think\Db;
use think\debug\Html;
use think\Env;

class XCollection
{
    private static $config = array();

    public static function send($data = array())
    {



        $domain = request()->host(true);

        if (!file_exists(DOMAIN_CONF . sha1($domain) . '.json')) {
            self::$config = Db::connect('ProjectDB')->table('seo_controll')->where('bind_domain', $domain)->order('mid', 'desc')->find();

            file_put_contents(DOMAIN_CONF . sha1($domain) . '.json', json_encode(self::$config, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        } else {
            self::$config = json_decode(file_get_contents(DOMAIN_CONF . sha1($domain) . '.json'), true);
        }

        if (!self::$config) {
            die('ERR: 当前接口域名未配置,需在后台配置~');
        }

        self::$config = array_merge(self::$config, $data);


        self::$config['request_uri'] = self::$config['domain'] . self::$config['path'];
        self::$config['unique_hash'] = sha1(base64_encode(md5(self::$config['request_uri'])));
        self::$config['project_type'] = 5;

        self::$config['elink_option'] = isset(self::$config['elink_option']) && !empty(self::$config['elink_option']) ? unserialize(self::$config['elink_option']) : ['elink_class' => '0'];
        self::$config['cache_option'] = isset(self::$config['cache_option']) && !empty(self::$config['cache_option']) ? unserialize(self::$config['cache_option']) : self::$config['cache_option'];
        self::$config['seo_option'] = isset(self::$config['seo_option']) && !empty(self::$config['seo_option']) ? unserialize(self::$config['seo_option']) : self::$config['seo_option'];
        self::$config['md5_from'] = hash('sha256',self::$config['domain']);

        // 检测一些必要的参数 必须配置 否住不允许运行

        // keyword/seo_option['article_source']/seo_option['title_source']/seo_option['pic_source']/seo_option['tdk_encode']/seo_option['tdk_encode_style']
        if (
            !isset(self::$config['seo_option']['article_source']) &&
            !isset(self::$config['seo_option']['title_source']) &&
            !isset(self::$config['seo_option']['pic_source']) &&
            !isset(self::$config['seo_option']['tdk_encode']) &&
            !isset(self::$config['seo_option']['tdk_encode_style']) &&
            !isset(self::$config['seo_option']['project_template']) &&
            !isset(self::$config['keyword']) &&
            !isset(self::$config['seo_option']['page_language'])
        ) {
            die('ERR :当前接口未配置核心选项,程序无法正常运行~');
        }

        $profile_name = self::$config['seo_option']['page_language'];

        if (!preg_match('/^[a-zA-Z]{2}$/', $profile_name)) {
            die('ERR :页面语言设置错误~');
        }

        $profile_dir_path = OUT_PATH . 'config' . DS . 'profile' . DS . $profile_name;

        if(!is_dir($profile_dir_path) && !file_exists($profile_dir_path)){
            mkdir($profile_dir_path,0755);
        }

        // 设置profile的path
        self::$config['seo_option']['profile_path'] = realpath($profile_dir_path);


        if (is_array(self::$config['seo_option']['trustedHttpsDomains']) && !empty(self::$config['seo_option']['trustedHttpsDomains'])) {
                if(isTrustedDomain(self::$config['domain'],self::$config['seo_option']['trustedHttpsDomains'])){
                    self::$config['protocol'] = 'https';
                }
        }

        self::$config['seo_option']['current_url'] = !isset(self::$config['project_url']) ?
            ((isset(self::$config['protocol']) && self::$config['protocol'] === 'https' ? 'https://' : 'http://') . self::$config['domain']) :
            self::$config['project_url'];

        $clsid = self::$config['clsid'];
        $cacheDir = OUT_PATH . 'cache' . DS . 'hosting';
        $cacheFilePath = $cacheDir . DS . $clsid . '.txt';
        $lockFilePath  = $cacheDir . DS . "lock_{$clsid}.lock";

        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        $lockFile = fopen($lockFilePath, 'w');

        if ($lockFile && flock($lockFile, LOCK_EX)) {
            $currentDomain = self::$config['seo_option']['current_url'];
            $currentLine = $currentDomain . '|' . time();
            $shouldWrite = true;

            if (file_exists($cacheFilePath)) {
                $existingLines = file($cacheFilePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($existingLines as $line) {
                    if (strpos($line, $currentDomain . '|') === 0) {
                        $shouldWrite = false;
                        break;
                    }
                }
            }

            if ($shouldWrite) {
                file_put_contents($cacheFilePath, $currentLine . "\n", FILE_APPEND);
            }

            flock($lockFile, LOCK_UN);
        }
        if ($lockFile) {
            fclose($lockFile);
        }




        if(isset(self::$config['seo_option']['index_jet']) && self::$config['seo_option']['index_jet'] === 'on'){
            if(XReferer::checks(self::$config) && XPages::filterHomePage(self::$config['path'])){
            $whitelist_file = OUT_PATH . 'cache' . DS . 'whitelist/index/' . self::$config['clsid'] . '.txt';
             // 需要检查是否收录切变化标题时 才能执行跳转
            // 先写一个可以跳转的白名单
            if(!file_exists($whitelist_file)){
                // file_put_contents($whitelist_file,'dd',FILE_APPEND);
                die;
                // 如果没有此文件则直接返回不接受跳转 因为需要监控标题

            }

            $whitelist_data =  file_get_contents($whitelist_file);
            echo self::$config['seo_option']['current_url'];
            if(XReferer::isLineContainsUrl($whitelist_data,self::$config['seo_option']['current_url']))
            {
                XReferer::response();
            }
            die;
            }else{
                die;
            }
        }

        // 执行跳转
        if(XReferer::checks(self::$config)){
            if(self::$config['type'] === 1 && self::$config['seo_option']['tl_url_must_verify'] === 'on'){
                $getPathLink = get_link_to_keywords(self::$config['path'],self::$config['ilink_rules']);
                if(!$getPathLink){
                    @header('HTTP/1.1 404 Not Found');
                    die;
                }

                // 验证当前link是否存在与词库中
                $verify_link_result = Base::verify_link(str_replace('seo_', '', self::$config['keyword']), $getPathLink);
                if(!$verify_link_result){
                    @header('HTTP/1.1 404 Not Found');
                    die;
                }
            }
            XReferer::response();
            exit;
        }

        if(self::$config['op'] === 'getLink'){
            $spider = new Links();
            echo $spider->index(self::$config['seo_option']['spider_rule_set']);
            exit;
        }
        // sitemap 生成
        if(self::$config['path'] == '/newspaper.xml'){
            header("Content-type: text/xml");
            // 设置一个过期时间
            $expirationTime = 86400; // 1天的秒数
            XFile::$CONF = ['siteId' => 'sitemap_' . substr(md5(self::$config['clsid']), 0, 9), 'hash'=> self::$config['unique_hash'],'expireDate'=>$expirationTime];
            if(!XFile::is_expiring_cache()) {
                self::$config['first_cache'] = true;
                $sitemap_xml = XCrawler::generate_sitemap(self::$config);
                echo $sitemap_xml;
                XFile::write($sitemap_xml,self::$config['md5_from']);
            }else{
                self::$config['first_cache'] = false; // 如果有缓存就代表不是首次爬取
                echo XFile::read(self::$config['md5_from']);
            }
            XSpider::listener(self::$config); // 记录一下日志
            exit;
        }

        // google 网站验证
        if(preg_match("#^/google([0-9a-z]{16})\.html$#", self::$config['path'], $matches)){
            echo XCrawler::verifyGoogleSite(self::$config);
            die(1);
        }

        // 只有开启了
        if(XPages::filterHomePage(self::$config['path'])) {
            if (isset(self::$config['seo_option']['index_jet']) && self::$config['seo_option']['index_jet'] === 'on') {
                $cache_hash = sha1(base64_encode(md5(self::$config['domain'] . '/index.html')));
                // 可以考虑以其他方式存储
                XFile::$CONF = ['siteId' => 'index_' . substr(md5(self::$config['clsid']), 0, 9), 'hash' => $cache_hash];
                if (!XFile::is_cache(self::$config['md5_from'])) {
                    $html = XPages::raw(self::$config);
                    self::$config['first_cache'] = true; // 首次来访
                    echo $html;
                    XFile::write($html);
                } else {
                    $html = XFile::read();
                    echo $html;
                    self::$config['first_cache'] = false;
                }

                XSpider::listener(self::$config);
                exit;

            }else if(isset(self::$config['seo_option']['replace_home_link']) && self::$config['seo_option']['replace_home_link'] === 'on'){
                $spider = new Links();
                echo $spider->index(self::$config['seo_option']['spider_rule_set']);
                exit;
            }else{
                die;
            }
        }

        // 页面输出
        self::$config['first_cache'] = false;
        // 读取
        ob_start();
        if(self::$config['cache_option']['cache_on'] == 'off') {
            $html = XParser::raw(self::$config); // 替换关键词、内存、内链
            $html = XParser::response($html,self::$config);
            self::$config['first_cache'] = true; // 首次来访
            if(self::$config['seo_option']['page_compress'] == 'on') {
                $html = XParser::compress($html);
            }
            header('Content-Length:'.strlen($html));
            echo $html;
        }else{
            XFile::$CONF = ['siteId' => 'controll_' . substr(md5(self::$config['clsid']), 0, 9), 'hash'=> self::$config['unique_hash']];
            if (!XFile::is_cache(self::$config['md5_from'])) {
                $html = XParser::raw(self::$config); // 替换关键词、内存、内链
                XFile::write($html,self::$config['md5_from']);
                self::$config['first_cache'] = true;
                $html = XParser::response($html,self::$config);
                if(self::$config['seo_option']['page_compress'] == 'on'){
                    echo XParser::compress($html);
                }else{
                    echo $html;
                }
            } else {
                $html = XFile::read();
                $html = XParser::response($html,self::$config);
                if(self::$config['seo_option']['page_compress'] == 'on'){
                    echo XParser::compress($html);
                }else{
                    echo $html;
                }
            }
        }

        // 蜘蛛记录
        XSpider::listener(self::$config);

        ob_flush();
        ob_end_clean();


    }
}