<?php
/**
 *  重写蜘蛛报告 采用队列后台入库
 */

namespace sdk;


use think\Config;
use think\Env;

class XSpider
{
    private static $spider = null;
    private static $spider_type = 0;
    private static $project_type = ['泛目录','动态寄生虫','Global','URL重写','404劫持','Controll接口'];

    /**
     *  监听器 判断是否蜘蛛爬行 及入库
     * @param array $config 包含spider及request_url的数组
     */
    public static function listener(array $config = array()){


        $config['spider'] = rawurldecode($config['spider']);
        if(preg_match('/(sogouspider|baiduspider|sogou web inst|sogou web spider|360spider|yisouspider|googlebot|bingbot|coccocbot|yeti|yetibot|yandexbot)/i',$config['spider'])){
            // 搜狗
            if (preg_match("/.*(sogou|sogou|sogo).*/i", $config['spider'])) {
                self::$spider = "SogouSpider";
                self::$spider_type = 2;
            }
            // 百度
            if (preg_match("/.*(baid|aid).*/i", $config['spider'])) {
                self::$spider = "BaiduSpider";
                self::$spider_type = 1;
            }
            // 360
            if (preg_match("/.*(360).*/i", $config['spider'])) {
                self::$spider = "360Spider";
                self::$spider_type = 3;
            }
            //神马
            if (preg_match("/.*(yisou|sm).*/i", $config['spider'])) {
                self::$spider = "YiSouSpider";
                self::$spider_type = 6;
            }

            // Google
            if (preg_match("/.*(googlebot|google).*/i", $config['spider'])) {
                self::$spider = "GoogleBot";
                self::$spider_type = 5;
            }

            // Bing
            if (preg_match("/.*(bingbot|bing).*/i", $config['spider'])) {
                self::$spider = "BingBot";
                self::$spider_type = 4;
            }

            // Naver
            if (preg_match("/.*(yeti|yetibot).*/i", $config['spider'])) {
                self::$spider = "YetiBot";
                self::$spider_type = 9;
            }

            // Yandex
            if (preg_match("/.*(yandex|yandexbot).*/i", $config['spider'])) {
                self::$spider = "YandexBot";
                self::$spider_type = 7;
            }

            // cococobot
            if (preg_match("/.*(coccocbot).*/i", $config['spider'])) {
                self::$spider = "CocoCobotBot";
                self::$spider_type = 8;
            }
            // $url = !isset($config['project_url']) ? 'http://'. $config['domain'] : $config['project_url'];
            $request_uri = $config['seo_option']['current_url'] . $config['path'];

            $JsonData = [
                'spider_url'=> $request_uri,
                'spider_domain'=> $config['domain'],
                'spider_cached' => $config['unique_hash'],
                'spider_types' => self::$spider_type,
                'spider_times' => time(),
                'spider_first' => $config['first_cache'] ? 1 : 0,
                'ip_source' => $config['ipaddr'] ?? null,
                'types'=> $config['project_type'] == 5 ? '接口(' . $config['remark'] . ')' : '项目(' . self::$project_type[$config['project_type']] . ')'
                ];

            $redis = new \Redis();
            $redis->pconnect(Config::get('cache.host'),Config::get('cache.port'));
            if ('' != Config::get('cache.password')) {
                $redis->auth(Config::get('cache.password'));
            }
            $redis->select(1);
            $redis->rPush(Config::get('cache.prefix') . 'spider_queue', json_encode($JsonData)); // 插入队列数据

            //return $PushResult;
        }
    }
}