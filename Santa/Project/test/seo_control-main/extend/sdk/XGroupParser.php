<?php

namespace sdk;

use think\Config;

class XGroupParser
{
    private static array $config = [];
    private static string $template = '';
    private static string $keyword_form = ''; // 关键词来源表
    public static function parse($config = array()){
        self::$config = $config;
        // 获取模板
        if (!isset(self::$config['category_template']) && empty(self::$config['category_template'])) {
            die('模板未设置');
        }

        if (!file_exists(Config::get('tpl_dir') . self::$config['category_template'])) {
            die('模板不存在');
        }

    }
}