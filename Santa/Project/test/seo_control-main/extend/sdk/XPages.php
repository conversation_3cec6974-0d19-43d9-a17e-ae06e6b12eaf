<?php

namespace sdk;

use think\Config;
use think\Exception;

class XPages
{
    private static array $config = array(); //配置

    private static string $template = '';

    public static function raw($config = array())
    {
        self::$config = $config;

        self::$template = file_get_contents(realpath(Config::get('tpl_dir') . 'index' . DS .'index_pgslot.html'));


        self::$template = str_replace('{当前URL}', $config['seo_option']['current_url'] . '/',self::$template);
        self::$template = str_replace('{格式化时间}',getFormatDate($config['seo_option']['page_language']),self::$template);
        self::$template = str_replace('{域名}',strtoupper($config['domain']),self::$template);


        if(preg_match_all("/<ue编码>(.*?)<\/ue编码\>/",self::$template,$ue_coding)){
            foreach($ue_coding[0] as $item){
                $items = str_replace(array('<ue编码>','</ue编码>'),'',$item);
                self::$template = str_replace($item,rawurlencode($items),self::$template);
            }
        }

        if (preg_match_all("/<压缩JSON>(.*?)<\/压缩JSON>/s", self::$template, $matches)) {
            foreach ($matches[1] as $rawJson) {
                try {
                    // 压缩 JSON
                    $compressedJson = self::compressJson($rawJson);
                    // 替换模板中的压缩内容
                    self::$template = str_replace("<压缩JSON>$rawJson</压缩JSON>", $compressedJson, self::$template);
                } catch (Exception $e) {
                    echo 'Error: ' . $e->getMessage();
                }
            }
        }


        return self::$template;

    }

    private static function compressJson(string $json = ''): string {
        // 检查输入是否是有效的 JSON 字符串
        $decoded = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON string provided.');
        }

        // 重新编码为紧凑的 JSON 格式
        return json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    public static function filterHomePage($inputUrl): bool
    {
        // 检查输入是否为有效的非空字符串
        if (empty($inputUrl)) {
            return false;
        }

        // 定义常见的首页文件名（包括常见的后缀）
        $homePageFiles = [
            '/index.html', '/index.htm', '/index.php', '/index.shtml', '/index.asp',
            '/default.html', '/default.htm', '/home.html', '/home.htm', '/welcome.html',
            '/index.aspx', '/index.cfm', '/index.cgi', '/index.pl', '/index.phtml',
            '/index.jsp', '/index.js', '/default.aspx', '/default.php',
            '/home.asp', '/home.php', '/default.asp', '/index.shtml','/'
        ];

        // 遍历每个可能的首页文件并检查输入 URL 是否匹配
        foreach ($homePageFiles as $file) {
            if (strcmp($inputUrl, $file) === 0) {
                return true;
            }
        }

        return false;
    }
}