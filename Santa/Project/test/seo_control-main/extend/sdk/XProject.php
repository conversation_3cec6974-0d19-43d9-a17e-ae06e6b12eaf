<?php


namespace sdk;


use think\Db;

class XProject
{
    private static $config = array();


    public static function send($data = array()){
        if(!file_exists(SITE_CONF . $data['siteid'] . '.json')){
            self::$config = Db::connect('ProjectDB')->table('seo_project')->where('siteid',$data['siteid'])->order('id','desc')->find();
            file_put_contents(SITE_CONF . $data['siteid'] . '.json',json_encode(self::$config,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        }else{
            self::$config = json_decode(file_get_contents(SITE_CONF . $data['siteid'] . '.json'),true);
        }

        self::$config = array_merge(self::$config,$data);
        self::$config['project_url'] = substr(self::$config['project_url'],strlen(self::$config['project_url']) - 1,strlen(self::$config['project_url'])) === '/' ? substr(self::$config['project_url'],0,strlen(self::$config['project_url']) - 1) : self::$config['project_url'];
        self::$config['request_uri'] = self::$config['domain'] . self::$config['path'];
        self::$config['unique_hash'] = sha1(base64_encode(md5(self::$config['request_uri'])));
        self::$config['cache_option'] = isset(self::$config['cache_option']) && !empty(self::$config['cache_option']) ? unserialize(self::$config['cache_option']) : self::$config['cache_option'];
        self::$config['seo_option'] = isset(self::$config['seo_option']) && !empty(self::$config['seo_option']) ? unserialize(self::$config['seo_option']) : self::$config['seo_option'];
        self::$config['elink_option'] = isset(self::$config['elink_option']) && !empty(self::$config['elink_option']) ? unserialize(self::$config['elink_option']) : ['elink_class'=>'0'];


        if (
            !isset(self::$config['seo_option']['article_source']) &&
            !isset(self::$config['seo_option']['title_source']) &&
            !isset(self::$config['seo_option']['pic_source']) &&
            !isset(self::$config['seo_option']['tdk_encode']) &&
            !isset(self::$config['seo_option']['tdk_encode_style']) &&
            !isset(self::$config['seo_option']['project_template']) &&
            !isset(self::$config['keyword']) &&
            !isset(self::$config['seo_option']['page_language'])
        ) {
            die('ERR :当前项目未配置核心选项,程序无法正常运行~');
        }
        $profile_name = self::$config['seo_option']['page_language'];

        if (!preg_match('/^[a-zA-Z]{2}$/', $profile_name)) {
            die('ERR :页面语言设置错误~');
        }

        $profile_dir_path = OUT_PATH . 'config' . DS . 'profile' . DS . $profile_name;

        if(!is_dir($profile_dir_path) && !file_exists($profile_dir_path)){
            mkdir($profile_dir_path,0755);
        }

        // 设置profile的path
        self::$config['seo_option']['profile_path'] = realpath($profile_dir_path);




        // 跳转
        if(XReferer::checks(self::$config)){
            XReferer::response();
            exit;
        }




        ob_start();
        self::$config['first_cache'] = false;
        if(self::$config['cache_option']['cache_on'] == 'off') {
            $html = XParser::raw(self::$config); // 替换关键词、内存、内链
            $html = XParser::response($html,self::$config);
            self::$config['first_cache'] = true; // 首次来访
            if(self::$config['seo_option']['page_compress'] == 'on') {
                $html = XParser::compress($html);
            }
            echo $html;
        }else{
            XFile::$CONF = ['siteId' => self::$config['siteid'], 'hash'=> self::$config['unique_hash']];
            if (!XFile::is_cache()) {
                $html = XParser::raw(self::$config); // 替换关键词、内存、内链
                XFile::write($html);
                self::$config['first_cache'] = true;
                $html = XParser::response($html,self::$config);
                if(self::$config['seo_option']['page_compress'] == 'on'){
                    echo XParser::compress($html);
                }else{
                    echo $html;
                }
            } else {
                $html = XFile::read();
                $html = XParser::response($html,self::$config);
                if(self::$config['seo_option']['page_compress'] == 'on'){
                    echo XParser::compress($html);
                }else{
                    echo $html;
                }
            }
        }

        ob_flush();
        ob_end_clean();

        // 蜘蛛记录
        XSpider::listener(self::$config);

    }
}