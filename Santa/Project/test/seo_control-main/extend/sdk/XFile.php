<?php


namespace sdk;
use MongoDB\BSON\Binary;

class XFile
{

    public static array $CONF = array();

    public static function write($contents = '',$from = ''){
        if($contents !== ''){
            $contents = self::encodeBinary($contents);
            $data = [
                'hash' => self::$CONF['hash'],
                'source' => $contents,
                'time'  => time(),
                'from'  => $from
            ];
            $input = XContainer::creates(self::$CONF['siteId'],$data);
            if($input !== 0){
                return true;
            }else{
                exit('写入缓存失败!!');
            }
        }
    }

    public static function read(){
        $file = self::decodeBinary(XContainer::get(self::$CONF['siteId'],self::$CONF['hash']));
        return $file;
    }

    public static function is_cache($form = ''): bool
    {
        return XContainer::exits(self::$CONF['siteId'],self::$CONF['hash'],$form);
    }

    public static function is_expiring_cache(): bool
    {
        return XContainer::getExpiringHash(self::$CONF['siteId'],self::$CONF['hash'],self::$CONF['expireDate']);
    }

    // 加密
    protected static function encode($string): string
    {
        return base64_encode(zlib_encode($string,ZLIB_ENCODING_GZIP));

    }
    // 解密
    protected static function decode($string){
        return zlib_decode(base64_decode($string));
    }


    protected static function encodeBinary(string $string): Binary
    {
        $compressed = zlib_encode($string, ZLIB_ENCODING_GZIP);
        return new Binary($compressed, Binary::TYPE_GENERIC);
    }

    protected static function decodeBinary(Binary $binary): string
    {
        return zlib_decode($binary->getData());
    }




}