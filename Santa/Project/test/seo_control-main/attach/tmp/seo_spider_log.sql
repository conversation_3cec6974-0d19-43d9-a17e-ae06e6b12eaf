CREATE TABLE `{__表名__}` (
                            `pid` bigint(19) NOT NULL AUTO_INCREMENT,
                            `spider_domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                            `spider_types` tinyint(1) NOT NULL,
                            `spider_url` TEXT  CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                            `spider_cached` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                            `spider_times` int(11) NOT NULL,
                            `types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                            `spider_first` tinyint(1) NOT NULL,
                            `ip_source` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                            `hour` int(2) AS (HOUR(FROM_UNIXTIME(spider_times))) STORED, -- 新增生成列：存储小时
                            PRIMARY KEY (`pid`) USING BTREE,
                            INDEX `spider_types` (`spider_types`) USING BTREE,
                            INDEX `spider_times` (`spider_times`) USING BTREE,
                            INDEX `spider_domain` (`spider_domain`) USING BTREE,
                            INDEX `spider_cached` (`spider_cached`) USING BTREE,
                            INDEX `types` (`types`) USING BTREE,
                            INDEX `spider_first` (`spider_first`) USING BTREE,
                            INDEX `hour` (`hour`) USING BTREE  -- 新增索引：针对生成列 `hour`
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC;