# -*- coding: utf-8 -*-
import sys
import os
import re
import requests
import lxml
from pyquery import PyQuery as pq
import time
import random
import datetime
import sqlite3

# 文件存储目录
g_content = "data/contents"
g_title = "data/title"
g_image = "data/image"
g_max_page = 800
# 全局变量
g_urls = []


def get_ua():
    user_agent = []
    with open('user-agnet.txt','r') as fp:
        user_agent = [_.strip() for _ in fp.readlines()]  # 去掉多余制表符
    fp.close()
    user = random.choice(user_agent)
    return user


#获取标题
def get_title(p=0):
    if p == 0:
        p = 1
    else :
        p = p

    # 保存文件
    # with open('temps/page.txt', 'a') as fp:
    #     fp.write(str(p))
    # fp.close()

    c_urls = []
    c_title = []
    title_urls = "http://www.techweb.com.cn/roll/list_{}.shtml"
    url = title_urls . format(p)
    headers = {
        'Host': 'www.techweb.com.cn',
        'User-Agent':get_ua()
    }
    print('正在抓取第{}页数据，请稍后...'.format(p))
    r = requests.get(url=url,headers=headers)
    r.encoding = "UTF-8"
    source = r.text
    document = pq(source)
    dd = document.find("span[class='tit'] > a").items()
    for ss in dd :
        if ss.attr('href').find('shipin') > 0:
            continue
        g_urls.append(ss.attr('href'))
        # with open('temps/urls.txt','a+',encoding='UTF-8') as fp:
        #     fp.write(ss.attr('href'))
        #     fp.write('\n')
        # fp.close()
        if not get_contents(ss.attr('href')) :
            print('{}抓取失败，跳过该条后自动休眠4秒...'.format(ss.attr('href')))
            time.sleep(4)
            continue
        nows = datetime.datetime.now()
        filename = 'title-' + str(nows.year) +  str(nows.month) + str(nows.day) + str(nows.hour) + '.txt'
        with open('temps/' + filename, 'a+',encoding='UTF-8') as fps:
            fps.write(ss.attr('title'))
            fps.write('\n')
        fps.close()
        c_title.append(ss.attr('title'))

        # 获取当前页码
    page = int(document.find("ul[class='inline'] > li > a[class='now']").text())
    if page <= g_max_page:
        next_page = int(page) + 1
        print('正在转到第{}页，请稍后...' .format(next_page))
        time.sleep(1)
        get_title(next_page)
    else:
        print('全部抓取完毕！')
    return 0
#获取内容
def get_contents(url= ''):
    print('正在抓取{}，请稍后...' . format(url))
    r = requests.Session()
    headers = {
             'Host': 'www.techweb.com.cn',
             'User-Agent':get_ua()
         }
    try:
        q = r.get(url=url,headers=headers,timeout=8)
    except:
        return False
    q.encoding = 'UTF-8'
    source = q.text
    document = pq(source)
    fq = document.find("div[class='main_c'] > #content")
    nows = datetime.datetime.now()
    filename = 'content-' + str(nows.year) +  str(nows.month) + str(nows.day) + str(nows.hour) + str(nows.minute) + '.txt'
    with open('temps/' + filename, 'a+', encoding='UTF-8') as fps:
        text_s = fq.text()
        fps.write(text_s.replace('TechWeb','本站消息'))
        fps.write('\n')
        fps.close()

    print('抓取完毕{}，正在进入下一条...'.format(url))
    return True

def get_lists():

    return 0

def next_pages():

    return 0


get_title()

