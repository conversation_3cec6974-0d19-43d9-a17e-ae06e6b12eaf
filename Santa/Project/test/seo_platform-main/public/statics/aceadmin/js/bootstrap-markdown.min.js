/* ===================================================
 * bootstrap-markdown.js v2.10.0
 * http://github.com/toopay/bootstrap-markdown
 * ===================================================
 * Copyright 2013-2016 Taufan Aditya
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ========================================================== */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a("object"==typeof exports?require("jquery"):jQuery)}(function(a){"use strict";var b=function(b,c){var d=["autofocus","savable","hideable","width","height","resize","iconlibrary","language","footer","fullscreen","hiddenButtons","disabledButtons"];a.each(d,function(d,e){"undefined"!=typeof a(b).data(e)&&(c="object"==typeof c?c:{},c[e]=a(b).data(e))}),this.$ns="bootstrap-markdown",this.$element=a(b),this.$editable={el:null,type:null,attrKeys:[],attrValues:[],content:null},this.$options=a.extend(!0,{},a.fn.markdown.defaults,c,this.$element.data("options")),this.$oldContent=null,this.$isPreview=!1,this.$isFullscreen=!1,this.$editor=null,this.$textarea=null,this.$handler=[],this.$callback=[],this.$nextTab=[],this.showEditor()};b.prototype={constructor:b,__alterButtons:function(b,c){var d=this.$handler,e="all"==b,f=this;a.each(d,function(a,d){var g=!0;g=e?!1:d.indexOf(b)<0,g===!1&&c(f.$editor.find('button[data-handler="'+d+'"]'))})},__buildButtons:function(b,c){var d,e=this.$ns,f=this.$handler,g=this.$callback;for(d=0;d<b.length;d++){var h,i=b[d];for(h=0;h<i.length;h++){var j,k=i[h].data,l=a("<div/>",{"class":"btn-group"});for(j=0;j<k.length;j++){var m,n,o=k[j],p=e+"-"+o.name,q=this.__getIcon(o.icon),r=o.btnText?o.btnText:"",s=o.btnClass?o.btnClass:"btn",t=o.tabIndex?o.tabIndex:"-1",u="undefined"!=typeof o.hotkey?o.hotkey:"",v="undefined"!=typeof jQuery.hotkeys&&""!==u?" ("+u+")":"";m=a("<button></button>"),m.text(" "+this.__localize(r)).addClass("btn-default btn-sm").addClass(s),s.match(/btn\-(primary|success|info|warning|danger|link)/)&&m.removeClass("btn-default"),m.attr({type:"button",title:this.__localize(o.title)+v,tabindex:t,"data-provider":e,"data-handler":p,"data-hotkey":u}),o.toggle===!0&&m.attr("data-toggle","button"),n=a("<span/>"),n.addClass(q),n.prependTo(m),l.append(m),f.push(p),g.push(o.callback)}c.append(l)}}return c},__setListener:function(){var b="undefined"!=typeof this.$textarea.attr("rows"),c=this.$textarea.val().split("\n").length>5?this.$textarea.val().split("\n").length:"5",d=b?this.$textarea.attr("rows"):c;this.$textarea.attr("rows",d),this.$options.resize&&this.$textarea.css("resize",this.$options.resize),this.$textarea.on({focus:a.proxy(this.focus,this),keyup:a.proxy(this.keyup,this),change:a.proxy(this.change,this),select:a.proxy(this.select,this)}),this.eventSupported("keydown")&&this.$textarea.on("keydown",a.proxy(this.keydown,this)),this.eventSupported("keypress")&&this.$textarea.on("keypress",a.proxy(this.keypress,this)),this.$textarea.data("markdown",this)},__handle:function(b){var c=a(b.currentTarget),d=this.$handler,e=this.$callback,f=c.attr("data-handler"),g=d.indexOf(f),h=e[g];a(b.currentTarget).focus(),h(this),this.change(this),f.indexOf("cmdSave")<0&&this.$textarea.focus(),b.preventDefault()},__localize:function(b){var c=a.fn.markdown.messages,d=this.$options.language;return"undefined"!=typeof c&&"undefined"!=typeof c[d]&&"undefined"!=typeof c[d][b]?c[d][b]:b},__getIcon:function(a){return"object"==typeof a?a[this.$options.iconlibrary]:a},setFullscreen:function(b){var c=this.$editor,d=this.$textarea;b===!0?(c.addClass("md-fullscreen-mode"),a("body").addClass("md-nooverflow"),this.$options.onFullscreen(this)):(c.removeClass("md-fullscreen-mode"),a("body").removeClass("md-nooverflow"),1==this.$isPreview&&this.hidePreview().showPreview()),this.$isFullscreen=b,d.focus()},showEditor:function(){var b,c=this,d=this.$ns,e=this.$element,f=(e.css("height"),e.css("width"),this.$editable),g=this.$handler,h=this.$callback,i=this.$options,j=a("<div/>",{"class":"md-editor",click:function(){c.focus()}});if(null===this.$editor){var k=a("<div/>",{"class":"md-header btn-toolbar"}),l=[];if(i.buttons.length>0&&(l=l.concat(i.buttons[0])),i.additionalButtons.length>0&&a.each(i.additionalButtons[0],function(b,c){var d=a.grep(l,function(a,b){return a.name===c.name});d.length>0?d[0].data=d[0].data.concat(c.data):l.push(i.additionalButtons[0][b])}),i.reorderButtonGroups.length>0&&(l=l.filter(function(a){return i.reorderButtonGroups.indexOf(a.name)>-1}).sort(function(a,b){return i.reorderButtonGroups.indexOf(a.name)<i.reorderButtonGroups.indexOf(b.name)?-1:i.reorderButtonGroups.indexOf(a.name)>i.reorderButtonGroups.indexOf(b.name)?1:0})),l.length>0&&(k=this.__buildButtons([l],k)),i.fullscreen.enable&&k.append('<div class="md-controls"><a class="md-control md-control-fullscreen" href="#"><span class="'+this.__getIcon(i.fullscreen.icons.fullscreenOn)+'"></span></a></div>').on("click",".md-control-fullscreen",function(a){a.preventDefault(),c.setFullscreen(!0)}),j.append(k),e.is("textarea"))e.before(j),b=e,b.addClass("md-input"),j.append(b);else{var m="function"==typeof toMarkdown?toMarkdown(e.html()):e.html(),n=a.trim(m);b=a("<textarea/>",{"class":"md-input",val:n}),j.append(b),f.el=e,f.type=e.prop("tagName").toLowerCase(),f.content=e.html(),a(e[0].attributes).each(function(){f.attrKeys.push(this.nodeName),f.attrValues.push(this.nodeValue)}),e.replaceWith(j)}var o=a("<div/>",{"class":"md-footer"}),p=!1,q="";if(i.savable){p=!0;var r="cmdSave";g.push(r),h.push(i.onSave),o.append('<button class="btn btn-success" data-provider="'+d+'" data-handler="'+r+'"><i class="icon icon-white icon-ok"></i> '+this.__localize("Save")+"</button>")}if(q="function"==typeof i.footer?i.footer(this):i.footer,""!==a.trim(q)&&(p=!0,o.append(q)),p&&j.append(o),i.width&&"inherit"!==i.width&&(jQuery.isNumeric(i.width)?(j.css("display","table"),b.css("width",i.width+"px")):j.addClass(i.width)),i.height&&"inherit"!==i.height)if(jQuery.isNumeric(i.height)){var s=i.height;k&&(s=Math.max(0,s-k.outerHeight())),o&&(s=Math.max(0,s-o.outerHeight())),b.css("height",s+"px")}else j.addClass(i.height);this.$editor=j,this.$textarea=b,this.$editable=f,this.$oldContent=this.getContent(),this.__setListener(),this.$editor.attr("id",(new Date).getTime()),this.$editor.on("click",'[data-provider="bootstrap-markdown"]',a.proxy(this.__handle,this)),(this.$element.is(":disabled")||this.$element.is("[readonly]"))&&(this.$editor.addClass("md-editor-disabled"),this.disableButtons("all")),this.eventSupported("keydown")&&"object"==typeof jQuery.hotkeys&&k.find('[data-provider="bootstrap-markdown"]').each(function(){var c=a(this),d=c.attr("data-hotkey");""!==d.toLowerCase()&&b.bind("keydown",d,function(){return c.trigger("click"),!1})}),"preview"===i.initialstate?this.showPreview():"fullscreen"===i.initialstate&&i.fullscreen.enable&&this.setFullscreen(!0)}else this.$editor.show();return i.autofocus&&(this.$textarea.focus(),this.$editor.addClass("active")),i.fullscreen.enable&&i.fullscreen!==!1&&(this.$editor.append('<div class="md-fullscreen-controls"><a href="#" class="exit-fullscreen" title="Exit fullscreen"><span class="'+this.__getIcon(i.fullscreen.icons.fullscreenOff)+'"></span></a></div>'),this.$editor.on("click",".exit-fullscreen",function(a){a.preventDefault(),c.setFullscreen(!1)})),this.hideButtons(i.hiddenButtons),this.disableButtons(i.disabledButtons),i.onShow(this),this},parseContent:function(a){var b,a=a||this.$textarea.val();return b=this.$options.parser?this.$options.parser(a):"object"==typeof markdown?markdown.toHTML(a):"function"==typeof marked?marked(a):a},showPreview:function(){var b,c,d=this.$options,e=this.$textarea,f=e.next(),g=a("<div/>",{"class":"md-preview","data-provider":"markdown-preview"});return 1==this.$isPreview?this:(this.$isPreview=!0,this.disableButtons("all").enableButtons("cmdPreview"),c=d.onPreview(this),b="string"==typeof c?c:this.parseContent(),g.html(b),f&&"md-footer"==f.attr("class")?g.insertBefore(f):e.parent().append(g),g.css({width:e.outerWidth()+"px",height:e.outerHeight()+"px"}),this.$options.resize&&g.css("resize",this.$options.resize),e.hide(),g.data("markdown",this),(this.$element.is(":disabled")||this.$element.is("[readonly]"))&&(this.$editor.addClass("md-editor-disabled"),this.disableButtons("all")),this)},hidePreview:function(){this.$isPreview=!1;var a=this.$editor.find('div[data-provider="markdown-preview"]');return a.remove(),this.enableButtons("all"),this.disableButtons(this.$options.disabledButtons),this.$textarea.show(),this.__setListener(),this},isDirty:function(){return this.$oldContent!=this.getContent()},getContent:function(){return this.$textarea.val()},setContent:function(a){return this.$textarea.val(a),this},findSelection:function(a){var b,c=this.getContent();if(b=c.indexOf(a),b>=0&&a.length>0){var d,e=this.getSelection();return this.setSelection(b,b+a.length),d=this.getSelection(),this.setSelection(e.start,e.end),d}return null},getSelection:function(){var a=this.$textarea[0];return("selectionStart"in a&&function(){var b=a.selectionEnd-a.selectionStart;return{start:a.selectionStart,end:a.selectionEnd,length:b,text:a.value.substr(a.selectionStart,b)}}||function(){return null})()},setSelection:function(a,b){var c=this.$textarea[0];return("selectionStart"in c&&function(){c.selectionStart=a,c.selectionEnd=b}||function(){return null})()},replaceSelection:function(a){var b=this.$textarea[0];return("selectionStart"in b&&function(){return b.value=b.value.substr(0,b.selectionStart)+a+b.value.substr(b.selectionEnd,b.value.length),b.selectionStart=b.value.length,this}||function(){return b.value+=a,jQuery(b)})()},getNextTab:function(){if(0===this.$nextTab.length)return null;var a,b=this.$nextTab.shift();return"function"==typeof b?a=b():"object"==typeof b&&b.length>0&&(a=b),a},setNextTab:function(a,b){if("string"==typeof a){var c=this;this.$nextTab.push(function(){return c.findSelection(a)})}else if("number"==typeof a&&"number"==typeof b){var d=this.getSelection();this.setSelection(a,b),this.$nextTab.push(this.getSelection()),this.setSelection(d.start,d.end)}},__parseButtonNameParam:function(a){return"string"==typeof a?a.split(" "):a},enableButtons:function(b){var c=this.__parseButtonNameParam(b),d=this;return a.each(c,function(a,b){d.__alterButtons(c[a],function(a){a.removeAttr("disabled")})}),this},disableButtons:function(b){var c=this.__parseButtonNameParam(b),d=this;return a.each(c,function(a,b){d.__alterButtons(c[a],function(a){a.attr("disabled","disabled")})}),this},hideButtons:function(b){var c=this.__parseButtonNameParam(b),d=this;return a.each(c,function(a,b){d.__alterButtons(c[a],function(a){a.addClass("hidden")})}),this},showButtons:function(b){var c=this.__parseButtonNameParam(b),d=this;return a.each(c,function(a,b){d.__alterButtons(c[a],function(a){a.removeClass("hidden")})}),this},eventSupported:function(a){var b=a in this.$element;return b||(this.$element.setAttribute(a,"return;"),b="function"==typeof this.$element[a]),b},keyup:function(a){var b=!1;switch(a.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 9:var c;if(c=this.getNextTab(),null!==c){var d=this;setTimeout(function(){d.setSelection(c.start,c.end)},500),b=!0}else{var e=this.getSelection();e.start==e.end&&e.end==this.getContent().length?b=!1:(this.setSelection(this.getContent().length,this.getContent().length),b=!0)}break;case 13:b=!1;break;case 27:this.$isFullscreen&&this.setFullscreen(!1),b=!1;break;default:b=!1}b&&(a.stopPropagation(),a.preventDefault()),this.$options.onChange(this)},change:function(a){return this.$options.onChange(this),this},select:function(a){return this.$options.onSelect(this),this},focus:function(b){var c=this.$options,d=(c.hideable,this.$editor);return d.addClass("active"),a(document).find(".md-editor").each(function(){if(a(this).attr("id")!==d.attr("id")){var b;b=a(this).find("textarea").data("markdown"),null===b&&(b=a(this).find('div[data-provider="markdown-preview"]').data("markdown")),b&&b.blur()}}),c.onFocus(this),this},blur:function(b){var c=this.$options,d=c.hideable,e=this.$editor,f=this.$editable;if(e.hasClass("active")||0===this.$element.parent().length){if(e.removeClass("active"),d)if(null!==f.el){var g=a("<"+f.type+"/>"),h=this.getContent(),i=this.parseContent(h);a(f.attrKeys).each(function(a,b){g.attr(f.attrKeys[a],f.attrValues[a])}),g.html(i),e.replaceWith(g)}else e.hide();c.onBlur(this)}return this}};var c=a.fn.markdown;a.fn.markdown=function(c){return this.each(function(){var d=a(this),e=d.data("markdown"),f="object"==typeof c&&c;e||d.data("markdown",e=new b(this,f))})},a.fn.markdown.messages={},a.fn.markdown.defaults={autofocus:!1,hideable:!1,savable:!1,width:"inherit",height:"inherit",resize:"none",iconlibrary:"glyph",language:"en",initialstate:"editor",parser:null,buttons:[[{name:"groupFont",data:[{name:"cmdBold",hotkey:"Ctrl+B",title:"Bold",icon:{glyph:"glyphicon glyphicon-bold",fa:"fa fa-bold","fa-3":"icon-bold"},callback:function(a){var b,c,d=a.getSelection(),e=a.getContent();b=0===d.length?a.__localize("strong text"):d.text,"**"===e.substr(d.start-2,2)&&"**"===e.substr(d.end,2)?(a.setSelection(d.start-2,d.end+2),a.replaceSelection(b),c=d.start-2):(a.replaceSelection("**"+b+"**"),c=d.start+2),a.setSelection(c,c+b.length)}},{name:"cmdItalic",title:"Italic",hotkey:"Ctrl+I",icon:{glyph:"glyphicon glyphicon-italic",fa:"fa fa-italic","fa-3":"icon-italic"},callback:function(a){var b,c,d=a.getSelection(),e=a.getContent();b=0===d.length?a.__localize("emphasized text"):d.text,"_"===e.substr(d.start-1,1)&&"_"===e.substr(d.end,1)?(a.setSelection(d.start-1,d.end+1),a.replaceSelection(b),c=d.start-1):(a.replaceSelection("_"+b+"_"),c=d.start+1),a.setSelection(c,c+b.length)}},{name:"cmdHeading",title:"Heading",hotkey:"Ctrl+H",icon:{glyph:"glyphicon glyphicon-header",fa:"fa fa-header","fa-3":"icon-font"},callback:function(a){var b,c,d,e,f=a.getSelection(),g=a.getContent();b=0===f.length?a.__localize("heading text"):f.text+"\n",d=4,"### "===g.substr(f.start-d,d)||(d=3,"###"===g.substr(f.start-d,d))?(a.setSelection(f.start-d,f.end),a.replaceSelection(b),c=f.start-d):f.start>0&&(e=g.substr(f.start-1,1),!!e&&"\n"!=e)?(a.replaceSelection("\n\n### "+b),c=f.start+6):(a.replaceSelection("### "+b),c=f.start+4),a.setSelection(c,c+b.length)}}]},{name:"groupLink",data:[{name:"cmdUrl",title:"URL/Link",hotkey:"Ctrl+L",icon:{glyph:"glyphicon glyphicon-link",fa:"fa fa-link","fa-3":"icon-link"},callback:function(b){var c,d,e,f=b.getSelection();b.getContent();c=0===f.length?b.__localize("enter link description here"):f.text,e=prompt(b.__localize("Insert Hyperlink"),"http://");var g=new RegExp("^((http|https)://|(mailto:)|(//))[a-z0-9]","i");if(null!==e&&""!==e&&"http://"!==e&&g.test(e)){var h=a("<div>"+e+"</div>").text();b.replaceSelection("["+c+"]("+h+")"),d=f.start+1,b.setSelection(d,d+c.length)}}},{name:"cmdImage",title:"Image",hotkey:"Ctrl+G",icon:{glyph:"glyphicon glyphicon-picture",fa:"fa fa-picture-o","fa-3":"icon-picture"},callback:function(b){var c,d,e,f=b.getSelection();b.getContent();c=0===f.length?b.__localize("enter image description here"):f.text,e=prompt(b.__localize("Insert Image Hyperlink"),"http://");var g=new RegExp("^((http|https)://|(//))[a-z0-9]","i");if(null!==e&&""!==e&&"http://"!==e&&g.test(e)){var h=a("<div>"+e+"</div>").text();b.replaceSelection("!["+c+"]("+h+' "'+b.__localize("enter image title here")+'")'),d=f.start+2,b.setNextTab(b.__localize("enter image title here")),b.setSelection(d,d+c.length)}}}]},{name:"groupMisc",data:[{name:"cmdList",hotkey:"Ctrl+U",title:"Unordered List",icon:{glyph:"glyphicon glyphicon-list",fa:"fa fa-list","fa-3":"icon-list-ul"},callback:function(b){var c,d,e=b.getSelection();b.getContent();if(0===e.length)c=b.__localize("list text here"),b.replaceSelection("- "+c),d=e.start+2;else if(e.text.indexOf("\n")<0)c=e.text,b.replaceSelection("- "+c),d=e.start+2;else{var f=[];f=e.text.split("\n"),c=f[0],a.each(f,function(a,b){f[a]="- "+b}),b.replaceSelection("\n\n"+f.join("\n")),d=e.start+4}b.setSelection(d,d+c.length)}},{name:"cmdListO",hotkey:"Ctrl+O",title:"Ordered List",icon:{glyph:"glyphicon glyphicon-th-list",fa:"fa fa-list-ol","fa-3":"icon-list-ol"},callback:function(b){var c,d,e=b.getSelection();b.getContent();if(0===e.length)c=b.__localize("list text here"),b.replaceSelection("1. "+c),d=e.start+3;else if(e.text.indexOf("\n")<0)c=e.text,b.replaceSelection("1. "+c),d=e.start+3;else{var f=[];f=e.text.split("\n"),c=f[0],a.each(f,function(a,b){f[a]="1. "+b}),b.replaceSelection("\n\n"+f.join("\n")),d=e.start+5}b.setSelection(d,d+c.length)}},{name:"cmdCode",hotkey:"Ctrl+K",title:"Code",icon:{glyph:"glyphicon glyphicon-asterisk",fa:"fa fa-code","fa-3":"icon-code"},callback:function(a){var b,c,d=a.getSelection(),e=a.getContent();b=0===d.length?a.__localize("code text here"):d.text,"```\n"===e.substr(d.start-4,4)&&"\n```"===e.substr(d.end,4)?(a.setSelection(d.start-4,d.end+4),a.replaceSelection(b),c=d.start-4):"`"===e.substr(d.start-1,1)&&"`"===e.substr(d.end,1)?(a.setSelection(d.start-1,d.end+1),a.replaceSelection(b),c=d.start-1):e.indexOf("\n")>-1?(a.replaceSelection("```\n"+b+"\n```"),c=d.start+4):(a.replaceSelection("`"+b+"`"),c=d.start+1),a.setSelection(c,c+b.length)}},{name:"cmdQuote",hotkey:"Ctrl+Q",title:"Quote",icon:{glyph:"glyphicon glyphicon-comment",fa:"fa fa-quote-left","fa-3":"icon-quote-left"},callback:function(b){var c,d,e=b.getSelection();b.getContent();if(0===e.length)c=b.__localize("quote here"),b.replaceSelection("> "+c),d=e.start+2;else if(e.text.indexOf("\n")<0)c=e.text,b.replaceSelection("> "+c),d=e.start+2;else{var f=[];f=e.text.split("\n"),c=f[0],a.each(f,function(a,b){f[a]="> "+b}),b.replaceSelection("\n\n"+f.join("\n")),d=e.start+4}b.setSelection(d,d+c.length)}}]},{name:"groupUtil",data:[{name:"cmdPreview",toggle:!0,hotkey:"Ctrl+P",title:"Preview",btnText:"Preview",btnClass:"btn btn-primary btn-sm",icon:{glyph:"glyphicon glyphicon-search",fa:"fa fa-search","fa-3":"icon-search"},callback:function(a){var b=a.$isPreview;b===!1?a.showPreview():a.hidePreview()}}]}]],additionalButtons:[],reorderButtonGroups:[],hiddenButtons:[],disabledButtons:[],footer:"",fullscreen:{enable:!0,icons:{fullscreenOn:{fa:"fa fa-expand",glyph:"glyphicon glyphicon-fullscreen","fa-3":"icon-resize-full"},fullscreenOff:{fa:"fa fa-compress",glyph:"glyphicon glyphicon-fullscreen","fa-3":"icon-resize-small"}}},onShow:function(a){},onPreview:function(a){},onSave:function(a){},onBlur:function(a){},onFocus:function(a){},onChange:function(a){},onFullscreen:function(a){},onSelect:function(a){}},a.fn.markdown.Constructor=b,a.fn.markdown.noConflict=function(){return a.fn.markdown=c,this};var d=function(a){var b=a;return b.data("markdown")?void b.data("markdown").showEditor():void b.markdown()},e=function(b){var c=a(document.activeElement);a(document).find(".md-editor").each(function(){var b=a(this),d=c.closest(".md-editor")[0]===this,e=b.find("textarea").data("markdown")||b.find('div[data-provider="markdown-preview"]').data("markdown");e&&!d&&e.blur()})};a(document).on("click.markdown.data-api",'[data-provide="markdown-editable"]',function(b){d(a(this)),b.preventDefault()}).on("click focusin",function(a){e(a)}).ready(function(){a('textarea[data-provide="markdown"]').each(function(){d(a(this))})})});