/*demo样式*/
#picker {
	display: inline-block;
	line-height: 1.428571429;
	vertical-align: middle;
	margin: 0 12px 0 0;
}

#picker .webuploader-pick {
	padding: 6px 12px;
	display: block;
}

#uploader-demo .thumbnail {
	width: 110px;
	height: 110px;
	position: relative;
}

#uploader-demo .thumbnail img {
	width: 100%;
}

#uploader-demo .thumbnail .file-panel {
	background: rgba(0, 0, 0, 0.6) none repeat scroll 0 0;
	top: 0px;
	color: white;
	font-size: 12px;
	height: 20px;
	left: 0px;
	line-height: 20px;
	overflow: hidden;
	position: absolute;
	text-indent: 5px;
	text-overflow: ellipsis;
	white-space: nowrap;
	z-index: 10;
}

.uploader-list {
	width: 100%;
	overflow: hidden;
}

.file-item {
	float: left;
	position: relative;
	margin: 0 2px 2px 0;
	padding: 0px;
}

.file-item .error {
	position: absolute;
	top: 4px;
	left: 4px;
	right: 4px;
	background: red;
	color: white;
	text-align: center;
	height: 20px;
	font-size: 14px;
	line-height: 23px;
}

.file-item .info {
	position: absolute;
	left: 4px;
	bottom: 4px;
	right: 4px;
	height: 20px;
	line-height: 20px;
	text-indent: 5px;
	background: rgba(0, 0, 0, 0.6);
	color: white;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 12px;
	z-index: 10;
}

.upload-state-done:after {
	content: "\f00c";
	font-family: FontAwesome;
	font-style: normal;
	font-weight: normal;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 32px;
	position: absolute;
	bottom: 0;
	right: 4px;
	color: #4cae4c;
	z-index: 99;
}

.file-item .progress {
	position: absolute;
	right: 4px;
	bottom: 4px;
	height: 3px;
	left: 4px;
	height: 4px;
	overflow: hidden;
	z-index: 15;
	margin: 0;
	padding: 0;
	border-radius: 0;
	background: transparent;
}

.file-item .progress span {
	display: block;
	overflow: hidden;
	width: 0;
	height: 100%;
	background: #d14 url(../images/progress.png) repeat-x;
	-webit-transition: width 200ms linear;
	-moz-transition: width 200ms linear;
	-o-transition: width 200ms linear;
	-ms-transition: width 200ms linear;
	transition: width 200ms linear;
	-webkit-animation: progressmove 2s linear infinite;
	-moz-animation: progressmove 2s linear infinite;
	-o-animation: progressmove 2s linear infinite;
	-ms-animation: progressmove 2s linear infinite;
	animation: progressmove 2s linear infinite;
	-webkit-transform: translateZ(0);
}

@
-webkit-keyframes progressmove { 0% {
	background-position: 0 0;
}

100%
{
background-position
:
 
17
px
 
0;
}
}
@
-moz-keyframes progressmove { 0% {
	background-position: 0 0;
}

100%
{
background-position
:
 
17
px
 
0;
}
}
@
keyframes progressmove { 0% {
	background-position: 0 0;
}

100%
{
background-position
:
 
17
px
 
0;
}
}
a.travis {
	position: relative;
	top: -4px;
	right: 15px;
}