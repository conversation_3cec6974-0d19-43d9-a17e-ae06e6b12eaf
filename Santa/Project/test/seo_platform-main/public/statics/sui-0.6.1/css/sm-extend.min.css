/*!
 * =====================================================
 * SUI Mobile - http://m.sui.taobao.org/
 *
 * =====================================================
 */
.photo-browser {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 10500
}

.photo-browser .bar-tab .tab-item .icon {
	width: 14px;
	height: 14px;
	margin-top: -5px
}

.photo-browser .bar-tab ~.photo-browser-captions {
	bottom: 52px;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.photo-browser.photo-browser-in {
	display: block;
	-webkit-animation: photoBrowserIn .4s forwards;
	animation: photoBrowserIn .4s forwards
}

.photo-browser.photo-browser-out {
	display: block;
	-webkit-animation: photoBrowserOut .4s forwards;
	animation: photoBrowserOut .4s forwards
}

html.with-statusbar-overlay .photo-browser {
	height: -webkit-calc(100% - 1rem);
	height: calc(100% - 1rem);
	top: 1rem
}

.popup>.photo-browser .navbar, .popup>.photo-browser .toolbar, body>.photo-browser .navbar,
	body>.photo-browser .toolbar {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.photo-browser .page[data-page=photo-browser-slides] {
	background: 0 0
}

.photo-browser .page {
	box-sizing: border-box;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: #efeff4
}

.photo-browser .view {
	overflow: hidden;
	box-sizing: border-box;
	position: relative;
	width: 100%;
	height: 100%;
	z-index: 5000
}

.page[data-page=photo-browser-slides] .toolbar a {
	color: #4cd964
}

.photo-browser-popup {
	background: 0 0
}

.photo-browser .navbar, .photo-browser .toolbar, .view[data-page=photo-browser-slides] .navbar,
	.view[data-page=photo-browser-slides] .toolbar {
	background: rgba(247, 247, 247, .95);
	-webkit-transition: .4s;
	transition: .4s
}

.view[data-page=photo-browser-slides] .page[data-page=photo-browser-slides] .navbar,
	.view[data-page=photo-browser-slides] .page[data-page=photo-browser-slides] .toolbar
	{
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.photo-browser-exposed .navbar, .photo-browser-exposed .toolbar {
	opacity: 0;
	visibility: hidden;
	pointer-events: none
}

.photo-browser-exposed .photo-browser-swiper-container {
	background: #000
}

.photo-browser-of {
	margin: 0 .25rem
}

.photo-browser-captions {
	pointer-events: none;
	position: absolute;
	left: 0;
	width: 100%;
	bottom: 0;
	z-index: 10;
	opacity: 1;
	-webkit-transition: .4s;
	transition: .4s
}

.photo-browser-captions.photo-browser-captions-exposed {
	opacity: 0
}

.toolbar ~.photo-browser-captions {
	bottom: 2.2rem;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.photo-browser-exposed .toolbar ~.photo-browser-captions {
	-webkit-transform: translate3d(0, 2.2rem, 0);
	transform: translate3d(0, 2.2rem, 0)
}

.toolbar ~.photo-browser-captions.photo-browser-captions-exposed {
	transformt: ranslate3d(0, 0, 0)
}

.photo-browser-caption {
	box-sizing: border-box;
	-webkit-transition: .3s;
	transition: .3s;
	position: absolute;
	bottom: 0;
	left: 0;
	opacity: 0;
	padding: .2rem .25px;
	width: 100%;
	text-align: center;
	color: #fff;
	background: rgba(0, 0, 0, .8)
}

.photo-browser-caption:empty {
	display: none
}

.photo-browser-caption.photo-browser-caption-active {
	opacity: 1
}

.photo-browser-captions-light .photo-browser-caption {
	background: rgba(255, 255, 255, .8);
	color: #3d4145
}

.photo-browser-exposed .photo-browser-caption {
	color: #fff;
	background: rgba(0, 0, 0, .8)
}

.photo-browser-swiper-container {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: #fff;
	-webkit-transition: .4s;
	transition: .4s
}

.photo-browser-swiper-wrapper {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	padding: 0;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex
}

.photo-browser-link-inactive {
	opacity: .3
}

.photo-browser-slide {
	width: 100%;
	height: 100%;
	position: relative;
	overflow: hidden;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	-webkit-flex-shrink: 0;
	flex-shrink: 0;
	box-sizing: border-box
}

.photo-browser-slide.transitioning {
	-webkit-transition: .4s;
	transition: .4s
}

.photo-browser-slide span.photo-browser-zoom-container {
	width: 100%;
	text-align: center;
	display: none
}

.photo-browser-slide img {
	width: auto;
	height: auto;
	max-width: 100%;
	max-height: 100%;
	display: none
}

.photo-browser-slide.swiper-slide-active span.photo-browser-zoom-container,
	.photo-browser-slide.swiper-slide-next span.photo-browser-zoom-container,
	.photo-browser-slide.swiper-slide-prev span.photo-browser-zoom-container
	{
	display: block
}

.photo-browser-slide.swiper-slide-active img, .photo-browser-slide.swiper-slide-next img,
	.photo-browser-slide.swiper-slide-prev img {
	display: inline
}

.photo-browser-slide.swiper-slide-active.photo-browser-slide-lazy .preloader,
	.photo-browser-slide.swiper-slide-next.photo-browser-slide-lazy .preloader,
	.photo-browser-slide.swiper-slide-prev.photo-browser-slide-lazy .preloader
	{
	display: block
}

.photo-browser-slide iframe {
	width: 100%;
	height: 100%
}

.photo-browser-slide .preloader {
	display: none;
	position: absolute;
	width: 2.1rem;
	height: 2.1rem;
	margin-left: -2.1rem;
	margin-top: -2.1rem;
	left: 50%;
	top: 50%
}

.photo-browser-dark .navbar, .photo-browser-dark .toolbar {
	background: rgba(30, 30, 30, .8);
	color: #fff
}

.photo-browser-dark .navbar:before, .photo-browser-dark .toolbar:before
	{
	display: none
}

.photo-browser-dark .navbar:after, .photo-browser-dark .toolbar:after {
	display: none
}

.photo-browser-dark .navbar a, .photo-browser-dark .toolbar a {
	color: #fff
}

.photo-browser-dark .photo-browser-swiper-container {
	background: #000
}

@
-webkit-keyframes photoBrowserIn { 0%{
	-webkit-transform: translate3d(0, 0, 0) scale(.5);
	transform: translate3d(0, 0, 0) scale(.5);
	opacity: 0
}

100%{
-webkit-transform
:translate3d
(0
,
0,0)
scale
(1);transform
:translate3d
(0
,
0,0)
scale
(1);opacity
:
1
}
}
@
keyframes photoBrowserIn { 0%{
	-webkit-transform: translate3d(0, 0, 0) scale(.5);
	transform: translate3d(0, 0, 0) scale(.5);
	opacity: 0
}

100%{
-webkit-transform
:translate3d
(0
,
0,0)
scale
(1);transform
:translate3d
(0
,
0,0)
scale
(1);opacity
:
1
}
}
@
-webkit-keyframes photoBrowserOut { 0%{
	-webkit-transform: translate3d(0, 0, 0) scale(1);
	transform: translate3d(0, 0, 0) scale(1);
	opacity: 1
}

100%{
-webkit-transform
:translate3d
(0
,
0,0)
scale
(
.5
);transform
:translate3d
(0
,
0,0)
scale
(
.5
);opacity
:
0
}
}
@
keyframes photoBrowserOut { 0%{
	-webkit-transform: translate3d(0, 0, 0) scale(1);
	transform: translate3d(0, 0, 0) scale(1);
	opacity: 1
}

100%{
-webkit-transform
:translate3d
(0
,
0,0)
scale
(
.5
);transform
:translate3d
(0
,
0,0)
scale
(
.5
);opacity
:
0
}
}
.swiper-container {
	margin: 0 auto;
	position: relative;
	overflow: hidden;
	padding-bottom: 30px;
	z-index: 1
}

.swiper-container-no-flexbox .swiper-slide {
	float: left
}

.swiper-container-vertical>.swiper-wrapper {
	-webkit-box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column
}

.swiper-wrapper {
	position: relative;
	width: 100%;
	height: 100%;
	z-index: 1;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-transform-style: preserve-3d;
	-ms-transform-style: preserve-3d;
	transform-style: preserve-3d;
	-webkit-transition-property: -webkit-transform;
	transition-property: transform;
	box-sizing: content-box
}

.swiper-container-android .swiper-slide, .swiper-wrapper {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.swiper-container-multirow>.swiper-wrapper {
	-webkit-box-lines: multiple;
	-moz-box-lines: multiple;
	-ms-fles-wrap: wrap;
	-webkit-flex-wrap: wrap;
	flex-wrap: wrap
}

.swiper-container-free-mode>.swiper-wrapper {
	-webkit-transition-timing-function: ease-out;
	transition-timing-function: ease-out;
	margin: 0 auto
}

.swiper-slide {
	-webkit-transform-style: preserve-3d;
	-ms-transform-style: preserve-3d;
	transform-style: preserve-3d;
	-webkit-flex-shrink: 0;
	-ms-flex: 0 0 auto;
	-webkit-flex-shrink: 0;
	flex-shrink: 0;
	width: 100%;
	height: 100%;
	position: relative
}

.swiper-container .swiper-notification {
	position: absolute;
	left: 0;
	top: 0;
	pointer-events: none;
	opacity: 0;
	z-index: -1000
}

.swiper-wp8-horizontal {
	touch-action: pan-y
}

.swiper-wp8-vertical {
	touch-action: pan-x
}

.swiper-button-next, .swiper-button-prev {
	position: absolute;
	top: 50%;
	width: 27px;
	height: 44px;
	margin-top: -22px;
	z-index: 10;
	cursor: pointer;
	background-size: 27px 44px;
	background-position: center;
	background-repeat: no-repeat
}

.swiper-button-next.swiper-button-disabled, .swiper-button-prev.swiper-button-disabled
	{
	opacity: .35;
	cursor: auto;
	pointer-events: none
}

.swiper-button-prev, .swiper-container-rtl .swiper-button-next {
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
	left: 10px;
	right: auto
}

.swiper-button-next, .swiper-container-rtl .swiper-button-prev {
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
	right: 10px;
	left: auto
}

.swiper-pagination {
	position: absolute;
	text-align: center;
	-webkit-transition: .3s;
	transition: .3s;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
	opacity: 0
}

.swiper-pagination-bullet {
	width: 8px;
	height: 8px;
	display: inline-block;
	border-radius: 100%;
	background: #000;
	opacity: .2
}

.swiper-pagination-bullet-active {
	opacity: 1;
	background: #007aff
}

.swiper-container-vertical>.swiper-pagination {
	right: 10px;
	top: 50%;
	-webkit-transform: translate3d(0, -50%, 0);
	transform: translate3d(0, -50%, 0)
}

.swiper-container-vertical>.swiper-pagination .swiper-pagination-bullet
	{
	margin: 5px 0;
	display: block
}

.swiper-container-horizontal>.swiper-pagination {
	bottom: 10px;
	left: 0;
	width: 100%
}

.swiper-container-horizontal>.swiper-pagination .swiper-pagination-bullet
	{
	margin: 0 5px
}

.swiper-container-3d {
	-webkit-perspective: 1200px;
	-o-perspective: 1200px;
	perspective: 1200px
}

.swiper-container-3d .swiper-cube-shadow, .swiper-container-3d .swiper-slide,
	.swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left,
	.swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top,
	.swiper-container-3d .swiper-wrapper {
	-webkit-transform-style: preserve-3d;
	-ms-transform-style: preserve-3d;
	transform-style: preserve-3d
}

.swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left,
	.swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top
	{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 10
}

.swiper-container-3d .swiper-slide-shadow-left {
	background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .5)),
		to(rgba(0, 0, 0, 0)));
	background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0));
	background-image: linear-gradient(to left, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-right {
	background-image: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, .5)),
		to(rgba(0, 0, 0, 0)));
	background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0));
	background-image: linear-gradient(to right, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-top {
	background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, .5)),
		to(rgba(0, 0, 0, 0)));
	background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0));
	background-image: linear-gradient(to top, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-bottom {
	background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, .5)),
		to(rgba(0, 0, 0, 0)));
	background-image: -webkit-linear-gradient(top, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0));
	background-image: linear-gradient(to bottom, rgba(0, 0, 0, .5),
		rgba(0, 0, 0, 0))
}

.swiper-container-coverflow .swiper-wrapper {
	-ms-perspective: 1200px
}

.swiper-container-fade.swiper-container-free-mode .swiper-slide {
	-webkit-transition-timing-function: ease-out;
	transition-timing-function: ease-out
}

.swiper-container-fade .swiper-slide {
	pointer-events: none
}

.swiper-container-fade .swiper-slide-active {
	pointer-events: auto
}

.swiper-container-cube {
	overflow: visible
}

.swiper-container-cube .swiper-slide {
	pointer-events: none;
	visibility: hidden;
	-webkit-transform-origin: 0 0;
	transform-origin: 0 0;
	-webkit-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	backface-visibility: hidden;
	width: 100%;
	height: 100%
}

.swiper-container-cube.swiper-container-rtl .swiper-slide {
	-webkit-transform-origin: 100% 0;
	transform-origin: 100% 0
}

.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-next,
	.swiper-container-cube .swiper-slide-next+.swiper-slide,
	.swiper-container-cube .swiper-slide-prev {
	pointer-events: auto;
	visibility: visible
}

.swiper-container-cube .swiper-cube-shadow {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	background: #000;
	opacity: .6;
	-webkit-filter: blur(50px);
	filter: blur(50px)
}

.swiper-container-cube.swiper-container-vertical .swiper-cube-shadow {
	z-index: 0
}

.swiper-scrollbar {
	border-radius: 10px;
	position: relative;
	-ms-touch-action: none;
	background: rgba(0, 0, 0, .1)
}

.swiper-container-horizontal>.swiper-scrollbar {
	position: absolute;
	left: 1%;
	bottom: 3px;
	z-index: 50;
	height: 5px;
	width: 98%
}

.swiper-container-vertical>.swiper-scrollbar {
	position: absolute;
	right: 3px;
	top: 1%;
	z-index: 50;
	width: 5px;
	height: 98%
}

.swiper-scrollbar-drag {
	height: 100%;
	width: 100%;
	position: relative;
	background: rgba(0, 0, 0, .5);
	border-radius: 10px;
	left: 0;
	top: 0
}

.swiper-scrollbar-cursor-drag {
	cursor: move
}

.swiper-slide .preloader {
	width: 42px;
	height: 42px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -21px;
	margin-top: -21px;
	z-index: 10
}

.swiper-slide img {
	display: block
}