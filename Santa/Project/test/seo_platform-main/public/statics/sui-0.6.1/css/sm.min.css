/*!
 * =====================================================
 * SUI Mobile - http://m.sui.taobao.org/
 *
 * =====================================================
 */
html {
	font-size: 20px
}

@media only screen and (min-width:400px) {
	html {
		font-size: 21.33px !important
	}
}

@media only screen and (min-width:414px) {
	html {
		font-size: 22.08px !important
	}
}

@media only screen and (min-width:480px) {
	html {
		font-size: 25.6px !important
	}
}
	/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
	font-family: sans-serif;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%
}

body {
	margin: 0
}

article, aside, details, figcaption, figure, footer, header, hgroup,
	main, menu, nav, section, summary {
	display: block
}

audio, canvas, progress, video {
	display: inline-block;
	vertical-align: baseline
}

audio:not ([controls] ){
	display: none;
	height: 0
}

[hidden], template {
	display: none
}

a {
	background-color: transparent
}

a:active, a:hover {
	outline: 0
}

abbr[title] {
	border-bottom: 1px dotted
}

b, strong {
	font-weight: 700
}

dfn {
	font-style: italic
}

h1 {
	font-size: 2em;
	margin: .67em 0
}

mark {
	background: #ff0;
	color: #000
}

small {
	font-size: 80%
}

sub, sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline
}

sup {
	top: -.5em
}

sub {
	bottom: -.25em
}

img {
	border: 0
}

svg:not (:root ){
	overflow: hidden
}

figure {
	margin: 1em 40px
}

hr {
	box-sizing: content-box;
	height: 0
}

pre {
	overflow: auto
}

code, kbd, pre, samp {
	font-family: monospace, monospace;
	font-size: 1em
}

button, input, optgroup, select, textarea {
	color: inherit;
	font: inherit;
	margin: 0
}

button {
	overflow: visible
}

button, select {
	text-transform: none
}

button, html input[type=button], input[type=reset], input[type=submit] {
	-webkit-appearance: button;
	cursor: pointer
}

button[disabled], html input[disabled] {
	cursor: default
}

button::-moz-focus-inner, input::-moz-focus-inner {
	border: 0;
	padding: 0
}

input {
	line-height: normal
}

input[type=checkbox], input[type=radio] {
	box-sizing: border-box;
	padding: 0
}

input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button
	{
	height: auto
}

input[type=search] {
	-webkit-appearance: textfield;
	box-sizing: content-box
}

input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration
	{
	-webkit-appearance: none
}

fieldset {
	border: 1px solid silver;
	margin: 0 2px;
	padding: .35em .625em .75em
}

legend {
	border: 0;
	padding: 0
}

textarea {
	overflow: auto
}

optgroup {
	font-weight: 700
}

table {
	border-collapse: collapse;
	border-spacing: 0
}

td, th {
	padding: 0
}

* {
	box-sizing: border-box;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none
}

body {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	font-family: "Helvetica Neue", Helvetica, sans-serif;
	font-size: .85rem;
	line-height: 1.5;
	color: #3d4145;
	background: #eee;
	overflow: hidden
}

a, button, input, select, textarea {
	outline: 0
}

p {
	margin: 1em 0
}

a {
	color: #0894ec;
	text-decoration: none;
	-webkit-tap-highlight-color: transparent
}

a:active {
	color: #0a8ddf
}

.page {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: #eee;
	z-index: 2000
}

.content {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: auto;
	-webkit-overflow-scrolling: touch
}

.bar-nav ~.content {
	top: 2.2rem
}

.bar-header-secondary ~.content {
	top: 4.4rem
}

.bar-footer ~.content {
	bottom: 2.2rem
}

.bar-footer-secondary ~.content {
	bottom: 4.4rem
}

.bar-tab ~.content {
	bottom: 2.5rem
}

.bar-footer-secondary-tab ~.content {
	bottom: 4.7rem
}

.content-padded {
	margin: .5rem
}

.text-center {
	text-align: center
}

.pull-left {
	float: left
}

.pull-right {
	float: right
}

.clearfix:after, .clearfix:before {
	content: " ";
	display: table
}

.clearfix:after {
	clear: both
}

.content-block {
	margin: 1.75rem 0;
	padding: 0 .75rem;
	color: #6d6d72
}

.content-block-title {
	position: relative;
	overflow: hidden;
	margin: 0;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: .7rem;
	text-transform: uppercase;
	line-height: 1;
	color: #6d6d72;
	margin: 1.75rem .75rem .5rem
}

.content-block-title+.card, .content-block-title+.content-block,
	.content-block-title+.list-block {
	margin-top: .5rem
}

.content-block-inner {
	background: #fff;
	padding: .5rem .75rem;
	margin-left: -.75rem;
	width: 100%;
	position: relative;
	color: #3d4145
}

.content-block-inner:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #c8c7cc;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.content-block-inner:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.content-block-inner:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.content-block-inner:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #c8c7cc;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.content-block-inner:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.content-block-inner:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.content-block.inset {
	margin-left: .75rem;
	margin-right: .75rem;
	border-radius: .35rem
}

.content-block.inset .content-block-inner {
	border-radius: .35rem
}

.content-block.inset .content-block-inner:before {
	display: none
}

.content-block.inset .content-block-inner:after {
	display: none
}

@media all and (min-width:768px) {
	.content-block.tablet-inset {
		margin-left: .75rem;
		margin-right: .75rem;
		border-radius: .35rem
	}
	.content-block.tablet-inset .content-block-inner {
		border-radius: .35rem
	}
	.content-block.tablet-inset .content-block-inner:before {
		display: none
	}
	.content-block.tablet-inset .content-block-inner:after {
		display: none
	}
}

.row {
	overflow: hidden;
	margin-left: -4%
}

.row>[class*=col-], .row>[class*=tablet-] {
	box-sizing: border-box;
	float: left
}

.row.no-gutter {
	margin-left: 0
}

.row .col-100 {
	width: 96%;
	margin-left: 4%
}

.row.no-gutter .col-100 {
	width: 100%;
	margin: 0
}

.row .col-95 {
	width: 91%;
	margin-left: 4%
}

.row.no-gutter .col-95 {
	width: 95%;
	margin: 0
}

.row .col-90 {
	width: 86%;
	margin-left: 4%
}

.row.no-gutter .col-90 {
	width: 90%;
	margin: 0
}

.row .col-85 {
	width: 81%;
	margin-left: 4%
}

.row.no-gutter .col-85 {
	width: 85%;
	margin: 0
}

.row .col-80 {
	width: 76%;
	margin-left: 4%
}

.row.no-gutter .col-80 {
	width: 80%;
	margin: 0
}

.row .col-75 {
	width: 71.00000000000001%;
	margin-left: 4%
}

.row.no-gutter .col-75 {
	width: 75%;
	margin: 0
}

.row .col-66 {
	width: 62.66666666666666%;
	margin-left: 4%
}

.row.no-gutter .col-66 {
	width: 66.66666666666666%;
	margin: 0
}

.row .col-60 {
	width: 55.99999999999999%;
	margin-left: 4%
}

.row.no-gutter .col-60 {
	width: 60%;
	margin: 0
}

.row .col-50 {
	width: 46%;
	margin-left: 4%
}

.row.no-gutter .col-50 {
	width: 50%;
	margin: 0
}

.row .col-40 {
	width: 36%;
	margin-left: 4%
}

.row.no-gutter .col-40 {
	width: 40%;
	margin: 0
}

.row .col-33 {
	width: 29.333333333333332%;
	margin-left: 4%
}

.row.no-gutter .col-33 {
	width: 33.333333333333336%;
	margin: 0
}

.row .col-25 {
	width: 21%;
	margin-left: 4%
}

.row.no-gutter .col-25 {
	width: 25%;
	margin: 0
}

.row .col-20 {
	width: 16%;
	margin-left: 4%
}

.row.no-gutter .col-20 {
	width: 20%;
	margin: 0
}

.row .col-15 {
	width: 10.999999999999998%;
	margin-left: 4%
}

.row.no-gutter .col-15 {
	width: 15%;
	margin: 0
}

.row .col-10 {
	width: 6%;
	margin-left: 4%
}

.row.no-gutter .col-10 {
	width: 10%;
	margin: 0
}

.row .col-5 {
	width: 1%;
	margin-left: 4%
}

.row.no-gutter .col-5 {
	width: 5%;
	margin: 0
}

@media all and (min-width:768px) {
	.row {
		margin-left: -2%
	}
	.row .col-100 {
		width: 98%;
		margin-left: 2%
	}
	.row.no-gutter .col-100 {
		width: 100%;
		margin: 0
	}
	.row .col-95 {
		width: 93%;
		margin-left: 2%
	}
	.row.no-gutter .col-95 {
		width: 95%;
		margin: 0
	}
	.row .col-90 {
		width: 87.99999999999999%;
		margin-left: 2%
	}
	.row.no-gutter .col-90 {
		width: 90%;
		margin: 0
	}
	.row .col-85 {
		width: 82.99999999999999%;
		margin-left: 2%
	}
	.row.no-gutter .col-85 {
		width: 85%;
		margin: 0
	}
	.row .col-80 {
		width: 78%;
		margin-left: 2%
	}
	.row.no-gutter .col-80 {
		width: 80%;
		margin: 0
	}
	.row .col-75 {
		width: 73%;
		margin-left: 2%
	}
	.row.no-gutter .col-75 {
		width: 75%;
		margin: 0
	}
	.row .col-66 {
		width: 64.66666666666666%;
		margin-left: 2%
	}
	.row.no-gutter .col-66 {
		width: 66.66666666666666%;
		margin: 0
	}
	.row .col-60 {
		width: 58%;
		margin-left: 2%
	}
	.row.no-gutter .col-60 {
		width: 60%;
		margin: 0
	}
	.row .col-50 {
		width: 48%;
		margin-left: 2%
	}
	.row.no-gutter .col-50 {
		width: 50%;
		margin: 0
	}
	.row .col-40 {
		width: 38%;
		margin-left: 2%
	}
	.row.no-gutter .col-40 {
		width: 40%;
		margin: 0
	}
	.row .col-33 {
		width: 31.333333333333332%;
		margin-left: 2%
	}
	.row.no-gutter .col-33 {
		width: 33.333333333333336%;
		margin: 0
	}
	.row .col-25 {
		width: 23%;
		margin-left: 2%
	}
	.row.no-gutter .col-25 {
		width: 25%;
		margin: 0
	}
	.row .col-20 {
		width: 18%;
		margin-left: 2%
	}
	.row.no-gutter .col-20 {
		width: 20%;
		margin: 0
	}
	.row .col-15 {
		width: 13%;
		margin-left: 2%
	}
	.row.no-gutter .col-15 {
		width: 15%;
		margin: 0
	}
	.row .col-10 {
		width: 8%;
		margin-left: 2%
	}
	.row.no-gutter .col-10 {
		width: 10%;
		margin: 0
	}
	.row .col-5 {
		width: 3%;
		margin-left: 2%
	}
	.row.no-gutter .col-5 {
		width: 5%;
		margin: 0
	}
	.row .tablet-100 {
		width: 98%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-100 {
		width: 100%;
		margin: 0
	}
	.row .tablet-95 {
		width: 93%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-95 {
		width: 95%;
		margin: 0
	}
	.row .tablet-90 {
		width: 87.99999999999999%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-90 {
		width: 90%;
		margin: 0
	}
	.row .tablet-85 {
		width: 82.99999999999999%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-85 {
		width: 85%;
		margin: 0
	}
	.row .tablet-80 {
		width: 78%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-80 {
		width: 80%;
		margin: 0
	}
	.row .tablet-75 {
		width: 73%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-75 {
		width: 75%;
		margin: 0
	}
	.row .tablet-66 {
		width: 64.66666666666666%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-66 {
		width: 66.66666666666666%;
		margin: 0
	}
	.row .tablet-60 {
		width: 58%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-60 {
		width: 60%;
		margin: 0
	}
	.row .tablet-50 {
		width: 48%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-50 {
		width: 50%;
		margin: 0
	}
	.row .tablet-40 {
		width: 38%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-40 {
		width: 40%;
		margin: 0
	}
	.row .tablet-33 {
		width: 31.333333333333332%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-33 {
		width: 33.333333333333336%;
		margin: 0
	}
	.row .tablet-25 {
		width: 23%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-25 {
		width: 25%;
		margin: 0
	}
	.row .tablet-20 {
		width: 18%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-20 {
		width: 20%;
		margin: 0
	}
	.row .tablet-15 {
		width: 13%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-15 {
		width: 15%;
		margin: 0
	}
	.row .tablet-10 {
		width: 8%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-10 {
		width: 10%;
		margin: 0
	}
	.row .tablet-5 {
		width: 3%;
		margin-left: 2%
	}
	.row.no-gutter .tablet-5 {
		width: 5%;
		margin: 0
	}
}

.color-default {
	color: #3d4145
}

.color-gray {
	color: #999
}

.color-primary {
	color: #0894ec
}

.color-success {
	color: #4cd964
}

.color-danger {
	color: #f6383a
}

.color-warning {
	color: #f60
}

.text-center {
	text-align: center
}

.bar {
	position: absolute;
	right: 0;
	left: 0;
	z-index: 10;
	height: 2.2rem;
	padding-right: .5rem;
	padding-left: .5rem;
	background-color: #f7f7f8;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden
}

.bar:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.bar:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.bar:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.bar-header-secondary {
	top: 2.2rem
}

.bar-footer {
	bottom: 0
}

.bar-footer-secondary {
	bottom: 2.2rem
}

.bar-footer-secondary-tab {
	bottom: 2.5rem
}

.bar-footer-secondary-tab:before, .bar-footer-secondary:before,
	.bar-footer:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.bar-footer-secondary-tab:before, .bar-footer-secondary:before,
		.bar-footer:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.bar-footer-secondary-tab:before, .bar-footer-secondary:before,
		.bar-footer:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.bar-footer-secondary-tab:after, .bar-footer-secondary:after,
	.bar-footer:after {
	display: none
}

.bar-nav {
	top: 0
}

.title {
	position: absolute;
	display: block;
	width: 100%;
	padding: 0;
	margin: 0 -.5rem;
	font-size: .85rem;
	font-weight: 500;
	line-height: 2.2rem;
	color: #3d4145;
	text-align: center;
	white-space: nowrap
}

.title a {
	color: inherit
}

.bar-tab {
	bottom: 0;
	width: 100%;
	height: 2.5rem;
	padding: 0;
	table-layout: fixed
}

.bar-tab:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.bar-tab:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.bar-tab:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.bar-tab:after {
	display: none
}

.bar-tab .tab-item {
	position: relative;
	display: table-cell;
	width: 1%;
	height: 2.5rem;
	color: #929292;
	text-align: center;
	vertical-align: middle
}

.bar-tab .tab-item.active, .bar-tab .tab-item:active {
	color: #0894ec
}

.bar-tab .tab-item .badge {
	position: absolute;
	top: .1rem;
	left: 50%;
	z-index: 100;
	height: .8rem;
	min-width: .8rem;
	padding: 0 .2rem;
	font-size: .6rem;
	line-height: .8rem;
	color: #fff;
	vertical-align: top;
	background: red;
	border-radius: .5rem;
	margin-left: .1rem
}

.bar-tab .tab-item .icon {
	top: .15rem;
	width: 1.2rem;
	height: 1.2rem;
	font-size: 1.2rem;
	line-height: 1.2rem;
	padding-top: 0;
	padding-bottom: 0
}

.bar-tab .tab-item .icon ~.tab-label {
	display: block;
	font-size: .55rem;
	position: relative;
	top: .15rem
}

.bar .button {
	position: relative;
	top: .35rem;
	z-index: 20;
	margin-top: 0;
	font-weight: 400
}

.bar .button.pull-right {
	margin-left: .5rem
}

.bar .button.pull-left {
	margin-right: .5rem
}

.bar .button-link {
	top: 0;
	padding: 0;
	font-size: .8rem;
	line-height: 2.2rem;
	height: 2.2rem;
	color: #0894ec;
	border: 0
}

.bar .button-link.active, .bar .button-link:active {
	color: #0675bb
}

.bar .button-block {
	top: .35rem;
	font-size: .8rem;
	width: 100%
}

.bar .button-nav.pull-left {
	margin-left: -.25rem
}

.bar .button-nav.pull-left .icon-left-nav {
	margin-right: -.15rem
}

.bar .button-nav.pull-right {
	margin-right: -.25rem
}

.bar .button-nav.pull-right .icon-right-nav {
	margin-left: -.15rem
}

.bar .icon {
	position: relative;
	z-index: 20;
	padding: .5rem .1rem;
	font-size: 1rem;
	line-height: 1.2rem
}

.bar .button .icon {
	padding: 0
}

.bar .title .icon {
	padding: 0
}

.bar .title .icon.icon-caret {
	top: .2rem;
	margin-left: -.25rem
}

.bar-footer .icon {
	font-size: 1.2rem;
	line-height: 1.2rem
}

.bar input[type=search] {
	height: 1.45rem;
	margin: .3rem 0
}

.badge {
	display: inline-block;
	padding: .1rem .45rem .15rem;
	font-size: .6rem;
	line-height: 1;
	color: #3d4145;
	background-color: rgba(0, 0, 0, .15);
	border-radius: 5rem
}

.badge.badge-inverted {
	padding: 0 .25rem 0 0;
	background-color: transparent
}

.list-block {
	margin: 1.75rem 0;
	font-size: .85rem
}

.list-block ul {
	background: #fff;
	list-style: none;
	padding: 0;
	margin: 0;
	position: relative
}

.list-block ul:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.list-block ul:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.list-block ul:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.list-block ul:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.list-block ul:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.list-block ul:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.list-block ul ul {
	padding-left: 2.25rem
}

.list-block ul ul:before {
	display: none
}

.list-block ul ul:after {
	display: none
}

.list-block .align-top, .list-block .align-top .item-content,
	.list-block .align-top .item-inner {
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	align-items: flex-start
}

.list-block.inset {
	margin-left: .75rem;
	margin-right: .75rem;
	border-radius: .35rem
}

.list-block.inset .content-block-title {
	margin-left: 0;
	margin-right: 0
}

.list-block.inset ul {
	border-radius: .35rem
}

.list-block.inset ul:before {
	display: none
}

.list-block.inset ul:after {
	display: none
}

.list-block.inset li:first-child>a {
	border-radius: .35rem .35rem 0 0
}

.list-block.inset li:last-child>a {
	border-radius: 0 0 .35rem .35rem
}

.list-block.inset li:first-child:last-child>a {
	border-radius: .35rem
}

@media all and (min-width:768px) {
	.list-block.tablet-inset {
		margin-left: .75rem;
		margin-right: .75rem;
		border-radius: .35rem
	}
	.list-block.tablet-inset .content-block-title {
		margin-left: 0;
		margin-right: 0
	}
	.list-block.tablet-inset ul {
		border-radius: .35rem
	}
	.list-block.tablet-inset ul:before {
		display: none
	}
	.list-block.tablet-inset ul:after {
		display: none
	}
	.list-block.tablet-inset li:first-child>a {
		border-radius: .35rem .35rem 0 0
	}
	.list-block.tablet-inset li:last-child>a {
		border-radius: 0 0 .35rem .35rem
	}
	.list-block.tablet-inset li:first-child:last-child>a {
		border-radius: .35rem
	}
	.list-block.tablet-inset .content-block-title {
		margin-left: 0;
		margin-right: 0
	}
	.list-block.tablet-inset ul {
		border-radius: .35rem
	}
	.list-block.tablet-inset ul:before {
		display: none
	}
	.list-block.tablet-inset ul:after {
		display: none
	}
	.list-block.tablet-inset li:first-child>a {
		border-radius: .35rem .35rem 0 0
	}
	.list-block.tablet-inset li:last-child>a {
		border-radius: 0 0 .35rem .35rem
	}
	.list-block.tablet-inset li:first-child:last-child>a {
		border-radius: .35rem
	}
}

.list-block li {
	box-sizing: border-box;
	position: relative
}

.list-block .item-media {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-shrink: 0;
	-ms-flex: 0 0 auto;
	-webkit-flex-shrink: 0;
	flex-shrink: 0;
	-webkit-box-lines: single;
	-moz-box-lines: single;
	-webkit-flex-wrap: nowrap;
	flex-wrap: nowrap;
	box-sizing: border-box;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	padding-top: .35rem;
	padding-bottom: .4rem
}

.list-block .item-media i+i {
	margin-left: .25rem
}

.list-block .item-media i+img {
	margin-left: .25rem
}

.list-block .item-media+.item-inner {
	margin-left: .75rem
}

.list-block .item-inner {
	padding-right: .75rem;
	position: relative;
	width: 100%;
	padding-top: .4rem;
	padding-bottom: .35rem;
	min-height: 2.2rem;
	overflow: hidden;
	box-sizing: border-box;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center
}

.list-block .item-inner:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.list-block .item-inner:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.list-block .item-inner:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.list-block .item-title {
	-webkit-flex-shrink: 1;
	-ms-flex: 0 1 auto;
	-webkit-flex-shrink: 1;
	flex-shrink: 1;
	white-space: nowrap;
	position: relative;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 100%
}

.list-block .item-title.label {
	width: 35%;
	-webkit-flex-shrink: 0;
	-ms-flex: 0 0 auto;
	-webkit-flex-shrink: 0;
	flex-shrink: 0;
	margin: 4px 0
}

.list-block .item-input {
	width: 100%;
	margin-top: -.4rem;
	margin-bottom: -.35rem;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	-webkit-flex-shrink: 1;
	-ms-flex: 0 1 auto;
	-webkit-flex-shrink: 1;
	flex-shrink: 1
}

.list-block .item-after {
	white-space: nowrap;
	color: #5f646e;
	-webkit-flex-shrink: 0;
	-ms-flex: 0 0 auto;
	-webkit-flex-shrink: 0;
	flex-shrink: 0;
	margin-left: .25rem;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	max-height: 1.4rem
}

.list-block .smart-select .item-after {
	max-width: 70%;
	overflow: hidden;
	text-overflow: ellipsis;
	position: relative
}

.list-block .item-link {
	-webkit-transition-duration: .3s;
	transition-duration: .3s;
	display: block;
	color: inherit
}

.list-block .item-link .item-inner {
	padding-right: 1.5rem;
	background-image:
		url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NUM0QzFDNzMyREM0MTFFNUJDNTI4OTMzMEE0RjBENzMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NUM0QzFDNzQyREM0MTFFNUJDNTI4OTMzMEE0RjBENzMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1QzRDMUM3MTJEQzQxMUU1QkM1Mjg5MzMwQTRGMEQ3MyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1QzRDMUM3MjJEQzQxMUU1QkM1Mjg5MzMwQTRGMEQ3MyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pjs2Bb4AAAItSURBVHjazJhbK0RRGIb3DIOU/AG5kUTOgxmHceFGKf6BO+Vf+E8KKYcYg3FuMpNIDhFJXJAcp/GtvKumrzVs+zBrvfU2u689q6d3rb33+lYgl8tZvymZ3JOX7eQp8gT50fJA0Wj4z3tKbY5VR14hV5ObyWLkZ6sICtq4p4V8CjihevIWucoUQJFUmtUayTvkShMAL5DiGqs3IMlK3YBSgwrIZkBWmAAoIRMKyG2/IIMO/hMjbygepCS53ARAoQHyOqu1YbrLTADMAXJbASmSDOkGlOpTQHaQN72CdAuYBeQuq4cBWaIbUEJGC0Am3UIGPVoqMsk9Vu/CwxTQDSj0iSQPWD2C6Q7oBhT6AmRKAZkwAVDoowBkn+LdqQVQ6A2QhwrIuAmAEjKi2KrF/jPdfgIKveI7Pcfq/eSMCYBSD4pakymA0+RxVrsn15oAOEMeY7Vbcif5ys4ApT7CzZJHWO2G3I1fSyfgPHmY1a7x6bvT/ZpZUMBdOoHzI8El8pCiK+wq8CQXNcFlBdw51tyD00G9SnAVHV++zgDn6hzHiwTjCrgTTKvrQya3Ca5jA5CvY3IP+UlnTxJEb8zhjpDck1cL20mCAcBFWD2D2ovOvjiERojDpTGtnsL9N8EQegt+LJrC5vRN59lMORp0DrePNH2BswvYivXVzuoHSO7dz+2QHcAa6+eMOl87WHOffm8m7QCK7foog+tFi2mZACg3npPkRUxrtkitgvUtwAA5A3LWdzPizwAAAABJRU5ErkJggg==);
	background-size: .7rem;
	background-repeat: no-repeat;
	background-position: 97% center;
	background-position: -webkit-calc(100% - .5rem) center;
	background-position: calc(100% - .5rem) center
}

.list-block .item-link.active-state, html:not (.watch-active-state ) .list-block .item-link:active
	{
	-webkit-transition-duration: 0s;
	transition-duration: 0s;
	background-color: #d9d9d9
}

.list-block .item-link.active-state .item-inner:after, html:not (.watch-active-state
	) .list-block .item-link:active .item-inner:after {
	background-color: transparent
}

.list-block .item-link.list-button {
	padding: 0 .75rem;
	text-align: center;
	color: #0894ec;
	display: block;
	line-height: 2.15rem
}

.list-block .item-link.list-button:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.list-block .item-link.list-button:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.list-block .item-link.list-button:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.list-block .item-content {
	box-sizing: border-box;
	padding-left: .75rem;
	min-height: 2.2rem;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center
}

.list-block .list-block-label {
	margin: .5rem 0 1.75rem;
	padding: 0 .75rem;
	font-size: .7rem;
	color: #5f646e
}

.list-block .swipeout {
	overflow: hidden;
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d
}

.list-block .swipeout.deleting {
	-webkit-transition-duration: .3s;
	transition-duration: .3s
}

.list-block .swipeout.deleting .swipeout-content {
	-webkit-transform: translateX(-100%);
	transform: translateX(-100%)
}

.list-block .swipeout.transitioning .swipeout-actions-left a,
	.list-block .swipeout.transitioning .swipeout-actions-right a,
	.list-block .swipeout.transitioning .swipeout-content, .list-block .swipeout.transitioning .swipeout-overswipe
	{
	-webkit-transition: .3s;
	transition: .3s
}

.list-block .swipeout-content {
	position: relative;
	z-index: 10
}

.list-block .swipeout-overswipe {
	-webkit-transition: .2s left;
	transition: .2s left
}

.list-block .swipeout-actions-left, .list-block .swipeout-actions-right
	{
	position: absolute;
	top: 0;
	height: 100%;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex
}

.list-block .swipeout-actions-left a, .list-block .swipeout-actions-right a
	{
	padding: 0 1.5rem;
	color: #fff;
	background: #c7c7cc;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	position: relative;
	left: 0
}

.list-block .swipeout-actions-left a:after, .list-block .swipeout-actions-right a:after
	{
	content: '';
	position: absolute;
	top: 0;
	width: 600%;
	height: 100%;
	background: inherit;
	z-index: -1
}

.list-block .swipeout-actions-left a.swipeout-delete, .list-block .swipeout-actions-right a.swipeout-delete
	{
	background: #f6383a
}

.list-block .swipeout-actions-right {
	right: 0;
	-webkit-transform: translateX(100%);
	transform: translateX(100%)
}

.list-block .swipeout-actions-right a:after {
	left: 100%;
	margin-left: -1px
}

.list-block .swipeout-actions-left {
	left: 0;
	-webkit-transform: translateX(-100%);
	transform: translateX(-100%)
}

.list-block .swipeout-actions-left a:after {
	right: 100%;
	margin-right: -1px
}

.list-block .item-subtitle {
	font-size: .75rem;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	max-width: 100%;
	text-overflow: ellipsis
}

.list-block .item-text {
	font-size: .75rem;
	color: #5f646e;
	line-height: 1.05rem;
	position: relative;
	overflow: hidden;
	height: 2.1rem;
	text-overflow: ellipsis;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -webkit-box
}

.list-block li.media-item .item-title, .list-block.media-list .item-title
	{
	font-weight: 500
}

.list-block li.media-item .item-inner, .list-block.media-list .item-inner
	{
	display: block;
	padding-top: .5rem;
	padding-bottom: .45rem;
	-webkit-align-self: stretch;
	align-self: stretch
}

.list-block li.media-item .item-media, .list-block.media-list .item-media
	{
	padding-top: .45rem;
	padding-bottom: .5rem
}

.list-block li.media-item .item-media img, .list-block.media-list .item-media img
	{
	display: block
}

.list-block li.media-item .item-title-row, .list-block.media-list .item-title-row
	{
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between
}

.list-block .list-group ul:after, .list-block .list-group ul:before {
	z-index: 11
}

.list-block .list-group+.list-group ul:before {
	display: none
}

.list-block .item-divider, .list-block .list-group-title {
	background: #f7f7f7;
	margin-top: -1px;
	padding: .2rem .75rem;
	white-space: nowrap;
	position: relative;
	max-width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
	color: #e7e7e7
}

.list-block .item-divider:before, .list-block .list-group-title:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.list-block .item-divider:before, .list-block .list-group-title:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.list-block .item-divider:before, .list-block .list-group-title:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.list-block .list-group-title {
	position: relative;
	position: -webkit-sticky;
	position: -moz-sticky;
	position: sticky;
	top: 0;
	z-index: 20;
	margin-top: 0
}

.list-block .list-group-title:before {
	display: none
}

.list-block li:last-child .list-button:after {
	display: none
}

.list-block li:last-child .item-inner:after, .list-block li:last-child li:last-child .item-inner:after
	{
	display: none
}

.list-block li li:last-child .item-inner:after, .list-block li:last-child li .item-inner:after
	{
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #e7e7e7;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.list-block li li:last-child .item-inner:after, .list-block li:last-child li .item-inner:after
		{
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.list-block li li:last-child .item-inner:after, .list-block li:last-child li .item-inner:after
		{
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.list-block input[type=text], .list-block input[type=password],
	.list-block input[type=email], .list-block input[type=tel], .list-block input[type=url],
	.list-block input[type=date], .list-block input[type=datetime-local],
	.list-block input[type=time], .list-block input[type=number],
	.list-block input[type=search], .list-block select, .list-block textarea
	{
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	appearance: none;
	box-sizing: border-box;
	border: none;
	background: 0 0;
	border-radius: 0;
	box-shadow: none;
	display: block;
	padding: 0 0 0 .25rem;
	margin: 0;
	width: 100%;
	height: 2.15rem;
	color: #3d4145;
	font-size: .85rem;
	font-family: inherit
}

.list-block input[type=date], .list-block input[type=datetime-local] {
	line-height: 2.2rem
}

.list-block select {
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	appearance: none
}

.list-block .label {
	vertical-align: top
}

.list-block textarea {
	height: 5rem;
	resize: none;
	line-height: 1.4;
	padding-top: .4rem;
	padding-bottom: .35rem
}

.label-switch {
	display: inline-block;
	vertical-align: middle;
	width: 2.6rem;
	border-radius: .8rem;
	box-sizing: border-box;
	height: 1.6rem;
	position: relative;
	cursor: pointer;
	-webkit-align-self: center;
	align-self: center
}

.label-switch .checkbox {
	width: 2.6rem;
	border-radius: .8rem;
	box-sizing: border-box;
	height: 1.6rem;
	background: #e5e5e5;
	z-index: 0;
	margin: 0;
	padding: 0;
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	appearance: none;
	border: none;
	cursor: pointer;
	position: relative;
	-webkit-transition-duration: .3s;
	transition-duration: .3s
}

.label-switch .checkbox:before {
	content: ' ';
	position: absolute;
	left: .1rem;
	top: .1rem;
	width: 2.4rem;
	border-radius: .8rem;
	box-sizing: border-box;
	height: 1.4rem;
	background: #fff;
	z-index: 1;
	-webkit-transition-duration: .3s;
	transition-duration: .3s;
	-webkit-transform: scale(1);
	transform: scale(1)
}

.label-switch .checkbox:after {
	content: ' ';
	height: 1.4rem;
	width: 1.4rem;
	border-radius: 1.4rem;
	background: #fff;
	position: absolute;
	z-index: 2;
	top: .1rem;
	left: .1rem;
	box-shadow: 0 .1rem .25rem rgba(0, 0, 0, .4);
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition-duration: .3s;
	transition-duration: .3s
}

.label-switch input[type=checkbox] {
	display: none
}

.label-switch input[type=checkbox]:checked+.checkbox {
	background: #4cd964
}

.label-switch input[type=checkbox]:checked+.checkbox:before {
	-webkit-transform: scale(0);
	transform: scale(0)
}

.label-switch input[type=checkbox]:checked+.checkbox:after {
	-webkit-transform: translateX(1.1rem);
	transform: translateX(1.1rem)
}

html.android .label-switch input[type=checkbox]+.checkbox {
	-webkit-transition-duration: 0;
	transition-duration: 0
}

html.android .label-switch input[type=checkbox]+.checkbox:after, html.android .label-switch input[type=checkbox]+.checkbox:before
	{
	-webkit-transition-duration: 0;
	transition-duration: 0
}

.range-slider {
	width: 100%;
	position: relative;
	overflow: hidden;
	padding-left: .15rem;
	padding-right: .15rem;
	margin-left: -1px;
	-webkit-align-self: center;
	align-self: center
}

.range-slider input[type=range] {
	position: relative;
	height: 1.4rem;
	width: 100%;
	margin: .2rem 0 .25rem 0;
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	appearance: none;
	background: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0, #b7b8b7),
		color-stop(100%, #b7b8b7));
	background: -webkit-linear-gradient(left, #b7b8b7 0, #b7b8b7 100%);
	background: linear-gradient(to right, #b7b8b7 0, #b7b8b7 100%);
	background-position: center;
	background-size: 100% .1rem;
	background-repeat: no-repeat;
	outline: 0
}

.range-slider input[type=range]:after {
	height: .1rem;
	background: #fff;
	content: ' ';
	width: .25rem;
	top: 50%;
	margin-top: -1px;
	left: -.25rem;
	z-index: 1;
	position: absolute
}

.range-slider input[type=range]::-webkit-slider-thumb {
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	appearance: none;
	border: none;
	height: 1.4rem;
	width: 1.4rem;
	position: relative;
	background: 0 0
}

.range-slider input[type=range]::-webkit-slider-thumb:after {
	height: 1.4rem;
	width: 1.4rem;
	border-radius: 1.4rem;
	background: #fff;
	z-index: 10;
	box-shadow: 0 .1rem .2rem rgba(0, 0, 0, .4);
	position: absolute;
	left: 0;
	top: 0;
	content: ' '
}

.range-slider input[type=range]::-webkit-slider-thumb:before {
	position: absolute;
	top: 50%;
	right: 100%;
	width: 100rem;
	height: .1rem;
	margin-top: -1px;
	z-index: 1;
	background: #0894ec;
	content: ' '
}

label.label-checkbox {
	cursor: pointer
}

label.label-checkbox i.icon-form-checkbox {
	width: 1.1rem;
	height: 1.1rem;
	position: relative;
	border-radius: 1.1rem;
	border: 1px solid #c7c7cc;
	box-sizing: border-box
}

label.label-checkbox i.icon-form-checkbox:after {
	content: ' ';
	position: absolute;
	left: 50%;
	margin-left: -.3rem;
	top: 50%;
	margin-top: -.2rem;
	width: .6rem;
	height: .45rem
}

label.label-checkbox input[type=checkbox], label.label-checkbox input[type=radio]
	{
	display: none
}

label.label-checkbox input[type=checkbox]:checked+.item-media i.icon-form-checkbox,
	label.label-checkbox input[type=radio]:checked+.item-media i.icon-form-checkbox
	{
	border: none;
	background-color: #0894ec
}

label.label-checkbox input[type=checkbox]:checked+.item-media i.icon-form-checkbox:after,
	label.label-checkbox input[type=radio]:checked+.item-media i.icon-form-checkbox:after
	{
	background: no-repeat center;
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20x%3D'0px'%20y%3D'0px'%20viewBox%3D'0%200%2012%209'%20xml%3Aspace%3D'preserve'%3E%3Cpolygon%20fill%3D'%23ffffff'%20points%3D'12%2C0.7%2011.3%2C0%203.9%2C7.4%200.7%2C4.2%200%2C4.9%203.9%2C8.8%203.9%2C8.8%203.9%2C8.8%20'%2F%3E%3C%2Fsvg%3E");
	background-size: .6rem .45rem
}

label.label-checkbox {
	-webkit-transition-duration: .3s;
	transition-duration: .3s
}

html:not (.watch-active-state ) label.label-checkbox:active, label.label-checkbox.active-state
	{
	-webkit-transition: 0s;
	transition: 0s;
	background-color: #d9d9d9
}

html:not (.watch-active-state ) label.label-checkbox:active .item-inner:after,
	label.label-checkbox.active-state .item-inner:after {
	background-color: transparent
}

.smart-select select {
	display: none
}

.searchbar {
	padding: 8px 0;
	overflow: hidden;
	height: 2.2rem;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center
}

.searchbar .searchbar-cancel {
	margin-right: -3rem;
	width: 2.2rem;
	float: right;
	height: 1.4rem;
	line-height: 1.4rem;
	text-align: center;
	-webkit-transition: all .3s;
	transition: all .3s;
	opacity: 0;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.searchbar .search-input {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	margin-right: 0;
	-webkit-transition: all .3s;
	transition: all .3s
}

.searchbar .search-input input {
	margin: 0;
	height: 1.4rem
}

.searchbar.searchbar-active .searchbar-cancel {
	margin-right: 0;
	opacity: 1
}

.searchbar.searchbar-active .searchbar-cancel+.search-input {
	margin-right: 2.5rem
}

.search-input {
	position: relative
}

.search-input input {
	box-sizing: border-box;
	width: 100%;
	height: 1.4rem;
	display: block;
	border: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	border-radius: .25rem;
	font-family: inherit;
	color: #3d4145;
	font-size: .7rem;
	font-weight: 400;
	padding: 0 .5rem;
	background-color: #fff;
	border: 1px solid #b4b4b4
}

.search-input input::-webkit-input-placeholder {
	color: #ccc;
	opacity: 1
}

.search-input .icon {
	position: absolute;
	font-size: .9rem;
	color: #b4b4b4;
	top: 50%;
	left: .3rem;
	-webkit-transform: translate3D(0, -50%, 0);
	transform: translate3D(0, -50%, 0)
}

.search-input label+input {
	padding-left: 1.4rem
}

.bar .searchbar {
	margin: 0 -.5rem;
	padding: .4rem .5rem;
	background: rgba(0, 0, 0, .1)
}

.bar .searchbar .search-input input {
	border: 0
}

.bar .searchbar .searchbar-cancel {
	color: #5f646e
}

.button {
	border: 1px solid #0894ec;
	color: #0894ec;
	text-decoration: none;
	text-align: center;
	display: block;
	border-radius: .25rem;
	line-height: 1.25rem;
	box-sizing: border-box;
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	appearance: none;
	background: 0 0;
	padding: 0 .5rem;
	margin: 0;
	height: 1.35rem;
	white-space: nowrap;
	position: relative;
	text-overflow: ellipsis;
	font-size: .7rem;
	font-family: inherit;
	cursor: pointer
}

input[type=button].button, input[type=submit].button {
	width: 100%
}

.button:active {
	color: #0a8ddf;
	border-color: #0a8ddf
}

.button.button-round {
	border-radius: 1.25rem
}

.button.active, .button.active:active {
	color: #0a8ddf;
	border-color: #0a8ddf
}

.button.button-big {
	font-size: .85rem;
	height: 2.4rem;
	line-height: 2.3rem
}

.button.button-fill {
	color: #fff;
	background: #0894ec;
	border: none;
	line-height: 1.35rem
}

.button.button-fill.active, .button.button-fill:active {
	background: #0a8ddf
}

.button.button-fill.button-big {
	line-height: 2.4rem
}

.button .button-link {
	padding-top: .3rem;
	padding-bottom: .3rem;
	color: #0894ec;
	background-color: transparent;
	border: 0
}

.button i.icon:first-child {
	margin-right: .5rem
}

.button i.icon:last-child {
	margin-left: .5rem
}

.button i.icon:first-child:last-child {
	margin-left: 0;
	margin-right: 0
}

.button-light {
	border-color: #ccc;
	color: #ccc;
	color: #5f646e
}

.button-light:active {
	border-color: #0a8ddf;
	color: #0a8ddf
}

.button-light.button-fill {
	color: #fff;
	background-color: #ccc
}

.button-light.button-fill:active {
	background-color: #0a8ddf
}

.button-dark {
	border-color: #6e727b;
	color: #6e727b;
	color: #5f646e
}

.button-dark:active {
	border-color: #0a8ddf;
	color: #0a8ddf
}

.button-dark.button-fill {
	color: #fff;
	background-color: #6e727b
}

.button-dark.button-fill:active {
	background-color: #0a8ddf
}

.button-success {
	border-color: #4cd964;
	color: #4cd964
}

.button-success:active {
	border-color: #2ac845;
	color: #2ac845
}

.button-success.button-fill {
	color: #fff;
	background-color: #4cd964
}

.button-success.button-fill:active {
	background-color: #2ac845
}

.button-danger {
	border-color: #f6383a;
	color: #f6383a
}

.button-danger:active {
	border-color: #f00b0d;
	color: #f00b0d
}

.button-danger.button-fill {
	color: #fff;
	background-color: #f6383a
}

.button-danger.button-fill:active {
	background-color: #f00b0d
}

.button-warning {
	border-color: #f60;
	color: #f60
}

.button-warning:active {
	border-color: #cc5200;
	color: #cc5200
}

.button-warning.button-fill {
	color: #fff;
	background-color: #f60
}

.button-warning.button-fill:active {
	background-color: #cc5200
}

.button.button-danger.disabled, .button.button-primary.disabled, .button.button-success.disabled,
	.button.button-warning.disabled, .button.disabled {
	border-color: #c8c9cb;
	color: #c8c9cb;
	cursor: not-allowed
}

.button.button-danger.disabled:active, .button.button-primary.disabled:active,
	.button.button-success.disabled:active, .button.button-warning.disabled:active,
	.button.disabled:active {
	border-color: #c8c9cb;
	color: #c8c9cb
}

.button.button-danger.disabled.button-fill, .button.button-primary.disabled.button-fill,
	.button.button-success.disabled.button-fill, .button.button-warning.disabled.button-fill,
	.button.disabled.button-fill {
	color: #fff;
	background-color: #c8c9cb
}

.button.button-danger.disabled.button-fill:active, .button.button-primary.disabled.button-fill:active,
	.button.button-success.disabled.button-fill:active, .button.button-warning.disabled.button-fill:active,
	.button.disabled.button-fill:active {
	background-color: #c8c9cb
}

.buttons-row, .buttons-tab {
	-webkit-align-self: center;
	align-self: center;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-lines: single;
	-moz-box-lines: single;
	-webkit-flex-wrap: nowrap;
	flex-wrap: nowrap
}

.buttons-row .button {
	border-radius: 0;
	margin-left: -1px;
	width: 100%;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	border-color: #0894ec;
	color: #0894ec
}

.buttons-row .button.active {
	background-color: #0894ec;
	color: #fff;
	z-index: 90
}

.buttons-row .button:first-child {
	border-radius: .25rem 0 0 .25rem;
	margin-left: 0;
	border-left-width: 1px;
	border-left-style: solid
}

.buttons-row .button:last-child {
	border-radius: 0 .25rem .25rem 0
}

.buttons-row .button.button-round:first-child {
	border-radius: 1.35rem 0 0 1.35rem
}

.buttons-row .button.button-round:last-child {
	border-radius: 0 1.35rem 1.35rem 0
}

.buttons-tab {
	background: #fff;
	position: relative
}

.buttons-tab:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #d0d0d0;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.buttons-tab:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.buttons-tab:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.buttons-tab .button {
	color: #5f646e;
	font-size: .8rem;
	width: 100%;
	height: 2rem;
	line-height: 2rem;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	border: 0;
	border-bottom: 2px solid transparent;
	border-radius: 0
}

.buttons-tab .button.active {
	color: #0894ec;
	border-color: #0894ec;
	z-index: 100
}

.buttons-fixed {
	position: fixed;
	z-index: 99;
	width: 100%
}

.tabs .tab {
	display: none
}

.tabs .tab.active {
	display: block
}

.tabs-animated-wrap {
	position: relative;
	width: 100%;
	overflow: hidden;
	height: 100%
}

.tabs-animated-wrap>.tabs {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	height: 100%;
	-webkit-transition: .3s;
	transition: .3s
}

.tabs-animated-wrap>.tabs>.tab {
	width: 100%;
	display: block;
	-webkit-flex-shrink: 0;
	-ms-flex: 0 0 auto;
	-webkit-flex-shrink: 0;
	flex-shrink: 0
}

.page, .page-group {
	box-sizing: border-box;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: #efeff4;
	display: none
}

.page-group.page-current, .page-group.page-from-center-to-left,
	.page-group.page-from-center-to-right, .page-group.page-from-left-to-center,
	.page-group.page-from-right-to-center, .page-group.page-visible, .page.page-current,
	.page.page-from-center-to-left, .page.page-from-center-to-right, .page.page-from-left-to-center,
	.page.page-from-right-to-center, .page.page-visible {
	display: block
}

.page-group.page-current, .page.page-current {
	overflow: hidden
}

.page-group {
	display: block
}

.page-transitioning, .page-transitioning .swipeback-page-shadow {
	-webkit-transition: .4s;
	transition: .4s
}

.page-from-right-to-center {
	-webkit-animation: pageFromRightToCenter .4s forwards;
	animation: pageFromRightToCenter .4s forwards;
	z-index: 2002
}

.page-from-center-to-right {
	-webkit-animation: pageFromCenterToRight .4s forwards;
	animation: pageFromCenterToRight .4s forwards;
	z-index: 2002
}

@
-webkit-keyframes pageFromRightToCenter {
	from {-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0);
	opacity: .9
}

to {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1
}

}
@
keyframes pageFromRightToCenter {
	from {-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0);
	opacity: .9
}

to {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1
}

}
@
-webkit-keyframes pageFromCenterToRight {
	from {-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1
}

to {
	-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0);
	opacity: .9
}

}
@
keyframes pageFromCenterToRight {
	from {-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1
}

to {
	-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0);
	opacity: .9
}

}
.page-from-center-to-left {
	-webkit-animation: pageFromCenterToLeft .4s forwards;
	animation: pageFromCenterToLeft .4s forwards
}

.page-from-left-to-center {
	-webkit-animation: pageFromLeftToCenter .4s forwards;
	animation: pageFromLeftToCenter .4s forwards
}

@
-webkit-keyframes pageFromCenterToLeft {
	from {opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

to {
	opacity: .5;
	-webkit-transform: translate3d(-20%, 0, 0);
	transform: translate3d(-20%, 0, 0)
}

}
@
keyframes pageFromCenterToLeft {
	from {opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

to {
	opacity: .5;
	-webkit-transform: translate3d(-20%, 0, 0);
	transform: translate3d(-20%, 0, 0)
}

}
@
-webkit-keyframes pageFromLeftToCenter {
	from {opacity: .5;
	-webkit-transform: translate3d(-20%, 0, 0);
	transform: translate3d(-20%, 0, 0)
}

to {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

}
@
keyframes pageFromLeftToCenter {
	from {opacity: .5;
	-webkit-transform: translate3d(-20%, 0, 0);
	transform: translate3d(-20%, 0, 0)
}

to {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

}
.content-inner {
	box-sizing: border-box;
	border-top: 1px solid transparent;
	margin-top: -1px;
	padding-bottom: .5rem
}

.javascript-scroll {
	overflow: hidden
}

.pull-to-refresh-layer {
	position: relative;
	left: 0;
	top: 0;
	width: 100%;
	height: 2.2rem
}

.pull-to-refresh-layer .preloader {
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -.5rem;
	margin-top: -.5rem;
	visibility: hidden
}

.pull-to-refresh-layer .pull-to-refresh-arrow {
	width: .65rem;
	height: 1rem;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -.15rem;
	margin-top: -.5rem;
	background: no-repeat center;
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2026%2040'%3E%3Cpolygon%20points%3D'9%2C22%209%2C0%2017%2C0%2017%2C22%2026%2C22%2013.5%2C40%200%2C22'%20fill%3D'%238c8c8c'%2F%3E%3C%2Fsvg%3E");
	background-size: .65rem 1rem;
	z-index: 10;
	-webkit-transform: rotate(0) translate3d(0, 0, 0);
	transform: rotate(0) translate3d(0, 0, 0);
	-webkit-transition-duration: .3s;
	transition-duration: .3s
}

.pull-to-refresh-content {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.pull-to-refresh-content.refreshing, .pull-to-refresh-content.transitioning
	{
	-webkit-transition: -webkit-transform .4s;
	transition: transform .4s
}

.pull-to-refresh-content:not (.refreshing ) .pull-to-refresh-layer .preloader
	{
	-webkit-animation: none;
	animation: none
}

.pull-to-refresh-content.refreshing .pull-to-refresh-arrow {
	visibility: hidden;
	-webkit-transition-duration: 0s;
	transition-duration: 0s
}

.pull-to-refresh-content.refreshing .preloader {
	visibility: visible
}

.pull-to-refresh-content.pull-up .pull-to-refresh-arrow {
	-webkit-transform: rotate(180deg) translate3d(0, 0, 0);
	transform: rotate(180deg) translate3d(0, 0, 0)
}

.pull-to-refresh-content {
	top: -2.2rem
}

.pull-to-refresh-content.refreshing {
	-webkit-transform: translate3d(0, 2.2rem, 0);
	transform: translate3d(0, 2.2rem, 0)
}

.bar-footer ~.pull-to-refresh-content, .bar-nav ~.pull-to-refresh-content,
	.bar-tab ~.pull-to-refresh-content {
	top: 0
}

.bar-footer ~.pull-to-refresh-content.refreshing, .bar-nav ~.pull-to-refresh-content.refreshing,
	.bar-tab ~.pull-to-refresh-content.refreshing {
	-webkit-transform: translate3d(0, 2.2rem, 0);
	transform: translate3d(0, 2.2rem, 0)
}

.bar-footer-secondary ~.pull-to-refresh-content, .bar-header-secondary ~.pull-to-refresh-content
	{
	top: 2.2rem
}

.infinite-scroll-preloader {
	margin: .5rem;
	text-align: center
}

.infinite-scroll-preloader .preloader {
	width: 1.5rem;
	height: 1.5rem
}

.infinite-scroll-top .infinite-scroll-preloader {
	position: absolute;
	width: 100%;
	top: 0;
	margin: 0
}

.modal-overlay, .popup-overlay, .preloader-indicator-overlay {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .4);
	z-index: 10600;
	visibility: hidden;
	opacity: 0;
	-webkit-transition-duration: .4s;
	transition-duration: .4s
}

.modal-overlay.modal-overlay-visible, .popup-overlay.modal-overlay-visible,
	.preloader-indicator-overlay.modal-overlay-visible {
	visibility: visible;
	opacity: 1
}

.popup-overlay {
	z-index: 10200
}

.modal {
	width: 13.5rem;
	position: absolute;
	z-index: 11000;
	left: 50%;
	margin-left: -6.75rem;
	margin-top: 0;
	top: 50%;
	text-align: center;
	border-radius: .35rem;
	opacity: 0;
	-webkit-transform: translate3d(0, 0, 0) scale(1.185);
	transform: translate3d(0, 0, 0) scale(1.185);
	-webkit-transition-property: -webkit-transform, opacity;
	transition-property: transform, opacity;
	color: #3d4145;
	display: none
}

.modal.modal-in {
	opacity: 1;
	-webkit-transition-duration: .4s;
	transition-duration: .4s;
	-webkit-transform: translate3d(0, 0, 0) scale(1);
	transform: translate3d(0, 0, 0) scale(1)
}

.modal.modal-out {
	opacity: 0;
	z-index: 10999;
	-webkit-transition-duration: .4s;
	transition-duration: .4s;
	-webkit-transform: translate3d(0, 0, 0) scale(.815);
	transform: translate3d(0, 0, 0) scale(.815)
}

.modal-inner {
	padding: .75rem;
	border-radius: .35rem .35rem 0 0;
	position: relative;
	background: #e8e8e8
}

.modal-inner:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #b5b5b5;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.modal-inner:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.modal-inner:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.modal-title {
	font-weight: 500;
	font-size: .9rem;
	text-align: center
}

.modal-title+.modal-text {
	margin-top: .25rem
}

.modal-buttons {
	height: 2.2rem;
	overflow: hidden;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center
}

.modal-buttons.modal-buttons-vertical {
	display: block;
	height: auto
}

.modal-button {
	width: 100%;
	padding: 0 .25rem;
	height: 2.2rem;
	font-size: .85rem;
	line-height: 2.2rem;
	text-align: center;
	color: #0894ec;
	background: #e8e8e8;
	display: block;
	position: relative;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	cursor: pointer;
	box-sizing: border-box;
	-webkit-box-flex: 1;
	-ms-flex: 1
}

.modal-button:after {
	content: '';
	position: absolute;
	right: 0;
	top: 0;
	left: auto;
	bottom: auto;
	width: 1px;
	height: 100%;
	background-color: #b5b5b5;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 100% 50%;
	transform-origin: 100% 50%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.modal-button:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.modal-button:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.modal-button:first-child {
	border-radius: 0 0 0 .35rem
}

.modal-button:last-child {
	border-radius: 0 0 .35rem 0
}

.modal-button:last-child:after {
	display: none
}

.modal-button:first-child:last-child {
	border-radius: 0 0 .35rem .35rem
}

.modal-button.modal-button-bold {
	font-weight: 500
}

.modal-button.active-state, html:not (.watch-active-state ) .modal-button:active
	{
	background: #d4d4d4
}

.modal-buttons-vertical .modal-button {
	border-radius: 0
}

.modal-buttons-vertical .modal-button:after {
	display: none
}

.modal-buttons-vertical .modal-button:before {
	display: none
}

.modal-buttons-vertical .modal-button:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #b5b5b5;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.modal-buttons-vertical .modal-button:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.modal-buttons-vertical .modal-button:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.modal-buttons-vertical .modal-button:last-child {
	border-radius: 0 0 .35rem .35rem
}

.modal-buttons-vertical .modal-button:last-child:after {
	display: none
}

.modal-no-buttons .modal-inner {
	border-radius: .35rem
}

.modal-no-buttons .modal-inner:after {
	display: none
}

.modal-no-buttons .modal-buttons {
	display: none
}

.actions-modal {
	position: absolute;
	left: 0;
	bottom: 0;
	z-index: 11000;
	width: 100%;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.actions-modal.modal-in {
	-webkit-transition-duration: .3s;
	transition-duration: .3s;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.actions-modal.modal-out {
	z-index: 10999;
	-webkit-transition-duration: .3s;
	transition-duration: .3s;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.actions-modal-group {
	margin: .4rem
}

.actions-modal-button, .actions-modal-label {
	width: 100%;
	text-align: center;
	font-weight: 400;
	margin: 0;
	background: rgba(243, 243, 243, .95);
	box-sizing: border-box;
	display: block;
	position: relative
}

.actions-modal-button:after, .actions-modal-label:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #d2d2d6;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.actions-modal-button:after, .actions-modal-label:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.actions-modal-button:after, .actions-modal-label:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.actions-modal-button a, .actions-modal-label a {
	text-decoration: none;
	color: inherit
}

.actions-modal-button b, .actions-modal-label b {
	font-weight: 500
}

.actions-modal-button.actions-modal-button-bold, .actions-modal-label.actions-modal-button-bold
	{
	font-weight: 500
}

.actions-modal-button.actions-modal-button-danger, .actions-modal-label.actions-modal-button-danger
	{
	color: #f6383a
}

.actions-modal-button.color-danger, .actions-modal-label.color-danger {
	color: #f6383a
}

.actions-modal-button.bg-danger, .actions-modal-label.bg-danger {
	background: #f6383a;
	color: #fff
}

.actions-modal-button.bg-danger:active, .actions-modal-label.bg-danger:active
	{
	background: #f00b0d
}

.actions-modal-button:first-child, .actions-modal-label:first-child {
	border-radius: .2rem .2rem 0 0
}

.actions-modal-button:last-child, .actions-modal-label:last-child {
	border-radius: 0 0 .2rem .2rem
}

.actions-modal-button:last-child:after, .actions-modal-label:last-child:after
	{
	display: none
}

.actions-modal-button:first-child:last-child, .actions-modal-label:first-child:last-child
	{
	border-radius: .2rem
}

.actions-modal-button.disabled, .actions-modal-label.disabled {
	opacity: .95;
	color: #8e8e93
}

.actions-modal-button {
	cursor: pointer;
	line-height: 2.15rem;
	font-size: 1rem;
	color: #0894ec
}

.actions-modal-button.active-state, .actions-modal-button:active {
	background: #dcdcdc
}

.actions-modal-label {
	font-size: .7rem;
	line-height: 1.3;
	min-height: 2.2rem;
	padding: .4rem .5rem;
	color: #5f646e;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center
}

input.modal-text-input {
	box-sizing: border-box;
	height: 1.5rem;
	background: #fff;
	margin: 0;
	margin-top: .75rem;
	padding: 0 .25rem;
	border: 1px solid #a0a0a0;
	border-radius: .25rem;
	width: 100%;
	font-size: .7rem;
	font-family: inherit;
	display: block;
	box-shadow: 0 0 0 transparent;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none
}

input.modal-text-input+input.modal-text-input {
	margin-top: .25rem
}

input.modal-text-input.modal-text-input-double {
	border-radius: .25rem .25rem 0 0
}

input.modal-text-input.modal-text-input-double+input.modal-text-input {
	margin-top: 0;
	border-top: 0;
	border-radius: 0 0 .25rem .25rem
}

.login-screen, .popup {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 10400;
	background: #fff;
	box-sizing: border-box;
	display: none;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	-webkit-transition-property: -webkit-transform;
	transition-property: transform;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.login-screen.modal-in, .login-screen.modal-out, .popup.modal-in, .popup.modal-out
	{
	-webkit-transition-duration: .4s;
	transition-duration: .4s
}

.login-screen.modal-in, .popup.modal-in {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.login-screen.modal-out, .popup.modal-out {
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.login-screen.modal-in, .login-screen.modal-out {
	display: block
}

html.with-statusbar-overlay .popup {
	height: -webkit-calc(100% - 1rem);
	height: calc(100% - 1rem);
	top: 1rem
}

html.with-statusbar-overlay .popup-overlay {
	z-index: 9800
}

@media all and (max-width:629px) , ( max-height :629px) {
	html.with-statusbar-overlay .popup {
		height: -webkit-calc(100% - 1rem);
		height: calc(100% - 1rem);
		top: 1rem
	}
	html.with-statusbar-overlay .popup-overlay {
		z-index: 9800
	}
}

html.with-statusbar-overlay .login-screen, html.with-statusbar-overlay .popup.tablet-fullscreen
	{
	height: -webkit-calc(100% - 1rem);
	height: calc(100% - 1rem);
	top: 1rem
}

.modal .preloader {
	width: 1.7rem;
	height: 1.7rem
}

.preloader-indicator-overlay {
	visibility: visible;
	opacity: 0;
	background: 0 0
}

.preloader-indicator-modal {
	position: absolute;
	left: 50%;
	top: 50%;
	padding: .4rem;
	margin-left: -1.25rem;
	margin-top: -1.25rem;
	background: rgba(0, 0, 0, .8);
	z-index: 11000;
	border-radius: .25rem
}

.preloader-indicator-modal .preloader {
	display: block;
	width: 1.7rem;
	height: 1.7rem
}

.picker-modal {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 13rem;
	z-index: 11500;
	display: none;
	-webkit-transition-property: -webkit-transform;
	transition-property: transform;
	background: #cfd5da;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.picker-modal.modal-in, .picker-modal.modal-out {
	-webkit-transition-duration: .4s;
	transition-duration: .4s
}

.picker-modal.modal-in {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.picker-modal.modal-out {
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.picker-modal .picker-modal-inner {
	height: 100%;
	position: relative
}

.picker-modal .toolbar {
	position: relative;
	width: 100%
}

.picker-modal .toolbar:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #999;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-modal .toolbar:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-modal .toolbar:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-modal .toolbar+.picker-modal-inner {
	height: -webkit-calc(100% - 2.2rem);
	height: calc(100% - 2.2rem)
}

.picker-modal.picker-modal-inline {
	display: block;
	position: relative;
	background: 0 0;
	z-index: inherit;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.picker-modal.picker-modal-inline .toolbar:before {
	display: none
}

.picker-modal.picker-modal-inline .toolbar:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #999;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-modal.picker-modal-inline .toolbar:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-modal.picker-modal-inline .toolbar:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.toast {
	background: rgba(0, 0, 0, .8);
	border-radius: 1rem;
	color: #fff;
	padding: 0 .8rem;
	height: 2rem;
	line-height: 2rem;
	font-size: .8rem;
	width: auto
}

.preloader {
	display: inline-block;
	width: 1rem;
	height: 1rem;
	-webkit-transform-origin: 50%;
	transform-origin: 50%;
	-webkit-animation: preloader-spin 1s steps(12, end) infinite;
	animation: preloader-spin 1s steps(12, end) infinite
}

.preloader:after {
	display: block;
	content: "";
	width: 100%;
	height: 100%;
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
	background-position: 50%;
	background-size: 100%;
	background-repeat: no-repeat
}

.preloader-white:after {
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%23fff'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E")
}

@
-webkit-keyframes preloader-spin { 100%{
	-webkit-transform: rotate(360deg)
}

}
@
keyframes preloader-spin { 100%{
	-webkit-transform: rotate(360deg);
	transform: rotate(360deg)
}

}
.card .list-block ul, .cards-list ul {
	background: 0 0
}

.card .list-block>ul:before, .cards-list>ul:before {
	display: none
}

.card .list-block>ul:after, .cards-list>ul:after {
	display: none
}

.card {
	background: #fff;
	box-shadow: 0 .05rem .1rem rgba(0, 0, 0, .3);
	margin: .5rem;
	position: relative;
	border-radius: .1rem;
	font-size: .7rem
}

.card .content-block, .card .list-block {
	margin: 0
}

.row:not (.no-gutter ) .col>.card {
	margin-left: 0;
	margin-right: 0
}

.card-content {
	position: relative
}

.card-content-inner {
	padding: .75rem;
	position: relative
}

.card-content-inner>p:first-child {
	margin-top: 0
}

.card-content-inner>p:last-child {
	margin-bottom: 0
}

.card-content-inner>.content-block, .card-content-inner>.list-block {
	margin: -.75rem
}

.card-footer, .card-header {
	min-height: 2.2rem;
	position: relative;
	padding: .5rem .75rem;
	box-sizing: border-box;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center
}

.card-footer[valign=top], .card-header[valign=top] {
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	align-items: flex-start
}

.card-footer[valign=bottom], .card-header[valign=bottom] {
	-webkit-box-align: end;
	-webkit-align-items: flex-end;
	align-items: flex-end
}

.card-footer a.link, .card-header a.link {
	line-height: 2.2rem;
	height: 2.2rem;
	text-decoration: none;
	position: relative;
	margin-top: -.5rem;
	margin-bottom: -.5rem;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	-webkit-transition-duration: .3s;
	transition-duration: .3s
}

.card-footer a.link.active-state, .card-header a.link.active-state, html:not
	(.watch-active-state ) .card-footer a.link:active, html:not (.watch-active-state
	) .card-header a.link:active {
	opacity: .3;
	-webkit-transition-duration: 0s;
	transition-duration: 0s
}

.card-footer a.link i+i, .card-footer a.link i+span, .card-footer a.link span+i,
	.card-footer a.link span+span, .card-header a.link i+i, .card-header a.link i+span,
	.card-header a.link span+i, .card-header a.link span+span {
	margin-left: .35rem
}

.card-footer a.link i.icon, .card-header a.link i.icon {
	display: block
}

.card-footer a.icon-only, .card-header a.icon-only {
	min-width: 2.2rem;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	margin: 0
}

.card-header {
	border-radius: .1rem .1rem 0 0;
	font-size: .85rem
}

.card-header:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #e1e1e1;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.card-header:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.card-header:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.card-header .card-cover {
	width: 100%;
	display: block
}

.card-header.no-border:after {
	display: none
}

.card-header.no-padding {
	padding: 0
}

.card-footer {
	border-radius: 0 0 .1rem .1rem;
	color: #5f646e
}

.card-footer:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #e1e1e1;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.card-footer:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.card-footer:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.card-footer.no-border:before {
	display: none
}

.facebook-card .card-header {
	display: block;
	padding: .5rem
}

.facebook-card .facebook-avatar {
	float: left
}

.facebook-card .facebook-name {
	margin-left: 2.2rem;
	font-size: .7rem;
	font-weight: 500
}

.facebook-card .facebook-date {
	margin-left: 2.2rem;
	font-size: .65rem;
	color: #5f646e
}

.facebook-card .card-footer {
	background: #fafafa
}

.facebook-card .card-footer a {
	color: #5f646e;
	font-weight: 500
}

.facebook-card .card-content img {
	display: block
}

.facebook-card .card-content-inner {
	padding: .75rem .5rem
}

.panel-overlay {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0);
	opacity: 0;
	z-index: 5999;
	display: none
}

.panel {
	z-index: 1000;
	display: none;
	background: #111;
	color: #fff;
	box-sizing: border-box;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	position: absolute;
	width: 12rem;
	top: 0;
	height: 100%;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	-webkit-transition: -webkit-transform .4s;
	transition: transform .4s
}

.panel.panel-left.panel-cover {
	z-index: 6000;
	left: -12rem
}

.panel.panel-left.panel-reveal {
	left: 0
}

.panel.panel-right.panel-cover {
	z-index: 6000;
	right: -12rem
}

.panel.panel-right.panel-reveal {
	right: 0
}

body.with-panel-left-cover .page, body.with-panel-right-cover .page {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	-webkit-transition: -webkit-transform .4s;
	transition: transform .4s
}

body.with-panel-left-cover .panel-overlay, body.with-panel-right-cover .panel-overlay
	{
	display: block
}

body.with-panel-left-reveal .page, body.with-panel-right-reveal .page {
	-webkit-transition: .4s;
	transition: .4s;
	-webkit-transition-property: -webkit-transform;
	transition-property: transform
}

body.with-panel-left-reveal .panel-overlay, body.with-panel-right-reveal .panel-overlay
	{
	display: block
}

body.with-panel-left-reveal .page {
	-webkit-transform: translate3d(12rem, 0, 0);
	transform: translate3d(12rem, 0, 0)
}

body.with-panel-left-reveal .panel-overlay {
	margin-left: 12rem
}

body.with-panel-left-cover .panel-left {
	-webkit-transform: translate3d(12rem, 0, 0);
	transform: translate3d(12rem, 0, 0)
}

body.with-panel-right-reveal .page {
	-webkit-transform: translate3d(-12rem, 0, 0);
	transform: translate3d(-12rem, 0, 0)
}

body.with-panel-right-reveal .panel-overlay {
	margin-left: -12rem
}

body.with-panel-right-cover .panel-right {
	-webkit-transform: translate3d(-12rem, 0, 0);
	transform: translate3d(-12rem, 0, 0)
}

body.panel-closing .page {
	-webkit-transition: .4s;
	transition: .4s;
	-webkit-transition-property: -webkit-transform;
	transition-property: transform
}

.picker-calendar {
	background: #fff;
	height: 300px;
	width: 100%;
	overflow: hidden
}

@media ( orientation :landscape) and (max-height:415px) {
	.picker-calendar:not (.picker-modal-inline ){
		height: 220px
	}
}

.picker-calendar .picker-modal-inner {
	overflow: hidden
}

.picker-calendar-week-days {
	height: 18px;
	background: #f7f7f8;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	font-size: 11px;
	box-sizing: border-box;
	position: relative
}

.picker-calendar-week-days:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #c4c4c4;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-calendar-week-days:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-calendar-week-days:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-calendar-week-days .picker-calendar-week-day {
	-webkit-flex-shrink: 1;
	-ms-flex: 0 1 auto;
	-webkit-flex-shrink: 1;
	flex-shrink: 1;
	width: 14.28571429%;
	width: -webkit-calc(100%/ 7);
	width: calc(100%/ 7);
	line-height: 17px;
	text-align: center
}

.picker-calendar-week-days+.picker-calendar-months {
	height: -webkit-calc(100% - 18px);
	height: calc(100% - 18px)
}

.picker-calendar-months {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative
}

.picker-calendar-months-wrapper {
	position: relative;
	width: 100%;
	height: 100%;
	-webkit-transition: .3s;
	transition: .3s
}

.picker-calendar-month {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column;
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0
}

.picker-calendar-row {
	height: 16.66666667%;
	height: -webkit-calc(100%/ 6);
	height: calc(100%/ 6);
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-shrink: 1;
	-ms-flex: 0 1 auto;
	-webkit-flex-shrink: 1;
	flex-shrink: 1;
	width: 100%;
	position: relative
}

.picker-calendar-row:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #ccc;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-calendar-row:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-calendar-row:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-calendar-row:last-child:after {
	display: none
}

.picker-calendar-day {
	-webkit-flex-shrink: 1;
	-ms-flex: 0 1 auto;
	-webkit-flex-shrink: 1;
	flex-shrink: 1;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	box-sizing: border-box;
	width: 14.28571429%;
	width: -webkit-calc(100%/ 7);
	width: calc(100%/ 7);
	text-align: center;
	color: #3d4145;
	font-size: 15px;
	cursor: pointer
}

.picker-calendar-day.picker-calendar-day-next, .picker-calendar-day.picker-calendar-day-prev
	{
	color: #ccc
}

.picker-calendar-day.picker-calendar-day-disabled {
	color: #d4d4d4;
	cursor: auto
}

.picker-calendar-day.picker-calendar-day-today span {
	background: #e3e3e3
}

.picker-calendar-day.picker-calendar-day-selected span {
	background: #0894ec;
	color: #fff
}

.picker-calendar-day span {
	display: inline-block;
	border-radius: 100%;
	width: 30px;
	height: 30px;
	line-height: 30px
}

.picker-calendar-month-picker, .picker-calendar-year-picker {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	width: 50%;
	max-width: 200px;
	-webkit-flex-shrink: 10;
	-ms-flex: 0 10 auto;
	-webkit-flex-shrink: 10;
	flex-shrink: 10
}

.picker-calendar-month-picker a.icon-only, .picker-calendar-year-picker a.icon-only
	{
	min-width: 36px
}

.picker-calendar-month-picker span, .picker-calendar-year-picker span {
	-webkit-flex-shrink: 1;
	-ms-flex: 0 1 auto;
	-webkit-flex-shrink: 1;
	flex-shrink: 1;
	position: relative;
	overflow: hidden;
	text-overflow: ellipsis
}

.picker-modal .toolbar-inner {
	height: 2.2rem;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	text-align: center
}

.picker-calendar-month-picker, .picker-calendar-year-picker {
	display: block;
	line-height: 2.2rem
}

.picker-calendar-month-picker a.icon-only, .picker-calendar-year-picker a.icon-only
	{
	float: left;
	width: 25%;
	height: 2.2rem;
	line-height: 2rem
}

.picker-calendar-month-picker .current-month-value,
	.picker-calendar-month-picker .current-year-value,
	.picker-calendar-year-picker .current-month-value,
	.picker-calendar-year-picker .current-year-value {
	float: left;
	width: 50%;
	height: 2.2rem
}

.picker-columns {
	width: 100%;
	height: 13rem;
	z-index: 11500
}

.picker-columns.picker-modal-inline {
	height: 10rem
}

@media ( orientation :landscape) and (max-height:415px) {
	.picker-columns:not (.picker-modal-inline ){
		height: 10rem
	}
}

.picker-items {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	padding: 0;
	text-align: right;
	font-size: 1.2rem;
	-webkit-mask-box-image: -webkit-linear-gradient(bottom, transparent, transparent 5%, #fff 20%,
		#fff 80%, transparent 95%, transparent);
	-webkit-mask-box-image: linear-gradient(to top, transparent, transparent 5%, #fff 20%, #fff 80%,
		transparent 95%, transparent)
}

.bar+.picker-items {
	height: 10.8rem
}

.picker-items-col {
	overflow: hidden;
	position: relative;
	max-height: 100%
}

.picker-items-col.picker-items-col-left {
	text-align: left
}

.picker-items-col.picker-items-col-center {
	text-align: center
}

.picker-items-col.picker-items-col-right {
	text-align: right
}

.picker-items-col.picker-items-col-divider {
	color: #3d4145;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center
}

.picker-items-col-normal {
	width: 100%
}

.picker-items-col-wrapper {
	-webkit-transition: .3s;
	transition: .3s;
	-webkit-transition-timing-function: ease-out;
	transition-timing-function: ease-out
}

.picker-item {
	height: 36px;
	line-height: 36px;
	padding: 0 10px;
	white-space: nowrap;
	position: relative;
	overflow: hidden;
	text-overflow: ellipsis;
	color: #999;
	left: 0;
	top: 0;
	width: 100%;
	box-sizing: border-box;
	-webkit-transition: .3s;
	transition: .3s
}

.picker-items-col-absolute .picker-item {
	position: absolute
}

.picker-item.picker-item-far {
	pointer-events: none
}

.picker-item.picker-selected {
	color: #3d4145;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	-webkit-transform: rotateX(0);
	transform: rotateX(0)
}

.picker-center-highlight {
	height: 36px;
	box-sizing: border-box;
	position: absolute;
	left: 0;
	width: 100%;
	top: 50%;
	margin-top: -18px;
	pointer-events: none
}

.picker-center-highlight:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #a8abb0;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-center-highlight:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-center-highlight:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-center-highlight:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #a8abb0;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-center-highlight:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-center-highlight:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-3d .picker-items {
	overflow: hidden;
	-webkit-perspective: 1200px;
	perspective: 1200px
}

.picker-3d .picker-item, .picker-3d .picker-items-col, .picker-3d .picker-items-col-wrapper
	{
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d
}

.picker-3d .picker-items-col {
	overflow: visible
}

.picker-3d .picker-item {
	-webkit-transform-origin: center center -110px;
	transform-origin: center center -110px;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition-timing-function: ease-out;
	transition-timing-function: ease-out
}

.picker-modal .bar {
	position: relative;
	top: 0
}

.picker-modal .bar:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: auto;
	right: auto;
	height: 1px;
	width: 100%;
	background-color: #a8abb0;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-modal .bar:before {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-modal .bar:before {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-modal .bar:after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	right: auto;
	top: auto;
	height: 1px;
	width: 100%;
	background-color: #a8abb0;
	display: block;
	z-index: 15;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%
}

@media only screen and (-webkit-min-device-pixel-ratio:2) {
	.picker-modal .bar:after {
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5)
	}
}

@media only screen and (-webkit-min-device-pixel-ratio:3) {
	.picker-modal .bar:after {
		-webkit-transform: scaleY(.33);
		transform: scaleY(.33)
	}
}

.picker-modal .bar .title {
	color: #5f646e;
	font-weight: 400
}

.city-picker .col-province {
	width: 5rem
}

.city-picker .col-city {
	width: 6rem
}

.city-picker .col-district {
	width: 5rem
}

@font-face {
	font-family: iconfont-sm;
	src: url(//at.alicdn.com/t/font_1433401008_2229297.woff) format('woff'),
		url(//at.alicdn.com/t/font_1433401008_2229297.ttf) format('truetype'),
		url(//at.alicdn.com/t/font_1433401008_2229297.svg#iconfont)
		format('svg')
}

.icon {
	font-family: iconfont-sm !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	-moz-osx-font-smoothing: grayscale;
	display: inline-block;
	vertical-align: middle;
	background-size: 100% auto;
	background-position: center;
	background-repeat: no-repeat
}

.icon-app:before {
	content: "\e605"
}

.icon-browser:before {
	content: "\e606"
}

.icon-card:before {
	content: "\e607"
}

.icon-cart:before {
	content: "\e600"
}

.icon-code:before {
	content: "\e609"
}

.icon-computer:before {
	content: "\e616"
}

.icon-remove:before {
	content: "\e60a"
}

.icon-download:before {
	content: "\e60b"
}

.icon-edit:before {
	content: "\e60c"
}

.icon-emoji:before {
	content: "\e615"
}

.icon-star:before {
	content: "\e60e"
}

.icon-friends:before {
	content: "\e601"
}

.icon-gift:before {
	content: "\e618"
}

.icon-phone:before {
	content: "\e60f"
}

.icon-clock:before {
	content: "\e619"
}

.icon-home:before {
	content: "\e602"
}

.icon-menu:before {
	content: "\e60d"
}

.icon-message:before {
	content: "\e617"
}

.icon-me:before {
	content: "\e603"
}

.icon-picture:before {
	content: "\e61a"
}

.icon-share:before {
	content: "\e61b"
}

.icon-settings:before {
	content: "\e604"
}

.icon-refresh:before {
	content: "\e61c"
}

.icon-caret:before {
	content: "\e610"
}

.icon-down:before {
	content: "\e611"
}

.icon-up:before {
	content: "\e612"
}

.icon-right:before {
	content: "\e613"
}

.icon-left:before {
	content: "\e614"
}

.icon-check:before {
	content: "\e608"
}

.icon-search:before {
	content: "\e61d"
}

.icon-new:before {
	content: "\e61e"
}

.icon-next, .icon-prev {
	width: .75rem;
	height: .75rem
}

.icon-next {
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2015%2015'%3E%3Cg%3E%3Cpath%20fill%3D'%23007aff'%20d%3D'M1%2C1.6l11.8%2C5.8L1%2C13.4V1.6%20M0%2C0v15l15-7.6L0%2C0L0%2C0z'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E")
}

.icon-prev {
	background-image:
		url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2015%2015'%3E%3Cg%3E%3Cpath%20fill%3D'%23007aff'%20d%3D'M14%2C1.6v11.8L2.2%2C7.6L14%2C1.6%20M15%2C0L0%2C7.6L15%2C15V0L15%2C0z'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E")
}

.theme-dark {
	background-color: #222426
}

.bar.theme-dark, .theme-dark .bar {
	background-color: #131313;
	color: #fff
}

.bar.theme-dark:after, .theme-dark .bar:after {
	background-color: #333
}

.theme-dark .title {
	color: #fff
}

.bar-nav.theme-dark, .bar-tab.theme-dark, .theme-dark .bar-nav,
	.theme-dark .bar-tab {
	background-color: #131313;
	color: #fff
}

.bar-nav.theme-dark:before, .bar-tab.theme-dark:before, .theme-dark .bar-nav:before,
	.theme-dark .bar-tab:before {
	background-color: #333
}

.theme-dark .tab-item {
	color: #fff
}

.theme-dark .tab-item.active {
	color: #0894ec
}

.theme-dark .picker-calendar-week-days {
	color: #fff;
	background-color: #131313
}

.theme-dark .picker-modal.picker-modal-inline .picker-center-highlight:before
	{
	background-color: #333
}

.theme-dark .picker-modal.picker-modal-inline .picker-center-highlight:after
	{
	background-color: #333
}

.theme-dark .picker-modal.picker-modal-inline .picker-item.picker-selected
	{
	color: #fff
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-week-days
	{
	color: #fff
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-day {
	color: #fff
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-day.picker-calendar-day-next,
	.theme-dark .picker-modal.picker-modal-inline .picker-calendar-day.picker-calendar-day-prev
	{
	color: #777
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-day.picker-calendar-day-disabled
	{
	color: #555
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-day.picker-calendar-day-today span
	{
	background: #444
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-row:after,
	.theme-dark .picker-modal.picker-modal-inline .picker-calendar-week-days:after
	{
	background-color: #333
}

.theme-dark .picker-modal.picker-modal-inline .picker-calendar-week-days
	~.picker-calendar-months:before, .theme-dark .picker-modal.picker-modal-inline .toolbar
	~.picker-modal-inner .picker-calendar-months:before {
	background-color: #333
}

.photo-browser.theme-dark .navbar, .photo-browser.theme-dark .toolbar,
	.theme-dark .photo-browser .navbar, .theme-dark .photo-browser .toolbar,
	.theme-dark .view[data-page=photo-browser-slides] .navbar, .theme-dark .view[data-page=photo-browser-slides] .toolbar,
	.view[data-page=photo-browser-slides].theme-dark .navbar, .view[data-page=photo-browser-slides].theme-dark .toolbar
	{
	background: rgba(19, 19, 19, .95)
}

.theme-dark .tabbar a:not (.active ){
	color: #fff
}

.page.theme-dark, .panel.theme-dark, .theme-dark .content, .theme-dark .login-screen-content,
	.theme-dark .page, .theme-dark .panel {
	background-color: #222426;
	color: #ddd
}

.theme-dark .content-block-title {
	color: #fff
}

.content-block.theme-dark, .theme-dark .content-block {
	color: #bbb
}

.theme-dark .content-block-inner {
	background: #1c1d1f;
	color: #ddd
}

.theme-dark .content-block-inner:before {
	background-color: #393939
}

.theme-dark .content-block-inner:after {
	background-color: #393939
}

.list-block.theme-dark ul, .theme-dark .list-block ul {
	background: #1c1d1f
}

.list-block.theme-dark ul:before, .theme-dark .list-block ul:before {
	background-color: #393939
}

.list-block.theme-dark ul:after, .theme-dark .list-block ul:after {
	background-color: #393939
}

.list-block.theme-dark.inset ul, .theme-dark .list-block.inset ul {
	background: #1c1d1f
}

.list-block.theme-dark.notifications>ul, .theme-dark .list-block.notifications>ul
	{
	background: 0 0
}

.list-block.theme-dark .item-subtitle, .list-block.theme-dark .item-title,
	.theme-dark .list-block .item-subtitle, .theme-dark .list-block .item-title
	{
	color: #bbb
}

.theme-dark .card {
	background: #1c1d1f
}

.theme-dark .card-header:after {
	background-color: #393939
}

.theme-dark .card-footer {
	color: #bbb
}

.theme-dark .card-footer:before {
	background-color: #393939
}

.theme-dark li.sorting {
	background-color: #29292f
}

.theme-dark .swipeout-actions-left a, .theme-dark .swipeout-actions-right a
	{
	background-color: #444
}

.theme-dark .item-inner:after, .theme-dark .list-block ul ul li:last-child .item-inner:after
	{
	background-color: #393939
}

.theme-dark .item-after {
	color: #bbb
}

.theme-dark .item-link.active-state, .theme-dark label.label-checkbox.active-state,
	.theme-dark label.label-radio.active-state, html:not (.watch-active-state
	) .theme-dark .item-link:active, html:not (.watch-active-state ) .theme-dark label.label-checkbox:active,
	html:not (.watch-active-state ) .theme-dark label.label-radio:active {
	background-color: #29292f
}

.theme-dark .item-link.list-button:after {
	background-color: #393939
}

.theme-dark .list-block-label {
	color: #bbb
}

.theme-dark .item-divider, .theme-dark .list-group-title {
	background: #1a1a1a;
	color: #bbb
}

.theme-dark .item-divider:before, .theme-dark .list-group-title:before {
	background-color: #393939
}

.theme-dark .searchbar {
	background: #333
}

.theme-dark .searchbar:after {
	background-color: #333
}

.list-block.theme-dark input[type=text], .list-block.theme-dark input[type=password],
	.list-block.theme-dark input[type=email], .list-block.theme-dark input[type=tel],
	.list-block.theme-dark input[type=url], .list-block.theme-dark input[type=date],
	.list-block.theme-dark input[type=datetime-local], .list-block.theme-dark input[type=number],
	.list-block.theme-dark select, .list-block.theme-dark textarea,
	.theme-dark .list-block input[type=text], .theme-dark .list-block input[type=password],
	.theme-dark .list-block input[type=email], .theme-dark .list-block input[type=tel],
	.theme-dark .list-block input[type=url], .theme-dark .list-block input[type=date],
	.theme-dark .list-block input[type=datetime-local], .theme-dark .list-block input[type=number],
	.theme-dark .list-block select, .theme-dark .list-block textarea {
	color: #fff
}

.theme-dark .label-switch .checkbox {
	background-color: #393939
}

.theme-dark .label-switch .checkbox:before {
	background-color: #1c1d1f
}

.theme-dark .range-slider input[type=range]:after {
	background: #1c1d1f
}

.theme-dark .buttons-tab {
	background: #131313
}

.theme-dark .buttons-tab .tab-link:not (.active ){
	color: #ddd
}

.navbar.theme-white, .subnavbar.theme-white, .theme-white .navbar,
	.theme-white .subnavbar {
	background-color: #fff;
	color: #000
}

.navbar.theme-white:after, .subnavbar.theme-white:after, .theme-white .navbar:after,
	.theme-white .subnavbar:after {
	background-color: #ddd
}

.theme-white .toolbar, .toolbar.theme-white {
	background-color: #fff;
	color: #000
}

.theme-white .toolbar:before, .toolbar.theme-white:before {
	background-color: #ddd
}

.theme-white .picker-modal.picker-modal-inline .picker-center-highlight:before
	{
	background-color: #ddd
}

.theme-white .picker-modal.picker-modal-inline .picker-center-highlight:after
	{
	background-color: #ddd
}

.theme-white .picker-modal.picker-modal-inline .picker-calendar-row:after,
	.theme-white .picker-modal.picker-modal-inline .picker-calendar-week-days:after
	{
	background-color: #ddd
}

.theme-white .picker-modal.picker-modal-inline .picker-calendar-week-days
	~.picker-calendar-months:before, .theme-white .picker-modal.picker-modal-inline .toolbar
	~.picker-modal-inner .picker-calendar-months:before {
	background-color: #ddd
}

.photo-browser.theme-white .navbar, .photo-browser.theme-white .toolbar,
	.theme-white .photo-browser .navbar, .theme-white .photo-browser .toolbar,
	.theme-white .view[data-page=photo-browser-slides] .navbar,
	.theme-white .view[data-page=photo-browser-slides] .toolbar, .view[data-page=photo-browser-slides].theme-white .navbar,
	.view[data-page=photo-browser-slides].theme-white .toolbar {
	background: rgba(255, 255, 255, .95)
}

.theme-white .tabbar a:not (.active ){
	color: #777
}

.page.theme-white, .panel.theme-white, .theme-white .login-screen-content,
	.theme-white .page, .theme-white .panel {
	background-color: #fff;
	color: #000
}

.theme-white .content-block-title {
	color: #777
}

.content-block.theme-white, .theme-white .content-block {
	color: #777
}

.theme-white .content-block-inner {
	background: #fafafa;
	color: #000
}

.theme-white .content-block-inner:after {
	background-color: #ddd
}

.theme-white .content-block-inner:before {
	background-color: #ddd
}

.list-block.theme-white ul, .theme-white .list-block ul {
	background: #fff
}

.list-block.theme-white ul:after, .theme-white .list-block ul:after {
	background-color: #ddd
}

.list-block.theme-white ul:before, .theme-white .list-block ul:before {
	background-color: #ddd
}

.list-block.theme-white.inset ul, .theme-white .list-block.inset ul {
	background: #fafafa
}

.list-block.theme-white.notifications>ul, .theme-white .list-block.notifications>ul
	{
	background: 0 0
}

.theme-white li.sorting {
	background-color: #eee
}

.theme-white .swipeout-actions-left a, .theme-white .swipeout-actions-right a
	{
	background-color: #c7c7cc
}

.theme-white .item-inner, .theme-white .list-block ul ul li:last-child .item-inner
	{
	border-color: #ddd
}

.theme-white .item-inner:after, .theme-white .list-block ul ul li:last-child .item-inner:after
	{
	background-color: #ddd
}

.theme-white .item-after {
	color: #8e8e93
}

.theme-white .item-link.active-state, .theme-white label.label-checkbox.active-state,
	.theme-white label.label-radio.active-state, html:not (.watch-active-state
	) .theme-white .item-link:active, html:not (.watch-active-state ) .theme-white label.label-checkbox:active,
	html:not (.watch-active-state ) .theme-white label.label-radio:active {
	background-color: #eee
}

.theme-white .item-link.list-button:after {
	background-color: #ddd
}

.theme-white .list-block-label {
	color: #777
}

.theme-white .item-divider, .theme-white .list-group-title {
	background: #f7f7f7;
	color: #777
}

.theme-white .item-divider:before, .theme-white .list-group-title:before
	{
	background-color: #ddd
}

.theme-white .searchbar {
	background: #c9c9ce
}

.theme-white .searchbar:after {
	background-color: #b4b4b4
}

.list-block.theme-white input[type=text], .list-block.theme-white input[type=password],
	.list-block.theme-white input[type=email], .list-block.theme-white input[type=tel],
	.list-block.theme-white input[type=url], .list-block.theme-white input[type=date],
	.list-block.theme-white input[type=datetime-local], .list-block.theme-white input[type=number],
	.list-block.theme-white select, .list-block.theme-white textarea,
	.theme-white .list-block input[type=text], .theme-white .list-block input[type=password],
	.theme-white .list-block input[type=email], .theme-white .list-block input[type=tel],
	.theme-white .list-block input[type=url], .theme-white .list-block input[type=date],
	.theme-white .list-block input[type=datetime-local], .theme-white .list-block input[type=number],
	.theme-white .list-block select, .theme-white .list-block textarea {
	color: #777
}

.theme-white .label-switch .checkbox {
	background-color: #e5e5e5
}

.theme-white .label-switch .checkbox:before {
	background-color: #fff
}

.theme-white .range-slider input[type=range]:after {
	background: #fff
}