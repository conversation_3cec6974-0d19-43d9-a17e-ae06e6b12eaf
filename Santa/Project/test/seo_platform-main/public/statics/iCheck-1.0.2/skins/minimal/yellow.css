/* iCheck plugin Minimal skin, yellow
----------------------------------- */
.icheckbox_minimal-yellow, .iradio_minimal-yellow {
	display: inline-block;
	*display: inline;
	vertical-align: middle;
	margin: 0;
	padding: 0;
	width: 18px;
	height: 18px;
	background: url(yellow.png) no-repeat;
	border: none;
	cursor: pointer;
}

.icheckbox_minimal-yellow {
	background-position: 0 0;
}

.icheckbox_minimal-yellow.hover {
	background-position: -20px 0;
}

.icheckbox_minimal-yellow.checked {
	background-position: -40px 0;
}

.icheckbox_minimal-yellow.disabled {
	background-position: -60px 0;
	cursor: default;
}

.icheckbox_minimal-yellow.checked.disabled {
	background-position: -80px 0;
}

.iradio_minimal-yellow {
	background-position: -100px 0;
}

.iradio_minimal-yellow.hover {
	background-position: -120px 0;
}

.iradio_minimal-yellow.checked {
	background-position: -140px 0;
}

.iradio_minimal-yellow.disabled {
	background-position: -160px 0;
	cursor: default;
}

.iradio_minimal-yellow.checked.disabled {
	background-position: -180px 0;
}

/* HiDPI support */
@media ( -o-min-device-pixel-ratio : 5/4) , ( -webkit-min-device-pixel-ratio :
		1.25) , ( min-resolution : 120dpi) {
	.icheckbox_minimal-yellow, .iradio_minimal-yellow {
		background-image: url(<EMAIL>);
		-webkit-background-size: 200px 20px;
		background-size: 200px 20px;
	}
}