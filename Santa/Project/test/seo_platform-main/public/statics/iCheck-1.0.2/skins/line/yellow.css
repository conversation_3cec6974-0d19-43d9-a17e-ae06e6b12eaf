/* iCheck plugin Line skin, yellow
----------------------------------- */
.icheckbox_line-yellow, .iradio_line-yellow {
	position: relative;
	display: block;
	margin: 0;
	padding: 5px 15px 5px 38px;
	font-size: 13px;
	line-height: 17px;
	color: #fff;
	background: #FFC414;
	border: none;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	cursor: pointer;
}

.icheckbox_line-yellow .icheck_line-icon, .iradio_line-yellow .icheck_line-icon
	{
	position: absolute;
	top: 50%;
	left: 13px;
	width: 13px;
	height: 11px;
	margin: -5px 0 0 0;
	padding: 0;
	overflow: hidden;
	background: url(line.png) no-repeat;
	border: none;
}

.icheckbox_line-yellow.hover, .icheckbox_line-yellow.checked.hover,
	.iradio_line-yellow.hover {
	background: #FFD34F;
}

.icheckbox_line-yellow.checked, .iradio_line-yellow.checked {
	background: #FFC414;
}

.icheckbox_line-yellow.checked .icheck_line-icon, .iradio_line-yellow.checked .icheck_line-icon
	{
	background-position: -15px 0;
}

.icheckbox_line-yellow.disabled, .iradio_line-yellow.disabled {
	background: #FFE495;
	cursor: default;
}

.icheckbox_line-yellow.disabled .icheck_line-icon, .iradio_line-yellow.disabled .icheck_line-icon
	{
	background-position: -30px 0;
}

.icheckbox_line-yellow.checked.disabled, .iradio_line-yellow.checked.disabled
	{
	background: #FFE495;
}

.icheckbox_line-yellow.checked.disabled .icheck_line-icon,
	.iradio_line-yellow.checked.disabled .icheck_line-icon {
	background-position: -45px 0;
}

/* HiDPI support */
@media ( -o-min-device-pixel-ratio : 5/4) , ( -webkit-min-device-pixel-ratio :
		1.25) , ( min-resolution : 120dpi) {
	.icheckbox_line-yellow .icheck_line-icon, .iradio_line-yellow .icheck_line-icon
		{
		background-image: url(<EMAIL>);
		-webkit-background-size: 60px 13px;
		background-size: 60px 13px;
	}
}