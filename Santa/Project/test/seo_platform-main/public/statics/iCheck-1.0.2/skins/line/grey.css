/* iCheck plugin Line skin, grey
----------------------------------- */
.icheckbox_line-grey, .iradio_line-grey {
	position: relative;
	display: block;
	margin: 0;
	padding: 5px 15px 5px 38px;
	font-size: 13px;
	line-height: 17px;
	color: #fff;
	background: #73716e;
	border: none;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	cursor: pointer;
}

.icheckbox_line-grey .icheck_line-icon, .iradio_line-grey .icheck_line-icon
	{
	position: absolute;
	top: 50%;
	left: 13px;
	width: 13px;
	height: 11px;
	margin: -5px 0 0 0;
	padding: 0;
	overflow: hidden;
	background: url(line.png) no-repeat;
	border: none;
}

.icheckbox_line-grey.hover, .icheckbox_line-grey.checked.hover,
	.iradio_line-grey.hover {
	background: #8B8986;
}

.icheckbox_line-grey.checked, .iradio_line-grey.checked {
	background: #73716e;
}

.icheckbox_line-grey.checked .icheck_line-icon, .iradio_line-grey.checked .icheck_line-icon
	{
	background-position: -15px 0;
}

.icheckbox_line-grey.disabled, .iradio_line-grey.disabled {
	background: #D5D4D3;
	cursor: default;
}

.icheckbox_line-grey.disabled .icheck_line-icon, .iradio_line-grey.disabled .icheck_line-icon
	{
	background-position: -30px 0;
}

.icheckbox_line-grey.checked.disabled, .iradio_line-grey.checked.disabled
	{
	background: #D5D4D3;
}

.icheckbox_line-grey.checked.disabled .icheck_line-icon,
	.iradio_line-grey.checked.disabled .icheck_line-icon {
	background-position: -45px 0;
}

/* HiDPI support */
@media ( -o-min-device-pixel-ratio : 5/4) , ( -webkit-min-device-pixel-ratio :
		1.25) , ( min-resolution : 120dpi) {
	.icheckbox_line-grey .icheck_line-icon, .iradio_line-grey .icheck_line-icon
		{
		background-image: url(<EMAIL>);
		-webkit-background-size: 60px 13px;
		background-size: 60px 13px;
	}
}