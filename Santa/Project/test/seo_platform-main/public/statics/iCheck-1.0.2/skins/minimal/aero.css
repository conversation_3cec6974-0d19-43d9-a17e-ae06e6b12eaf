/* iCheck plugin Minimal skin, aero
----------------------------------- */
.icheckbox_minimal-aero, .iradio_minimal-aero {
	display: inline-block;
	*display: inline;
	vertical-align: middle;
	margin: 0;
	padding: 0;
	width: 18px;
	height: 18px;
	background: url(aero.png) no-repeat;
	border: none;
	cursor: pointer;
}

.icheckbox_minimal-aero {
	background-position: 0 0;
}

.icheckbox_minimal-aero.hover {
	background-position: -20px 0;
}

.icheckbox_minimal-aero.checked {
	background-position: -40px 0;
}

.icheckbox_minimal-aero.disabled {
	background-position: -60px 0;
	cursor: default;
}

.icheckbox_minimal-aero.checked.disabled {
	background-position: -80px 0;
}

.iradio_minimal-aero {
	background-position: -100px 0;
}

.iradio_minimal-aero.hover {
	background-position: -120px 0;
}

.iradio_minimal-aero.checked {
	background-position: -140px 0;
}

.iradio_minimal-aero.disabled {
	background-position: -160px 0;
	cursor: default;
}

.iradio_minimal-aero.checked.disabled {
	background-position: -180px 0;
}

/* HiDPI support */
@media ( -o-min-device-pixel-ratio : 5/4) , ( -webkit-min-device-pixel-ratio :
		1.25) , ( min-resolution : 120dpi) {
	.icheckbox_minimal-aero, .iradio_minimal-aero {
		background-image: url(<EMAIL>);
		-webkit-background-size: 200px 20px;
		background-size: 200px 20px;
	}
}