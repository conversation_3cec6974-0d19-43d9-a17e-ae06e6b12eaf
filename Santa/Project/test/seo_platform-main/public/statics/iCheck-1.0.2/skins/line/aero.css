/* iCheck plugin Line skin, aero
----------------------------------- */
.icheckbox_line-aero, .iradio_line-aero {
	position: relative;
	display: block;
	margin: 0;
	padding: 5px 15px 5px 38px;
	font-size: 13px;
	line-height: 17px;
	color: #fff;
	background: #9cc2cb;
	border: none;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	cursor: pointer;
}

.icheckbox_line-aero .icheck_line-icon, .iradio_line-aero .icheck_line-icon
	{
	position: absolute;
	top: 50%;
	left: 13px;
	width: 13px;
	height: 11px;
	margin: -5px 0 0 0;
	padding: 0;
	overflow: hidden;
	background: url(line.png) no-repeat;
	border: none;
}

.icheckbox_line-aero.hover, .icheckbox_line-aero.checked.hover,
	.iradio_line-aero.hover {
	background: #B5D1D8;
}

.icheckbox_line-aero.checked, .iradio_line-aero.checked {
	background: #9cc2cb;
}

.icheckbox_line-aero.checked .icheck_line-icon, .iradio_line-aero.checked .icheck_line-icon
	{
	background-position: -15px 0;
}

.icheckbox_line-aero.disabled, .iradio_line-aero.disabled {
	background: #D2E4E8;
	cursor: default;
}

.icheckbox_line-aero.disabled .icheck_line-icon, .iradio_line-aero.disabled .icheck_line-icon
	{
	background-position: -30px 0;
}

.icheckbox_line-aero.checked.disabled, .iradio_line-aero.checked.disabled
	{
	background: #D2E4E8;
}

.icheckbox_line-aero.checked.disabled .icheck_line-icon,
	.iradio_line-aero.checked.disabled .icheck_line-icon {
	background-position: -45px 0;
}

/* HiDPI support */
@media ( -o-min-device-pixel-ratio : 5/4) , ( -webkit-min-device-pixel-ratio :
		1.25) , ( min-resolution : 120dpi) {
	.icheckbox_line-aero .icheck_line-icon, .iradio_line-aero .icheck_line-icon
		{
		background-image: url(<EMAIL>);
		-webkit-background-size: 60px 13px;
		background-size: 60px 13px;
	}
}