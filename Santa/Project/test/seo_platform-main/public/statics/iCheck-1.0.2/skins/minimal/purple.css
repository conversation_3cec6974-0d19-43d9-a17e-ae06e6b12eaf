/* iCheck plugin Minimal skin, purple
----------------------------------- */
.icheckbox_minimal-purple, .iradio_minimal-purple {
	display: inline-block;
	*display: inline;
	vertical-align: middle;
	margin: 0;
	padding: 0;
	width: 18px;
	height: 18px;
	background: url(purple.png) no-repeat;
	border: none;
	cursor: pointer;
}

.icheckbox_minimal-purple {
	background-position: 0 0;
}

.icheckbox_minimal-purple.hover {
	background-position: -20px 0;
}

.icheckbox_minimal-purple.checked {
	background-position: -40px 0;
}

.icheckbox_minimal-purple.disabled {
	background-position: -60px 0;
	cursor: default;
}

.icheckbox_minimal-purple.checked.disabled {
	background-position: -80px 0;
}

.iradio_minimal-purple {
	background-position: -100px 0;
}

.iradio_minimal-purple.hover {
	background-position: -120px 0;
}

.iradio_minimal-purple.checked {
	background-position: -140px 0;
}

.iradio_minimal-purple.disabled {
	background-position: -160px 0;
	cursor: default;
}

.iradio_minimal-purple.checked.disabled {
	background-position: -180px 0;
}

/* HiDPI support */
@media ( -o-min-device-pixel-ratio : 5/4) , ( -webkit-min-device-pixel-ratio :
		1.25) , ( min-resolution : 120dpi) {
	.icheckbox_minimal-purple, .iradio_minimal-purple {
		background-image: url(<EMAIL>);
		-webkit-background-size: 200px 20px;
		background-size: 200px 20px;
	}
}