malihu custom scrollbar plugin
================================

Highly customizable custom scrollbar jQuery plugin. Features include: 

* Vertical and/or horizontal scrollbar(s)  
* Adjustable scrolling momentum 
* Mouse-wheel, keyboard and touch support 
* Ready-to-use themes and customization via CSS 
* RTL direction support 
* Option parameters for full control of scrollbar functionality 
* Methods for triggering actions like scroll-to, update, destroy etc. 
* User-defined callbacks 
* Selectable/searchable content

#### Installation

Bower: `bower install malihu-custom-scrollbar-plugin` 

npm: `npm install malihu-custom-scrollbar-plugin` 

#### Usage 

`$(selector).mCustomScrollbar();` 

###### Using with [Browserify](http://browserify.org/)

    var $ = require('jquery');
    require('malihu-custom-scrollbar-plugin')($);


#### For more information 

* [Plugin homepage and documentation](http://manos.malihu.gr/jquery-custom-content-scroller) 

Requirements
-------------------------

jQuery version **1.6.0** or higher

Browser compatibility
-------------------------

* Internet Explorer 8+ 
* Firefox 
* Chrome 
* Opera 
* Safari  
* iOS 
* Android 
* Windows Phone

License 
-------------------------

MIT License (MIT)

http://opensource.org/licenses/MIT

Donate 
-------------------------

https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=UYJ5G65M6ZA28