<!DOCTYPE html>
<!--[if IE 8 ]><html lang="en" class="ie8"><![endif]-->
<!--[if IE 9 ]><html lang="en" class="ie9"><![endif]-->
<!--[if (gt IE 9)|!(IE)]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>jQuery custom scrollbar demo</title>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<!-- stylesheet for demo and examples -->
<link rel="stylesheet" href="style.css">
<!--[if lt IE 9]>
	<script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>
	<![endif]-->

<!-- custom scrollbar stylesheet -->
<link rel="stylesheet" href="../jquery.mCustomScrollbar.css">

<!-- Google CDN jQuery UI stylesheet -->
<link rel="stylesheet"
	href="http://ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css" />

</head>

<body>
	<header>
		<a href="http://manos.malihu.gr/jquery-custom-content-scroller/"
			class="logo"><img src="logo.png" alt="jQuery custom scrollbar" /></a>
		<hr />
	</header>

	<div id="demo">
		<section id="examples" class="accordion">
			<h2>Scrollbar for each accordion content</h2>

			<!-- accordion -->
			<div id="accordion">
				<h3>Section 1</h3>
				<div>
					<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit,
						sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
						Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris
						nisi ut aliquip ex ea commodo consequat.</p>
					<p>Duis aute irure dolor in reprehenderit in voluptate velit
						esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
						occaecat cupidatat non proident, sunt in culpa qui officia
						deserunt mollit anim id est laborum.</p>
					<p>Sed ut perspiciatis unde omnis iste natus error sit
						voluptatem accusantium doloremque laudantium, totam rem aperiam,
						eaque ipsa quae ab illo inventore veritatis et quasi architecto
						beatae vitae dicta sunt explicabo.</p>
					<p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut
						odit aut fugit, sed quia consequuntur magni dolores eos qui
						ratione voluptatem sequi nesciunt.</p>
					<p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit
						amet, consectetur, adipisci velit, sed quia non numquam eius modi
						tempora incidunt ut labore et dolore magnam aliquam quaerat
						voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem
						ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi
						consequatur?</p>
					<p>Quis autem vel eum iure reprehenderit qui in ea voluptate
						velit esse quam nihil molestiae consequatur, vel illum qui dolorem
						eum fugiat quo voluptas nulla pariatur?</p>
					<p>At vero eos et accusamus et iusto odio dignissimos ducimus
						qui blanditiis praesentium voluptatum deleniti atque corrupti quos
						dolores et quas molestias excepturi sint occaecati cupiditate non
						provident, similique sunt in culpa qui officia deserunt mollitia
						animi, id est laborum et dolorum fuga. Et harum quidem rerum
						facilis est et expedita distinctio.</p>
					<p>End of panel content.</p>
				</div>
				<h3>Section 2</h3>
				<div>
					<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit,
						sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
						Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris
						nisi ut aliquip ex ea commodo consequat.</p>
					<p>Duis aute irure dolor in reprehenderit in voluptate velit
						esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
						occaecat cupidatat non proident, sunt in culpa qui officia
						deserunt mollit anim id est laborum.</p>
					<p>Sed ut perspiciatis unde omnis iste natus error sit
						voluptatem accusantium doloremque laudantium, totam rem aperiam,
						eaque ipsa quae ab illo inventore veritatis et quasi architecto
						beatae vitae dicta sunt explicabo.</p>
					<p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut
						odit aut fugit, sed quia consequuntur magni dolores eos qui
						ratione voluptatem sequi nesciunt.</p>
					<p>End of panel content.</p>
				</div>
				<h3>Section 3</h3>
				<div>
					<p>Quis autem vel eum iure reprehenderit qui in ea voluptate
						velit esse quam nihil molestiae consequatur, vel illum qui dolorem
						eum fugiat quo voluptas nulla pariatur?</p>
					<p>At vero eos et accusamus et iusto odio dignissimos ducimus
						qui blanditiis praesentium voluptatum deleniti atque corrupti quos
						dolores et quas molestias excepturi sint occaecati cupiditate non
						provident, similique sunt in culpa qui officia deserunt mollitia
						animi, id est laborum et dolorum fuga. Et harum quidem rerum
						facilis est et expedita distinctio.</p>
					<p>Nam libero tempore, cum soluta nobis est eligendi optio
						cumque nihil impedit quo minus id quod maxime placeat facere
						possimus, omnis voluptas assumenda est, omnis dolor repellendus.</p>
					<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit,
						sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
						Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris
						nisi ut aliquip ex ea commodo consequat.</p>
					<p>End of panel content.</p>
				</div>
			</div>

		</section>
	</div>

	<footer>
		<hr />
		<p>
			<a href="http://manos.malihu.gr/jquery-custom-content-scroller">Plugin
				home</a> <a
				href="https://github.com/malihu/malihu-custom-scrollbar-plugin">Project
				on Github</a> <a href="http://opensource.org/licenses/MIT">MIT
				License</a>
		</p>
	</footer>

	<!-- Google CDN jQuery with fallback to local -->
	<script
		src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
	<script>window.jQuery || document.write('<script src="../js/minified/jquery-1.11.0.min.js"><\/script>')</script>

	<!-- Google CDN jQuery UI with fallback to local -->
	<script
		src="http://ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js"></script>
	<script>window.jQuery.ui || document.write('<script src="../js/minified/jquery-ui-1.10.4.min.js"><\/script>')</script>

	<!-- custom scrollbar plugin -->
	<script src="../jquery.mCustomScrollbar.concat.min.js"></script>

	<script>
		(function($){
			$(window).load(function(){
							
				$("#accordion").accordion({
					create:function(e,ui){
						/* call mCustomScrollbar function on each accordion content upon accordion creation */
						$(".ui-accordion-content").mCustomScrollbar({
							setHeight:300,
							theme:"dark-thick"
						});
					}
				});
				
			});
		})(jQuery);
	</script>
</body>
</html>