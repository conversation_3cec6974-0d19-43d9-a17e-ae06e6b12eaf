<!DOCTYPE html>
<!--[if IE 8 ]><html lang="en" class="ie8"><![endif]-->
<!--[if IE 9 ]><html lang="en" class="ie9"><![endif]-->
<!--[if (gt IE 9)|!(IE)]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>jQuery custom scrollbar demo</title>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<!-- stylesheet for demo and examples -->
<link rel="stylesheet" href="style.css">
<!--[if lt IE 9]>
	<script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>
	<![endif]-->

<!-- custom scrollbar stylesheet -->
<link rel="stylesheet" href="../jquery.mCustomScrollbar.css">

<!-- Google CDN jQuery UI stylesheet -->
<link rel="stylesheet"
	href="http://ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css" />

</head>

<body>
	<header>
		<a href="http://manos.malihu.gr/jquery-custom-content-scroller/"
			class="logo"><img src="logo.png" alt="jQuery custom scrollbar" /></a>
		<hr />
	</header>

	<div id="demo">
		<section id="examples" class="sortable">

			<!-- content -->
			<div id="sortable" class="content">
				<h2>Sortable content</h2>
				<hr />
				<ul>
					<li>Item 1</li>
					<li>Item 2</li>
					<li>Item 3</li>
					<li>Item 4</li>
					<li>Item 5</li>
					<li>Item 6</li>
					<li>Item 7</li>
					<li>Item 8</li>
					<li>Item 9</li>
					<li>Item 10</li>
					<li>Item 11</li>
					<li>Item 12</li>
					<li>Item 13</li>
					<li>Item 14</li>
					<li>Item 15</li>
					<li>Item 16</li>
					<li>Item 17</li>
					<li>Item 18</li>
					<li>Item 19</li>
					<li>Item 20</li>
				</ul>
				<hr />
				<p>End of content.</p>
			</div>

		</section>
	</div>

	<footer>
		<hr />
		<p>
			<a href="http://manos.malihu.gr/jquery-custom-content-scroller">Plugin
				home</a> <a
				href="https://github.com/malihu/malihu-custom-scrollbar-plugin">Project
				on Github</a> <a href="http://opensource.org/licenses/MIT">MIT
				License</a>
		</p>
	</footer>

	<!-- Google CDN jQuery with fallback to local -->
	<script
		src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
	<script>window.jQuery || document.write('<script src="../js/minified/jquery-1.11.0.min.js"><\/script>')</script>

	<!-- Google CDN jQuery UI with fallback to local -->
	<script
		src="http://ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js"></script>
	<script>window.jQuery.ui || document.write('<script src="../js/minified/jquery-ui-1.10.4.min.js"><\/script>')</script>

	<!-- custom scrollbar plugin -->
	<script src="../jquery.mCustomScrollbar.concat.min.js"></script>

	<script>
		(function($){
			$(window).load(function(){
				
				/* call mCustomScrollbar function before jquery ui sortable() */
				
				$("#sortable").mCustomScrollbar({
					scrollbarPosition:"outside",
					scrollInertia:450,
					theme:"light-2"
				});
							
				$("#sortable ul").sortable({
					axis:"y",
					cursor:"move",
					tolerance:"intersect",
					change:function(e,ui){
						var h=ui.helper.outerHeight(true),
							elem=$("#sortable .mCustomScrollBox"),
							elemHeight=elem.height(),
							moveBy=$("#sortable li").outerHeight(true)*3,
							mouseCoordsY=e.pageY-elem.offset().top;
						if(mouseCoordsY<h){
							$("#sortable").mCustomScrollbar("scrollTo","+="+moveBy);
						}else if(mouseCoordsY>elemHeight-h){
							$("#sortable").mCustomScrollbar("scrollTo","-="+moveBy);
						}
					}
				});
				
			});
		})(jQuery);
	</script>
</body>
</html>