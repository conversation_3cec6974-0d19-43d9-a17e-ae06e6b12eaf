<!DOCTYPE html>
<!--[if IE 8 ]><html lang="en" class="ie8"><![endif]-->
<!--[if IE 9 ]><html lang="en" class="ie9"><![endif]-->
<!--[if (gt IE 9)|!(IE)]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>jQuery custom scrollbar demo</title>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<!-- stylesheet for demo and examples -->
<link rel="stylesheet" href="style.css">
<!--[if lt IE 9]>
	<script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>
	<![endif]-->

<!-- custom scrollbar stylesheet -->
<link rel="stylesheet" href="../jquery.mCustomScrollbar.css">

</head>

<body>
	<header>
		<a href="http://manos.malihu.gr/jquery-custom-content-scroller/"
			class="logo"><img src="logo.png" alt="jQuery custom scrollbar" /></a>
		<hr />
	</header>

	<div id="demo">
		<section id="examples" class="textarea-example">

			<!-- content -->
			<div id="content-1" class="content">
				<h2>textarea example</h2>
				<hr />
				<form>
					<!-- wrap textarea in a .textarea-wrapper div -->
					<div class="textarea-wrapper">
						<textarea>Start typing...</textarea>
						<!-- add an extra .textarea-clone div -->
						<div class="textarea-clone"></div>
					</div>
				</form>
				<hr />
			</div>

		</section>
	</div>

	<footer>
		<hr />
		<p>
			<a href="http://manos.malihu.gr/jquery-custom-content-scroller">Plugin
				home</a> <a
				href="https://github.com/malihu/malihu-custom-scrollbar-plugin">Project
				on Github</a> <a href="http://opensource.org/licenses/MIT">MIT
				License</a>
		</p>
	</footer>

	<!-- Google CDN jQuery with fallback to local -->
	<script
		src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
	<script>window.jQuery || document.write('<script src="../js/minified/jquery-1.11.0.min.js"><\/script>')</script>

	<!-- custom scrollbar plugin -->
	<script src="../jquery.mCustomScrollbar.concat.min.js"></script>

	<script>
		(function($){
			$(window).load(function(){
				
				var textareaLineHeight=parseInt($(".textarea-wrapper textarea").css("line-height"));
				
				$(".textarea-wrapper").mCustomScrollbar({
					scrollInertia:0,
					theme:"dark-3",
					advanced:{autoScrollOnFocus:false},
					keyboard:{enable:false},
					snapAmount:textareaLineHeight
				});
				
				var textarea=$(".textarea-wrapper textarea"),textareaWrapper=$(".textarea-wrapper"),textareaClone=$(".textarea-wrapper .textarea-clone");
				
				textarea.bind("keyup keydown",function(e){
        			var $this=$(this),textareaContent=$this.val(),clength=textareaContent.length,cursorPosition=textarea.getCursorPosition();
					textareaContent="<span>"+textareaContent.substr(0,cursorPosition)+"</span>"+textareaContent.substr(cursorPosition,textareaContent.length);
					textareaContent=textareaContent.replace(/\n/g,"<br />");
        			textareaClone.html(textareaContent+"<br />");
        			$this.css("height",textareaClone.height());
					var textareaCloneSpan=textareaClone.children("span"),textareaCloneSpanOffset=0,
						viewLimitBottom=(parseInt(textareaClone.css("min-height")))-textareaCloneSpanOffset,viewLimitTop=textareaCloneSpanOffset,
						viewRatio=Math.round(textareaCloneSpan.height()+textareaWrapper.find(".mCSB_container").position().top);
					if(viewRatio>viewLimitBottom || viewRatio<viewLimitTop){
						if((textareaCloneSpan.height()-textareaCloneSpanOffset)>0){
							textareaWrapper.mCustomScrollbar("scrollTo",textareaCloneSpan.height()-textareaCloneSpanOffset-textareaLineHeight);
						}else{
							textareaWrapper.mCustomScrollbar("scrollTo","top");
						}
					}
    			});
    			
    			$.fn.getCursorPosition=function(){
        			var el=$(this).get(0),pos=0;
        			if("selectionStart" in el){
            			pos=el.selectionStart;
        			}else if("selection" in document){
            			el.focus();
            			var sel=document.selection.createRange(),selLength=document.selection.createRange().text.length;
            			sel.moveStart("character",-el.value.length);
            			pos=sel.text.length-selLength;
        			}
        			return pos;
    			}
				
			});
		})(jQuery);
	</script>
</body>
</html>