!function(){function e(e){return e.charAt(0).toUpperCase()+e.slice(1)}function t(e){var t=e.direction;if("string"==typeof t){var i="DIRECTION_"+t.toUpperCase();o.indexOf(t)>-1&&r.hasOwnProperty(i)?e.direction=r[i]:console.warn("[vue-touch] invalid direction: "+t)}}var i={},r="function"==typeof require?require("hammerjs"):window.Hammer,n=["tap","pan","pinch","press","rotate","swipe"],o=["up","down","left","right","horizontal","vertical","all"],a={};if(!r)throw new Error("[vue-touch] cannot locate Hammer.js.");i.config={},i.install=function(o){o.directive("touch",{isFn:!0,acceptStatement:!0,priority:o.directive("on").priority,bind:function(){this.el.hammer||(this.el.hammer=new r.Manager(this.el));var o=this.mc=this.el.hammer,s=this.arg;s||console.warn("[vue-touch] event type argument is required.");var h,c;if(a[s]){var u=a[s];h=u.type,c=new(r[e(h)])(u),c.recognizeWith(o.recognizers),o.add(c)}else{for(var d=0;d<n.length;d++)if(0===s.indexOf(n[d])){h=n[d];break}if(!h)return void console.warn("[vue-touch] invalid event type: "+s);c=o.get(h),c||(c=new(r[e(h)]),c.recognizeWith(o.recognizers),o.add(c));var f=i.config[h];f&&(t(f),c.set(f));var l=this.el.hammerOptions&&this.el.hammerOptions[h];l&&(t(l),c.set(l))}this.recognizer=c},update:function(e){var t=this.mc,i=this.arg;this.handler&&t.off(i,this.handler),"function"!=typeof e?console.warn("[vue-touch] invalid handler function for v-touch: "+this.arg+'="'+this.descriptor.raw):t.on(i,this.handler=e)},unbind:function(){this.mc.off(this.arg,this.handler),Object.keys(this.mc.handlers).length||(this.mc.destroy(),this.el.hammer=null)}}),o.directive("touch-options",{priority:o.directive("on").priority+1,update:function(e){var t=this.el.hammerOptions||(this.el.hammerOptions={});this.arg?t[this.arg]=e:console.warn("[vue-touch] recognizer type argument for v-touch-options is required.")}})},i.registerCustomEvent=function(e,t){t.event=e,a[e]=t},"object"==typeof exports?module.exports=i:"function"==typeof define&&define.amd?define([],function(){return i}):window.Vue&&(window.VueTouch=i,Vue.use(i))}();
