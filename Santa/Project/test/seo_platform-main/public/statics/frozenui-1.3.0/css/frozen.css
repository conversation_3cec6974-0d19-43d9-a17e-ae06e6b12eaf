@charset "UTF-8"; 

@media screen and (max-width:319px) {
	html {
		font-size: 85.33333px
	}
}

@media screen and (min-width:320px) and (max-width:359px) {
	html {
		font-size: 85.33333px
	}
}

@media screen and (min-width:360px) and (max-width:374px) {
	html {
		font-size: 96px
	}
}

@media screen and (min-width:375px) and (max-width:383px) {
	html {
		font-size: 100px
	}
}

@media screen and (min-width:384px) and (max-width:399px) {
	html {
		font-size: 102.4px
	}
}

@media screen and (min-width:400px) and (max-width:413px) {
	html {
		font-size: 106.66667px
	}
}

@media screen and (min-width:414px) {
	html {
		font-size: 110.4px
	}
}

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code,
	form, fieldset, legend, input, textarea, p, blockquote, th, td, header,
	hgroup, nav, section, article, aside, footer, figure, figcaption, menu,
	button {
	margin: 0;
	padding: 0
}

body {
	font-family: "Helvetica Neue", Helvetica, STHeiTi, sans-serif;
	line-height: 1.5;
	font-size: 16px;
	color: #000;
	background-color: #f8f8f8;
	-webkit-user-select: none;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: transparent;
	outline: 0
}

h1, h2, h3, h4, h5, h6 {
	font-size: 100%;
	font-weight: 400
}

table {
	border-collapse: collapse;
	border-spacing: 0
}

caption, th {
	text-align: left
}

fieldset, img {
	border: 0
}

li {
	list-style: none
}

ins {
	text-decoration: none
}

del {
	text-decoration: line-through
}

input, button, textarea, select, optgroup, option {
	font-family: inherit;
	font-size: inherit;
	font-style: inherit;
	font-weight: inherit;
	outline: 0
}

button {
	-webkit-appearance: none;
	border: 0;
	background: 0 0
}

a {
	-webkit-touch-callout: none;
	text-decoration: none
}

:focus {
	outline: 0;
	-webkit-tap-highlight-color: transparent
}

em, i {
	font-style: normal
}

@font-face {
	font-family: iconfont;
	src: url(../font/iconfont.ttf) format("truetype")
}

.ui-icon, [class^=ui-icon-] {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5)
}

.ui-icon-close:before {
	content: ""
}

.ui-icon-search:before {
	content: ""
}

.ui-icon-return:before {
	content: ""
}

.ui-icon-close, .ui-icon-search {
	color: #8e8e93
}

@font-face {
	font-family: iconfont;
	src: url(../font/iconfont-full.ttf) format("truetype")
}

.ui-icon, [class^=ui-icon-] {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5)
}

.ui-icon-add:before {
	content: "\f615"
}

.ui-icon-more:before {
	content: "\f616"
}

.ui-icon-arrow:before {
	content: "\f600"
}

.ui-icon-return:before {
	content: "\f614"
}

.ui-icon-checked:before {
	content: "\f601"
}

.ui-icon-checked-s:before {
	content: "\f602"
}

.ui-icon-info-block:before {
	content: "\f603"
}

.ui-icon-success-block:before {
	content: "\f604"
}

.ui-icon-warn-block:before {
	content: "\f605"
}

.ui-icon-info:before {
	content: "\f606"
}

.ui-icon-success:before {
	content: "\f607"
}

.ui-icon-warn:before {
	content: "\f608"
}

.ui-icon-next:before {
	content: "\f617"
}

.ui-icon-prev:before {
	content: "\f618"
}

.ui-icon-tag:before {
	content: "\f60d"
}

.ui-icon-tag-pop:before {
	content: "\f60f"
}

.ui-icon-tag-s:before {
	content: "\f60e"
}

.ui-icon-warn-lg:before {
	content: "\f609"
}

.ui-icon-close:before {
	content: "\f60a"
}

.ui-icon-close-progress:before {
	content: "\f619"
}

.ui-icon-close-page:before {
	content: "\f60b"
}

.ui-icon-emo:before {
	content: "\f61a"
}

.ui-icon-delete:before {
	content: "\f61b"
}

.ui-icon-search:before {
	content: "\f60c"
}

.ui-icon-order:before {
	content: "\f61c"
}

.ui-icon-news:before {
	content: "\f61d"
}

.ui-icon-personal:before {
	content: "\f61e"
}

.ui-icon-dressup:before {
	content: "\f61f"
}

.ui-icon-cart:before {
	content: "\f620"
}

.ui-icon-history:before {
	content: "\f621"
}

.ui-icon-wallet:before {
	content: "\f622"
}

.ui-icon-refresh:before {
	content: "\f623"
}

.ui-icon-thumb:before {
	content: "\f624"
}

.ui-icon-file:before {
	content: "\f625"
}

.ui-icon-hall:before {
	content: "\f626"
}

.ui-icon-voice:before {
	content: "\f627"
}

.ui-icon-unfold:before {
	content: "\f628"
}

.ui-icon-gototop:before {
	content: "\f629"
}

.ui-icon-share:before {
	content: "\f62a"
}

.ui-icon-home:before {
	content: "\f62b"
}

.ui-icon-pin:before {
	content: "\f62c"
}

.ui-icon-star:before {
	content: "\f62d"
}

.ui-icon-bugle:before {
	content: "\f62e"
}

.ui-icon-trend:before {
	content: "\f62f"
}

.ui-icon-unchecked:before {
	content: "\f610"
}

.ui-icon-unchecked-s:before {
	content: "\f611"
}

.ui-icon-play-active:before {
	content: "\f630"
}

.ui-icon-stop-active:before {
	content: "\f631"
}

.ui-icon-play:before {
	content: "\f632"
}

.ui-icon-stop:before {
	content: "\f633"
}

.ui-icon-set:before {
	content: "\f634"
}

.ui-icon-add-group:before {
	content: "\f635"
}

.ui-icon-add-people:before {
	content: "\f636"
}

.ui-icon-pc:before {
	content: "\f637"
}

.ui-icon-scan:before {
	content: "\f638"
}

.ui-icon-tag-svip:before {
	content: "\f613"
}

.ui-icon-tag-vip:before {
	content: "\f612"
}

.ui-icon-male:before {
	content: "\f639"
}

.ui-icon-female:before {
	content: "\f63a"
}

.ui-icon-collect:before {
	content: "\f63b"
}

.ui-icon-commented:before {
	content: "\f63c"
}

.ui-icon-like:before {
	content: "\f63d"
}

.ui-icon-liked:before {
	content: "\f63e"
}

.ui-icon-comment:before {
	content: "\f63f"
}

.ui-icon-collected:before {
	content: "\f640"
}

a {
	color: #00a5e0
}

em {
	color: #ff8444
}

::-webkit-input-placeholder {
	color: #bbb
}

h1 {
	font-size: 18px
}

h2 {
	font-size: 17px
}

h3, h4 {
	font-size: 16px
}

h5, .ui-txt-sub {
	font-size: 14px
}

h6, .ui-txt-tips {
	font-size: 12px
}

.ui-txt-default {
	color: #000
}

.ui-txt-white {
	color: #fff
}

.ui-txt-info {
	color: #777
}

.ui-txt-muted {
	color: #bbb
}

.ui-txt-warning, .ui-txt-red {
	color: #ff4222
}

.ui-txt-feeds {
	color: #314c83
}

.ui-txt-highlight {
	color: #ff8444
}

.ui-txt-justify {
	text-align: justify
}

.ui-txt-justify-one {
	text-align: justify;
	overflow: hidden;
	height: 24px
}

.ui-txt-justify-one:after {
	display: inline-block;
	content: '';
	overflow: hidden;
	width: 100%;
	height: 0
}

.ui-border-t {
	border-top: 1px solid #e0e0e0
}

.ui-border-b {
	border-bottom: 1px solid #e0e0e0
}

.ui-border-tb {
	border-top: #e0e0e0 1px solid;
	border-bottom: #e0e0e0 1px solid;
	background-image: none
}

.ui-border-l {
	border-left: 1px solid #e0e0e0
}

.ui-border-r {
	border-right: 1px solid #e0e0e0
}

.ui-border {
	border: 1px solid #e0e0e0
}

.ui-border-radius {
	border: 1px solid #e0e0e0;
	border-radius: 4px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-border-radius {
		position: relative;
		border: 0
	}
	.ui-border-radius:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #e0e0e0;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 8px;
		pointer-events: none
	}
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-border {
		position: relative;
		border: 0
	}
	.ui-border-t, .ui-border-b, .ui-border-l, .ui-border-r, .ui-border-tb {
		border: 0
	}
	.ui-border-t {
		background-position: left top;
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0))
	}
	.ui-border-b {
		background-position: left bottom;
		background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0))
	}
	.ui-border-t, .ui-border-b, .ui-border-tb {
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}
	.ui-border-tb {
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0)),
			-webkit-gradient(linear, left top, left bottom, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		background-position: top, bottom
	}
	.ui-border-l {
		background-position: left top;
		background-image: -webkit-gradient(linear, right top, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0))
	}
	.ui-border-r {
		background-position: right top;
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0))
	}
	.ui-border-l, .ui-border-r {
		background-repeat: repeat-y;
		-webkit-background-size: 1px 100%
	}
	.ui-border:after {
		content: "";
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0)),
			-webkit-gradient(linear, left top, right top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0)),
			-webkit-gradient(linear, left top, left bottom, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0)),
			-webkit-gradient(linear, right top, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		-webkit-background-size: 100% 1px, 1px 100%, 100% 1px, 1px 100%;
		background-size: 100% 1px, 1px 100%, 100% 1px, 1px 100%;
		-webkit-background-size: 100% 1px, 1px 100%, 100% 1px, 1px 100%;
		background-size: 100% 1px, 1px 100%, 100% 1px, 1px 100%;
		background-repeat: no-repeat;
		background-position: top, right, bottom, left;
		padding: 1px;
		-webkit-box-sizing: border-box;
		z-index: 10;
		pointer-events: none
	}
}

.ui-arrowlink {
	position: relative
}

.ui-arrowlink:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	color: #c7c7c7;
	content: "";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media ( max-width :320px) {
	.ui-arrowlink:before {
		right: 10px
	}
}

.ui-arrowlink.active {
	background: #e5e6e7
}

.ui-nowrap {
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-nowrap-flex {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	-webkit-box-flex: 1;
	height: inherit
}

.ui-nowrap-multi {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2
}

.ui-placehold-wrap {
	padding-top: 31.25%;
	position: relative
}

.ui-placehold {
	color: #bbb;
	position: absolute;
	top: 0;
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-sizing: border-box;
	text-align: center;
	height: 100%;
	z-index: -1
}

.ui-placehold-img {
	padding-top: 31.25%;
	position: relative
}

.ui-placehold-img>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-placehold-img img {
	width: 100%;
	height: 100%
}

.ui-grid, .ui-grid-trisect, .ui-grid-halve {
	padding-left: 15px;
	padding-right: 10px;
	overflow: hidden;
	padding-top: 10px
}

@media ( max-width :320px) {
	.ui-grid, .ui-grid-trisect, .ui-grid-halve {
		padding-left: 10px;
		padding-right: 5px
	}
}

.ui-grid li, .ui-grid-trisect li, .ui-grid-halve li {
	padding-right: 5px;
	padding-bottom: 10px;
	float: left;
	position: relative;
	-webkit-box-sizing: border-box
}

.ui-grid-trisect>li {
	width: 33.3333%
}

.ui-grid-trisect-img {
	padding-top: 149.47%
}

.ui-grid-trisect h4 {
	position: relative;
	margin: 7px 0 3px
}

.ui-grid-trisect h4 span {
	display: inline-block;
	margin-left: 12px;
	color: #777
}

.ui-grid-halve>li {
	width: 50%
}

.ui-grid-halve-img {
	padding-top: 55.17%
}

.ui-grid-trisect-img, .ui-grid-halve-img {
	position: relative;
	width: 100%
}

.ui-grid-trisect-img>span, .ui-grid-halve-img>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-grid-trisect-img img, .ui-grid-halve-img img {
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0
}

.ui-grid-trisect-img.active, .ui-grid-halve-img.active {
	opacity: .5
}

.ui-row {
	display: block;
	overflow: hidden
}

.ui-col {
	float: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%
}

.ui-col-10 {
	width: 10%
}

.ui-col-20 {
	width: 20%
}

.ui-col-25 {
	width: 25%
}

.ui-col-33 {
	width: 33.3333%
}

.ui-col-50 {
	width: 50%
}

.ui-col-67 {
	width: 66.6666%
}

.ui-col-75 {
	width: 75%
}

.ui-col-80 {
	width: 80%
}

.ui-col-90 {
	width: 90%
}

.ui-row-flex {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box
}

.ui-row-flex .ui-col {
	float: none;
	-webkit-box-flex: 1;
	width: 0
}

.ui-row-flex .ui-col-2 {
	-webkit-box-flex: 2
}

.ui-row-flex .ui-col-3 {
	-webkit-box-flex: 3
}

.ui-row-flex .ui-col-4 {
	-webkit-box-flex: 4
}

.ui-row-flex-ver {
	-webkit-box-orient: vertical
}

.ui-row-flex-ver .ui-col {
	width: 100%;
	height: 0
}

.ui-whitespace {
	padding-left: 15px;
	padding-right: 15px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

@media ( max-width :320px) {
	.ui-whitespace {
		padding-left: 10px;
		padding-right: 10px
	}
}

.ui-whitespace-left {
	padding-left: 15px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

@media ( max-width :320px) {
	.ui-whitespace-left {
		padding-left: 10px
	}
}

.ui-whitespace-right {
	padding-right: 15px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

@media ( max-width :320px) {
	.ui-whitespace-right {
		padding-right: 10px
	}
}

.ui-justify {
	text-align: justify;
	font-size: 0
}

.ui-justify:after {
	content: '';
	display: inline-block;
	width: 100%;
	height: 0;
	overflow: hidden
}

.ui-justify li {
	display: inline-block;
	text-align: center
}

.ui-justify p {
	font-size: 16px
}

.ui-justify-flex {
	width: 100%;
	display: -webkit-box;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between
}

.ui-header, .ui-footer {
	position: fixed;
	width: 100%;
	z-index: 100;
	left: 0
}

.ui-header {
	top: 0;
	height: 45px;
	line-height: 45px
}

.ui-header-stable, .ui-header-positive {
	padding: 0 10px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-header-stable, .ui-footer-stable {
	background-color: #f8f8f8
}

.ui-header-positive, .ui-footer-positive {
	background-color: #18b4ed;
	color: #fff
}

.ui-header-positive a, .ui-header-positive a:active, .ui-header-positive i,
	.ui-footer-positive a, .ui-footer-positive a:active,
	.ui-footer-positive i {
	color: #fff
}

.ui-footer-btn {
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f9f9f9),
		to(#e0e0e0));
	color: #00a5e0
}

.ui-footer-btn .ui-tiled {
	height: 100%
}

.ui-footer {
	bottom: 0;
	height: 56px
}

.ui-header ~.ui-container {
	border-top: 45px solid transparent
}

.ui-footer ~.ui-container {
	border-bottom: 56px solid transparent
}

.ui-header h1 {
	text-align: center;
	font-size: 18px
}

.ui-header .ui-icon-return {
	position: absolute;
	left: 0
}

.ui-header .ui-btn, .ui-header .ui-btn-lg, .ui-header .ui-btn-s {
	display: block;
	position: absolute;
	right: 10px;
	top: 50%;
	margin-top: -15px
}

.ui-center {
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 150px
}

.ui-flex, .ui-tiled {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box
}

.ui-flex-ver {
	-webkit-box-orient: vertical
}

.ui-flex-pack-start {
	-webkit-box-pack: start
}

.ui-flex-pack-end {
	-webkit-box-pack: end
}

.ui-flex-pack-center {
	-webkit-box-pack: center
}

.ui-flex-align-start {
	-webkit-box-align: start
}

.ui-flex-align-end {
	-webkit-box-align: end
}

.ui-flex-align-center {
	-webkit-box-align: center
}

.ui-tiled li {
	-webkit-box-flex: 1;
	width: 100%;
	text-align: center;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center
}

.ui-badge, .ui-badge-muted, .ui-badge-num, .ui-badge-corner,
	.ui-badge-cornernum {
	display: inline-block;
	text-align: center;
	background: #f74c31;
	color: #fff;
	font-size: 11px;
	height: 16px;
	line-height: 16px;
	-webkit-border-radius: 8px;
	padding: 0 6px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-badge-muted {
	background: #b6cae0
}

.ui-badge-num {
	height: 19px;
	line-height: 20px;
	font-size: 12px;
	min-width: 19px;
	-webkit-border-radius: 10px
}

.ui-badge-wrap {
	position: relative;
	text-align: center
}

.ui-badge-corner {
	position: absolute;
	border: 2px #fff solid;
	height: 20px;
	line-height: 20px;
	top: -4px;
	right: -9px
}

.ui-badge-cornernum {
	position: absolute;
	top: -4px;
	right: -9px;
	height: 19px;
	line-height: 19px;
	font-size: 12px;
	min-width: 19px;
	-webkit-border-radius: 10px;
	top: -5px;
	right: -5px
}

.ui-reddot, .ui-reddot-border, .ui-reddot-s {
	position: relative;
	display: inline-block;
	line-height: 22px;
	padding: 0 6px
}

.ui-reddot:after, .ui-reddot-border:after, .ui-reddot-s:after {
	content: '';
	position: absolute;
	display: block;
	width: 8px;
	height: 8px;
	background-color: #f74c31;
	border-radius: 5px;
	right: -3px;
	top: -3px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-reddot-static {
	display: block;
	width: 8px;
	height: 8px;
	padding: 0
}

.ui-reddot-static:after {
	top: 0;
	right: 0
}

.ui-reddot-border:before {
	content: '';
	position: absolute;
	display: block;
	width: 8px;
	height: 8px;
	background-color: #fff;
	border-radius: 5px;
	right: -4px;
	top: -4px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	padding: 1px
}

.ui-reddot-s:after {
	width: 6px;
	height: 6px;
	top: -5px;
	right: -5px
}

.ui-avatar, .ui-avatar-lg, .ui-avatar-s, .ui-avatar-one,
	.ui-avatar-tiled {
	display: block;
	-webkit-background-size: cover;
	background-image:
		url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAAA8FBMVEXp8Peat8+jwNidutKhvtagvdWcudGfu9Kpwteiv9ecudCsxNifvNTM2+ieu9PE1eSdudDO3OmkvtSyyNu/0eKhvNOivdPm7vWlv9WeutHI2Oa5zd+dudG+0eHn7/bG1+Xi6/SzydzT4Ozc5vDe6PLY5O/a5e/P3eqmv9WmwNW6zt/j7PTX4+6uxtno8Pff6fLh6vPW4u280ODg6fLC1OObuM+buNDD1eTU4eza5fDd5/GhvNKowdarw9jA0+K1y93k7PTk7fXl7fW7zt/F1uXc5/DZ5O/R3+vB0+OnwNXg6vKcuNDM2+mtxNjO3eq3zN1UQ75QAAACR0lEQVR4Xu3W1a7cMBAG4PnHDi4zMx5mxiLD+79Ne7YXq6hKHMU+Ui/8XVpKfo0nMwr9hyzLsizLsqx5ZTfX9DyvmXtXOaNXsd+rYqs9mJFx454HiLwMXsi8CzTO35JZ0x1ABLwlBZAzW0yhAzfgKOmiekLmVEII/peAd22u5ZGMSEpzSWYc30cyoim+oe4/wuU4LgZkwq0HyXEkPCMX9hmC4wmcHpK2VhWS40ncHZG2KcBJBAom2l7kJA6eSFsNDicJsB5qt8SH5EToz0nT1zUCRUi4IE3zqjLkm/aaPGsrQ8oz0nSkDgm1Z750AU4mtL/hYQ1FThZgZ4+0HH9BoAzx9knL8hKsoL9YChCsksdAd3PlWcXBhHSM15CsEqCsNY49uKwm4Lcos5MyAk7BRYmyOpxAcBoOqkca/1sBpyKyl1KH4HQc5J4pmzYkpwQsKJsQnFYRI8qmnD7EwdPrh0gcZA9xio76piBY4iFziACUMw+EcLNXEgKd7o5qVtD52UYeu5RNB3iiifIP0qcRgAplU4N/TNdILsVFgVq/0My6Vxa9lyeTF5jAwzPRsF4gLbfNhBSJ/pRMKPThxGbgkcy4iu19HqdkxN7oR2wlDmqrQ9K39JPm8RLYbZGu8T1cJ3mp1ElXJVqGLAKI7DOJxpA0Le8gJP8VSIGN7RE7Lmr6XfneACCKfwgAjfPFdP8qcpSbk76bgX+BDe+gPqMXs3quj43OQekNGTH+WBmV3nc/fdi+b+9m1S2VuqvZM6lZlmVZlmVZvwEAnS9LHbI74gAAAABJRU5ErkJggg==)
}

.ui-avatar {
	width: 50px;
	height: 50px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-lg, .ui-avatar-one {
	width: 70px;
	height: 70px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar-lg>span, .ui-avatar-one>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-s {
	width: 40px;
	height: 40px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar-s>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-tiled {
	width: 30px;
	height: 30px;
	-webkit-border-radius: 200px;
	overflow: hidden;
	display: inline-block
}

.ui-avatar-tiled>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-label {
	display: inline-block;
	position: relative;
	line-height: 30px;
	height: 30px;
	padding: 0 15px;
	border: 1px solid #cacccd;
	border-radius: 15px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-label {
		position: relative;
		border: 0
	}
	.ui-label:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #cacccd;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 30px;
		pointer-events: none
	}
}

.ui-label:active {
	background-color: #f3f2f2
}

.ui-label-list {
	margin: 0 10px
}

.ui-label-list .ui-label {
	margin: 0 10px 10px 0
}

.ui-label-s {
	font-size: 11px;
	line-height: 13px;
	display: inline-block;
	position: relative;
	padding: 0 1px;
	color: #ff7f0d;
	border: 1px solid #ff7f0d;
	border-radius: 2px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-label-s {
		position: relative;
		border: 0
	}
	.ui-label-s:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #ff7f0d;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 4px;
		pointer-events: none
	}
}

.ui-label-s:active {
	background-color: #f3f2f2
}

.ui-label-s:after {
	content: "";
	position: absolute;
	top: -5px;
	bottom: -5px;
	left: -5px;
	right: -5px
}

.ui-tag-t, .ui-tag-hot, .ui-tag-new, .ui-tag-s-hot, .ui-tag-s-new,
	.ui-tag-pop-hot, .ui-tag-pop-new {
	position: relative
}

.ui-tag-t:before, .ui-tag-hot:before, .ui-tag-new:before, .ui-tag-s-hot:before,
	.ui-tag-s-new:before, .ui-tag-pop-hot:before, .ui-tag-pop-new:before,
	.ui-tag-t:after, .ui-tag-hot:after, .ui-tag-new:after, .ui-tag-s-hot:after,
	.ui-tag-s-new:after, .ui-tag-pop-hot:after, .ui-tag-pop-new:after {
	height: 20px;
	left: 0;
	top: 0;
	z-index: 9;
	display: block
}

.ui-tag-t:before, .ui-tag-hot:before, .ui-tag-new:before, .ui-tag-s-hot:before,
	.ui-tag-s-new:before, .ui-tag-pop-hot:before, .ui-tag-pop-new:before,
	.ui-tag-vip:before, .ui-tag-svip:before, .ui-tag-selected:after {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	position: absolute
}

.ui-tag-t:before, .ui-tag-hot:before, .ui-tag-new:before, .ui-tag-s-hot:before,
	.ui-tag-s-new:before, .ui-tag-pop-hot:before, .ui-tag-pop-new:before {
	content: "";
	line-height: 20px;
	color: red
}

.ui-tag-t:after, .ui-tag-hot:after, .ui-tag-new:after, .ui-tag-s-hot:after,
	.ui-tag-s-new:after, .ui-tag-pop-hot:after, .ui-tag-pop-new:after {
	position: absolute;
	content: '';
	width: 22px;
	text-align: right;
	line-height: 20px;
	font-size: 12px;
	color: #fff;
	padding-right: 14px
}

.ui-tag-b, .ui-tag-freelimit, .ui-tag-free, .ui-tag-last, .ui-tag-limit,
	.ui-tag-act, .ui-tag-xy, .ui-tag-vip, .ui-tag-svip {
	position: relative
}

.ui-tag-b:before, .ui-tag-freelimit:before, .ui-tag-free:before,
	.ui-tag-last:before, .ui-tag-limit:before, .ui-tag-act:before,
	.ui-tag-xy:before, .ui-tag-vip:before, .ui-tag-svip:before {
	position: absolute;
	font-size: 10px;
	width: 28px;
	height: 13px;
	line-height: 13px;
	bottom: 0;
	right: 0;
	z-index: 9;
	color: #fff;
	border-radius: 2px;
	text-align: center
}

.ui-tag-vip:before, .ui-tag-svip:before {
	font-size: 32px;
	text-indent: -2px;
	border-radius: 2px
}

.ui-tag-vip:before {
	background-color: red;
	color: #fffadf;
	content: ""
}

.ui-tag-svip:before {
	background-color: #ffd400;
	color: #b7440e;
	content: ""
}

.ui-tag-freelimit:before {
	background-color: #18b4ed;
	content: '\u9650\u514d'
}

.ui-tag-free:before {
	background-color: #5fb336;
	content: '\u514d\u8d39'
}

.ui-tag-last:before {
	background-color: #8f6adb;
	content: '\u7edd\u7248'
}

.ui-tag-limit:before {
	background-color: #3385e6;
	content: '\u9650\u91cf'
}

.ui-tag-act:before {
	background-color: #00c795;
	content: '\u6d3b\u52a8'
}

.ui-tag-xy:before {
	background-color: #d7ba42;
	content: '\u661f\u5f71'
}

.ui-tag-freemonthly:before {
	background-color: #ff7f0d;
	content: '\u5305\u6708'
}

.ui-tag-onsale:before {
	background-color: #00c795;
	content: '\u7279\u4ef7'
}

.ui-tag-hot:after, .ui-tag-s-hot:after, .ui-tag-pop-hot:after {
	content: '\u70ed'
}

.ui-tag-new:after, .ui-tag-s-new:after, .ui-tag-pop-new:after {
	content: '\u65b0'
}

.ui-tag-hot:before, .ui-tag-s-hot:before, .ui-tag-pop-hot:before {
	color: #ff7200
}

.ui-tag-s-hot:before, .ui-tag-s-new:before {
	content: "";
	left: -2px
}

.ui-tag-s-hot:after, .ui-tag-s-new:after {
	width: 16px;
	padding-right: 12px
}

.ui-tag-selected:after {
	content: "";
	color: #18b4ed;
	right: -5px;
	top: -5px;
	z-index: 9;
	width: 26px;
	height: 26px;
	background: #fff;
	border-radius: 13px;
	line-height: 26px;
	text-indent: -3px
}

.ui-tag-wrap {
	display: inline-block;
	position: relative;
	padding-right: 32px
}

.ui-tag-wrap .ui-tag-vip, .ui-tag-wrap .ui-tag-svip {
	position: static
}

.ui-tag-wrap .ui-tag-vip:before, .ui-tag-wrap .ui-tag-svip:before {
	top: 50%;
	margin-top: -7px
}

.ui-tag-pop-hot:before, .ui-tag-pop-new:before {
	content: "";
	left: -10px;
	top: 1px
}

.ui-tag-pop-hot:after, .ui-tag-pop-new:after {
	font-size: 11px;
	padding-right: 0;
	text-align: center;
	left: -5px
}

.ui-btn, .ui-btn-lg, .ui-btn-s {
	height: 30px;
	line-height: 30px;
	padding: 0 11px;
	min-width: 55px;
	display: inline-block;
	position: relative;
	text-align: center;
	font-size: 15px;
	background-color: #fdfdfd;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #fff),
		to(#fafafa));
	vertical-align: top;
	color: #00a5e0;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid #cacccd;
	border-radius: 3px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn, .ui-btn-lg, .ui-btn-s {
		position: relative;
		border: 0
	}
	.ui-btn:before, .ui-btn-lg:before, .ui-btn-s:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #cacccd;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 6px;
		pointer-events: none
	}
}

.ui-btn:not (.disabled ):not (:disabled ):active, .ui-btn-lg:not (.disabled
	):not (:disabled ):active, .ui-btn-s:not (.disabled ):not (:disabled ):active,
	.ui-btn.active, .active.ui-btn-lg, .active.ui-btn-s {
	background: #f2f2f2;
	color: rgba(0, 165, 224, .5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn:after, .ui-btn-lg:after, .ui-btn-s:after {
	content: "";
	position: absolute;
	top: -7px;
	bottom: -7px;
	left: 0;
	right: 0
}

.ui-btn-primary {
	background-color: #18b4ed;
	border-color: #0baae4;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #1fbaf3),
		to(#18b4ed));
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-primary:not (.disabled ):not (:disabled ):active,
	.ui-btn-primary.active {
	background: #1ca7da;
	border-color: #1ca7da;
	color: rgba(255, 255, 255, .5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-danger {
	background-color: #f75549;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #fc6156),
		to(#f75549));
	color: #fff;
	border-color: #f43d30;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-danger:not (.disabled ):not (:disabled ):active, .ui-btn-danger.active
	{
	background: #e2574d;
	border-color: #e2574d;
	color: rgba(255, 255, 255, .5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn.disabled, .disabled.ui-btn-lg, .disabled.ui-btn-s, .ui-btn:disabled,
	.ui-btn-lg:disabled, .ui-btn-s:disabled {
	border: 0;
	color: #ccc;
	background: #e9ebec;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-lg {
	font-size: 18px;
	height: 44px;
	line-height: 44px;
	display: block;
	width: 100%;
	border-radius: 5px
}

.ui-btn-wrap {
	padding: 15px 10px
}

@media ( max-width :320px) {
	.ui-btn-wrap {
		padding: 10px
	}
}

.ui-btn-s {
	padding: 0;
	width: 55px;
	height: 25px;
	line-height: 25px;
	font-size: 13px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn-primary:before {
		border: 1px solid #0baae4
	}
	.ui-btn-danger:before {
		border: 1px solid #f43d30
	}
	.ui-btn, .ui-btn-lg, .ui-btn-s {
		border: 0
	}
	.ui-btn.disabled, .disabled.ui-btn-lg, .disabled.ui-btn-s, .ui-btn:disabled,
		.ui-btn-lg:disabled, .ui-btn-s:disabled, .ui-btn.disabled:before,
		.disabled.ui-btn-lg:before, .disabled.ui-btn-s:before, .ui-btn:disabled:before,
		.ui-btn-lg:disabled:before, .ui-btn-s:disabled:before {
		border: 1px solid #e9ebec
	}
	.ui-btn-lg:before {
		border-radius: 10px
	}
}

.ui-btn-progress {
	width: 55px;
	padding: 0;
	overflow: hidden
}

.ui-btn-progress .ui-btn-inner {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	overflow: hidden;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #1fbaf3),
		to(#18b4ed));
	border-bottom-left-radius: 3px;
	border-top-left-radius: 3px
}

.ui-btn-progress .ui-btn-inner span {
	display: inline-block;
	color: #fff;
	position: absolute;
	width: 55px;
	left: 0
}

.ui-btn-progress.disabled, .ui-btn-progress:disabled {
	background-color: #fefefe;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #fff),
		to(#fafafa));
	color: #ccc;
	border: 1px solid #cacccd;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn-progress.disabled, .ui-btn-progress:disabled {
		border: 0
	}
	.ui-btn-progress.disabled:before, .ui-btn-progress:disabled:before {
		border: 1px solid #cacccd
	}
}

.ui-btn-group {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-align: center
}

.ui-btn-group button {
	display: block;
	-webkit-box-flex: 1;
	margin-right: 10px
}

.ui-btn-group button:first-child {
	margin-left: 10px
}

.ui-tips {
	padding: 20px 15px;
	text-align: center;
	font-size: 16px;
	color: #000
}

.ui-tips i {
	display: inline-block;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-tips i:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	content: "";
	color: #0090ff;
	line-height: 21px
}

.ui-tips-success i:before {
	content: "";
	color: #65d521
}

.ui-tips-warn i:before {
	content: "";
	color: #f76249
}

.ui-newstips-wrap {
	margin: 20px 15px;
	text-align: center
}

.ui-newstips {
	background: #383939;
	position: relative;
	height: 40px;
	line-height: 40px;
	display: -webkit-inline-box;
	-webkit-box-align: center;
	padding-right: 25px;
	border-radius: 5px;
	font-size: 14px;
	color: #fff;
	padding-left: 15px
}

.ui-newstips .ui-avatar-tiled, .ui-newstips .ui-newstips-thumb,
	.ui-newstips i {
	display: block;
	margin-left: -5px;
	margin-right: 10px
}

.ui-newstips .ui-newstips-thumb {
	width: 30px;
	height: 30px;
	position: relative
}

.ui-newstips .ui-newstips-thumb>span {
	display: block;
	width: 100%;
	height: 100%;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-newstips div {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	-webkit-box-flex: 1;
	height: inherit
}

.ui-newstips:after {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	color: #c7c7c7;
	content: "";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media ( max-width :320px) {
	.ui-newstips:after {
		right: 10px
	}
}

.ui-newstips .ui-reddot, .ui-newstips .ui-reddot-border, .ui-newstips .ui-reddot-s,
	.ui-newstips .ui-badge-num {
	margin-left: 10px;
	margin-right: 5px
}

.ui-tooltips {
	width: 100%;
	position: relative;
	z-index: 99;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-tooltips-cnt {
	background-color: #fff;
	line-height: 44px;
	height: 44px;
	padding-left: 10px;
	padding-right: 30px;
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-tooltips-cnt .ui-icon-close:before {
	font-size: 40px;
	color: rgba(0, 0, 0, .2);
	margin-left: -10px;
	position: absolute;
	right: 0;
	top: 0
}

.ui-tooltips-warn .ui-tooltips-cnt {
	background-color: rgba(255, 242, 183, .95);
	color: #000
}

.ui-tooltips-warn:active .ui-tooltips-cnt {
	background-color: #e1d498
}

.ui-tooltips-guide .ui-tooltips-cnt {
	color: #00a5e0;
	background-color: rgba(205, 242, 255, .95)
}

.ui-tooltips-guide .ui-tooltips-cnt .ui-icon-close:before {
	color: rgba(0, 165, 224, .2)
}

.ui-tooltips-guide:active .ui-tooltips-cnt {
	background-color: #b5dbe8
}

.ui-tooltips-cnt-link:after {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	color: #c7c7c7;
	content: "";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px;
	color: rgba(0, 0, 0, .5)
}

@media ( max-width :320px) {
	.ui-tooltips-cnt-link:after {
		right: 10px
	}
}

.ui-tooltips-guide .ui-tooltips-cnt-link:after {
	color: #00aeef
}

.ui-tooltips-warn i {
	display: inline-block;
	margin-right: 4px;
	margin-left: -4px;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-tooltips-warn i:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	content: "";
	color: #f76249
}

.ui-table {
	width: 100%;
	border-collapse: collapse
}

.ui-table th {
	font-weight: 500
}

.ui-table td, .ui-table th {
	border-bottom: 1px solid #e0e0e0;
	border-right: 1px solid #e0e0e0;
	text-align: center
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-table td, .ui-table th {
		position: relative;
		border-right: 0;
		border-bottom: 0
	}
	.ui-table td:after, .ui-table th:after {
		content: "";
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0)),
			-webkit-gradient(linear, left top, left bottom, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		-webkit-background-size: 1px 100%, 100% 1px;
		background-size: 1px 100%, 100% 1px;
		background-repeat: no-repeat;
		background-position: right, bottom;
		pointer-events: none
	}
	.ui-table tr td:last-child:after, .ui-table tr th:last-child:after {
		background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		-webkit-background-size: 100% 1px;
		background-size: 100% 1px;
		background-repeat: no-repeat;
		background-position: bottom
	}
	.ui-table tr:last-child td:not (:last-child ):after {
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		-webkit-background-size: 1px 100%;
		background-size: 1px 100%;
		background-repeat: no-repeat;
		background-position: right
	}
}

.ui-table tr td:last-child, .ui-table tr th:last-child {
	border-right: 0
}

.ui-table tr:last-child td {
	border-bottom: 0
}

.ui-list {
	background-color: #fff;
	width: 100%
}

.ui-list>li {
	position: relative;
	margin-left: 15px;
	display: -webkit-box
}

.ui-list-pure>li {
	display: block
}

.ui-list-text>li, .ui-list-pure>li {
	position: relative;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-right: 15px;
	-webkit-box-align: center
}

.ui-list-text h4, .ui-list-text p {
	-webkit-box-flex: 1
}

.ui-list-cover>li {
	padding-left: 15px;
	margin-left: 0
}

.ui-list>li.ui-border-t:first-child, .ui-list>li:first-child>.ui-border-t
	{
	border: 0;
	background-image: none
}

.ui-list-thumb, .ui-list-thumb-s, .ui-list-img, .ui-list-icon {
	position: relative;
	margin: 10px 10px 10px 0
}

.ui-list-thumb>span, .ui-list-thumb-s>span, .ui-list-img>span,
	.ui-list-icon>span {
	display: block;
	width: 100%;
	height: 100%;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-list-thumb {
	width: 50px;
	height: 50px
}

.ui-list-img {
	width: 100px;
	height: 68px
}

.ui-list-thumb-s {
	width: 28px;
	height: 28px
}

.ui-list-icon {
	width: 40px;
	height: 40px
}

.ui-list .ui-avatar, .ui-list .ui-avatar-s, .ui-list .ui-avatar-lg {
	margin: 10px 10px 10px 0
}

.ui-list-info {
	-webkit-box-flex: 1;
	padding-top: 10px;
	padding-bottom: 10px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	padding-right: 15px
}

.ui-list-info p {
	color: #777;
	font-size: 14px
}

.ui-list-text .ui-list-info {
	padding-top: 0;
	padding-bottom: 0
}

.ui-list li h4 {
	font-size: 16px
}

.ui-list:not (.ui-list-text ) li>p, .ui-list li>h5 {
	font-size: 14px;
	color: #777
}

.ui-list-active>li:active, .ui-list li.active {
	background-color: #e5e6e7;
	padding-left: 15px;
	margin-left: 0
}

.ui-list-active>li:active, .ui-list>li.active, .ui-list>li.active>.ui-border-t,
	.ui-list>li.active+li>.ui-border-t, .ui-list>li.active+li.ui-border-t {
	background-image: none;
	border-top-color: #e5e6e7
}

.ui-list-link>li:after {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	color: #c7c7c7;
	content: "";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media ( max-width :320px) {
	.ui-list-link>li:after {
		right: 10px
	}
}

.ui-list-text.ui-list-link>li {
	padding-right: 30px
}

.ui-list-link .ui-list-info {
	padding-right: 30px
}

.ui-list-function .ui-list-info {
	padding-right: 75px
}

.ui-list-function .ui-btn, .ui-list-function .ui-btn-lg,
	.ui-list-function .ui-btn-s {
	position: absolute;
	top: 50%;
	right: 15px;
	margin-top: -15px
}

.ui-list-function .ui-btn-s {
	margin-top: -12px
}

.ui-list-function.ui-list-link .ui-list-info {
	padding-right: 90px
}

.ui-list-function.ui-list-link .ui-btn, .ui-list-function.ui-list-link .ui-btn-lg,
	.ui-list-function.ui-list-link .ui-btn-s {
	right: 30px
}

.ui-list-function li {
	-webkit-box-align: inherit
}

.ui-list-one>li {
	padding-top: 0;
	padding-bottom: 0;
	line-height: 44px
}

.ui-list-one .ui-list-info {
	-webkit-box-orient: horizontal;
	-webkit-box-align: center
}

.ui-list-one h4 {
	-webkit-box-flex: 1
}

@media ( max-width :320px) {
	.ui-list>li {
		margin-left: 10px
	}
	.ui-list-text>li, .ui-list-pure>li, .ui-list-info {
		padding-right: 10px
	}
	.ui-list-cover>li, .ui-list-active>li:active, .ui-list li.active {
		padding-left: 10px
	}
	.ui-list-text.ui-list-link>li {
		padding-right: 25px
	}
	.ui-list-function .ui-list-info {
		padding-right: 70px
	}
	.ui-list-function .ui-btn, .ui-list-function .ui-btn-lg,
		.ui-list-function .ui-btn-s {
		right: 10px
	}
	.ui-list-function.ui-list-link .ui-list-info {
		padding-right: 85px
	}
	.ui-list-function.ui-list-link .ui-btn, .ui-list-function.ui-list-link .ui-btn-lg,
		.ui-list-function.ui-list-link .ui-btn-s {
		right: 25px
	}
}

.ui-notice {
	width: 100%;
	height: 100%;
	z-index: 99;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	position: absolute;
	text-align: center
}

.ui-notice>i {
	display: block;
	margin-bottom: 20px
}

.ui-notice>i:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	content: "";
	font-size: 100px;
	line-height: 100px;
	color: rgba(0, 0, 0, .3)
}

.ui-notice p {
	font-size: 16px;
	line-height: 20px;
	color: #bbb;
	text-align: center;
	padding: 0 15px
}

.ui-notice-btn {
	width: 100%;
	-webkit-box-sizing: border-box;
	padding: 50px 15px 15px
}

.ui-notice-btn button {
	margin: 10px 0
}

.ui-form {
	background-color: #fff
}

.ui-form-item-order.active {
	background-color: #e5e6e7
}

.ui-form-item {
	position: relative;
	font-size: 16px;
	height: 44px;
	line-height: 44px;
	padding-right: 15px;
	padding-left: 15px
}

.ui-form-item label:not (.ui-switch ):not (.ui-checkbox ):not (.ui-checkbox-s
	):not (.ui-radio ){
	width: 95px;
	position: absolute;
	text-align: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item input, .ui-form-item textarea {
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-appearance: none;
	border: 0;
	background: 0 0;
	padding-left: 95px
}

.ui-form-item input[type=checkbox], .ui-form-item input[type=radio] {
	padding-left: 0
}

.ui-form-item .ui-icon-close {
	position: absolute;
	top: 0;
	right: 6px
}

@media ( max-width :320px) {
	.ui-form-item .ui-icon-close {
		right: 1px
	}
}

@media ( max-width :320px) {
	.ui-form-item {
		padding-left: 10px;
		padding-right: 10px
	}
}

.ui-form-item-textarea {
	height: 65px
}

.ui-form-item-textarea label {
	vertical-align: top
}

.ui-form-item-textarea textarea {
	margin-top: 15px;
	border: 0
}

.ui-form-item-textarea textarea:focus {
	outline: 0
}

.ui-form-item-link>li:after {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	color: #c7c7c7;
	content: "";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media ( max-width :320px) {
	.ui-form-item-link>li:after {
		right: 10px
	}
}

.ui-form-item-l label, .ui-form-item-r button {
	color: #00a5e0;
	text-align: center
}

.ui-form-item-r .ui-icon-close {
	right: 125px
}

.ui-form-item-l input:not ([type=checkbox] ):not ([type=radio] ){
	padding-left: 115px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item-r {
	padding-right: 0
}

.ui-form-item-r input:not ([type=checkbox] ):not ([type=radio] ){
	padding-left: 0;
	padding-right: 150px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item-r button {
	width: 110px;
	height: 44px;
	position: absolute;
	top: 0;
	right: 0
}

.ui-form-item-r button.disabled {
	color: #bbb
}

.ui-form-item-r button:not (.disabled ):active {
	background-color: #e5e6e7
}

.ui-form-item-pure input, .ui-form-item-pure textarea {
	padding-left: 0
}

.ui-form-item-show label {
	color: #777
}

.ui-form-item-link:after {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	color: #c7c7c7;
	content: "";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media ( max-width :320px) {
	.ui-form-item-link:after {
		right: 10px
	}
}

.ui-form-item-checkbox, .ui-form-item-radio, .ui-form-item-switch {
	display: -webkit-box;
	-webkit-box-align: center
}

.ui-checkbox, .ui-checkbox-s {
	display: inline-block
}

.ui-checkbox input, .ui-checkbox-s input {
	display: inline-block;
	width: 25px;
	height: 1px;
	position: relative;
	overflow: visible;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0;
	margin-right: 8px;
	vertical-align: middle
}

.ui-checkbox input:before, .ui-checkbox-s input:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	content: "";
	color: #18b4ed;
	position: absolute;
	top: -22px;
	left: -4px;
	color: #dedfe0
}

.ui-checkbox input:checked:before, .ui-checkbox-s input:checked:before {
	content: "";
	color: #18b4ed
}

.ui-checkbox-s input:before {
	content: ""
}

.ui-checkbox-s input:checked:before {
	content: ""
}

.ui-switch {
	position: absolute;
	font-size: 16px;
	right: 15px;
	top: 50%;
	margin-top: -16px;
	width: 52px;
	height: 32px;
	line-height: 32px
}

@media ( max-width :320px) {
	.ui-switch {
		right: 10px
	}
}

.ui-switch input {
	width: 52px;
	height: 32px;
	position: absolute;
	z-index: 2;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0
}

.ui-switch input:before {
	content: '';
	width: 50px;
	height: 30px;
	border: 1px solid #dfdfdf;
	background-color: #fdfdfd;
	border-radius: 20px;
	cursor: pointer;
	display: inline-block;
	position: relative;
	vertical-align: middle;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	border-color: #dfdfdf;
	-webkit-box-shadow: #dfdfdf 0 0 0 0 inset;
	box-shadow: #dfdfdf 0 0 0 0 inset;
	-webkit-transition: border .4s, -webkit-box-shadow .4s;
	transition: border .4s, box-shadow .4s;
	-webkit-background-clip: content-box;
	background-clip: content-box
}

.ui-switch input:checked:before {
	border-color: #64bd63;
	-webkit-box-shadow: #64bd63 0 0 0 16px inset;
	box-shadow: #64bd63 0 0 0 16px inset;
	background-color: #64bd63;
	transition: border .4s, box-shadow .4s, background-color 1.2s;
	-webkit-transition: border .4s, -webkit-box-shadow .4s, background-color
		1.2s;
	background-color: #64bd63
}

.ui-switch input:checked:after {
	left: 21px
}

.ui-switch input:after {
	content: '';
	width: 30px;
	height: 30px;
	position: absolute;
	top: 1px;
	left: 0;
	border-radius: 100%;
	background-color: #fff;
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
	box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
	-webkit-transition: left .2s;
	transition: left .2s
}

.ui-radio {
	line-height: 25px;
	display: inline-block
}

.ui-radio input {
	display: inline-block;
	width: 26px;
	height: 26px;
	position: relative;
	overflow: visible;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0;
	margin-right: 8px;
	vertical-align: middle
}

.ui-radio input:before {
	content: '';
	display: block;
	width: 24px;
	height: 24px;
	border: 1px solid #dfe0e1;
	border-radius: 13px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	position: absolute;
	left: 0;
	top: 0
}

.ui-radio input:checked:after {
	content: '';
	display: block;
	width: 14px;
	height: 14px;
	background: #18b4ed;
	border-radius: 7px;
	position: absolute;
	left: 6px;
	top: 6px
}

.ui-select {
	position: relative;
	margin-right: 6px
}

.ui-select select {
	-webkit-appearance: none;
	border: 0;
	background: 0 0;
	width: 100%;
	padding-right: 14px
}

.ui-select:after {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -4px;
	width: 0;
	height: 0;
	border-top: 6px solid;
	border-right: 5px solid transparent;
	border-left: 5px solid transparent;
	color: #a6a6a6;
	content: "";
	pointer-events: none
}

.ui-select-group {
	margin-left: 95px;
	overflow: hidden
}

.ui-select-group .ui-select {
	float: left
}

.ui-form-item>.ui-select {
	margin-left: 95px
}

.ui-input-wrap {
	background-color: #ebeced;
	height: 44px;
	display: -webkit-box;
	-webkit-box-align: center
}

.ui-input-wrap .ui-btn, .ui-input-wrap .ui-btn-lg, .ui-input-wrap .ui-btn-s,
	.ui-input-wrap i {
	margin-right: 10px
}

.ui-input {
	height: 30px;
	line-height: 30px;
	margin: 7px 10px;
	background: #fff;
	padding-left: 10px;
	-webkit-box-flex: 1
}

.ui-input input {
	width: 100%;
	height: 100%;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0
}

.ui-searchbar-wrap {
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	background-color: #ebeced;
	height: 44px
}

.ui-searchbar-wrap button {
	margin-right: 10px
}

.ui-searchbar-wrap .ui-searchbar-cancel {
	color: #00a5e0;
	font-size: 16px;
	padding: 4px 8px
}

.ui-searchbar-wrap .ui-searchbar-input, .ui-searchbar-wrap button,
	.ui-searchbar-wrap .ui-icon-close {
	display: none
}

.ui-searchbar-wrap.focus {
	-webkit-box-pack: start
}

.ui-searchbar-wrap.focus .ui-searchbar-input, .ui-searchbar-wrap.focus button,
	.ui-searchbar-wrap.focus .ui-icon-close {
	display: block
}

.ui-searchbar-wrap.focus .ui-searchbar-text {
	display: none
}

.ui-searchbar {
	border-radius: 5px;
	margin: 0 10px;
	background: #fff;
	height: 30px;
	line-height: 30px;
	position: relative;
	padding-left: 4px;
	-webkit-box-flex: 1;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	color: #bbb;
	font-size: 14px;
	width: 100%
}

.ui-searchbar input {
	-webkit-appearance: none;
	border: 0;
	background: 0 0;
	color: #000;
	width: 100%;
	padding: 4px 0
}

.ui-searchbar .ui-icon-search {
	line-height: 30px
}

.ui-searchbar .ui-icon-close {
	line-height: 30px
}

.ui-searchbar-input {
	-webkit-box-flex: 1
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-searchbar.ui-border-radius:before {
		border-radius: 10px
	}
}

.ui-slider {
	width: 100%;
	overflow: hidden;
	position: relative;
	padding-top: 31.25%
}

.ui-slider-content {
	display: -webkit-box;
	position: absolute;
	left: 0;
	top: 0;
	height: 100%
}

.ui-slider-content>li {
	-webkit-box-flex: 1;
	width: 100%;
	height: 100%
}

.ui-slider-content>li img {
	display: block;
	width: 100%
}

.ui-slider-content>li span {
	display: block;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	-webkit-background-size: 100% 100%
}

.ui-slider-content>li.active {
	opacity: .5
}

.ui-slider-indicators {
	position: absolute;
	display: -webkit-box;
	-webkit-box-pack: end;
	width: 100%;
	bottom: 10px;
	right: 4px;
	font-size: 0
}

.ui-slider-indicators li {
	display: block;
	text-indent: 100%;
	white-space: nowrap;
	overflow: hidden;
	font-size: 0;
	width: 7px;
	height: 7px;
	background-color: rgba(0, 0, 0, .3);
	border-radius: 10px;
	margin-right: 6px;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	position: relative
}

.ui-slider-indicators li.current:before {
	content: '';
	position: absolute;
	background-color: #fff;
	left: 1px;
	top: 1px;
	width: 5px;
	height: 5px;
	border-radius: 10px;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-slider-indicators-center {
	-webkit-box-pack: center;
	right: 0
}

.ui-panel {
	overflow: hidden;
	margin-bottom: 10px
}

.ui-panel .ui-grid-halve, .ui-panel .ui-grid-trisect {
	padding-top: 0
}

.ui-panel h1, .ui-panel h2, .ui-panel h3 {
	padding-left: 15px;
	padding-right: 15px;
	line-height: 44px;
	position: relative;
	overflow: hidden;
	display: -webkit-box
}

@media ( max-width :320px) {
	.ui-panel h1, .ui-panel h2, .ui-panel h3 {
		padding-left: 10px;
		padding-right: 10px
	}
}

.ui-panel h1 span, .ui-panel h2 span, .ui-panel h3 span {
	display: block
}

.ui-panel-card, .ui-panel-simple {
	background-color: #fff
}

.ui-panel-pure h2, .ui-panel-pure h3 {
	color: #777
}

.ui-panel-simple {
	margin-bottom: 0
}

.ui-panel-subtitle {
	font-size: 14px;
	color: #777;
	margin-left: 10px
}

.ui-panel-title-tips {
	font-size: 12px;
	color: #777;
	position: absolute;
	right: 15px
}

@media ( max-width :320px) {
	.ui-panel-title-tips {
		right: 10px
	}
}

.ui-arrowlink .ui-panel-title-tips {
	right: 30px
}

@media ( max-width :320px) {
	.ui-arrowlink .ui-panel-title-tips {
		right: 25px
	}
}

.ui-progress {
	overflow: hidden;
	width: 100%;
	height: 2px;
	font-size: 0;
	background-color: #e2e2e2;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-progress span {
	display: block;
	width: 0;
	background: #65d521;
	height: 100%;
	font-size: 0
}

.ui-grid-trisect li .ui-progress, .ui-grid-halve li .ui-progress {
	position: absolute;
	height: 13px;
	bottom: 0;
	z-index: 9;
	border: 5px solid rgba(248, 248, 248, .9)
}

.ui-grid-trisect li .ui-progress span, .ui-grid-halve li .ui-progress span
	{
	border-radius: 3px
}

.ui-tab {
	width: 100%;
	overflow: hidden
}

.ui-tab-nav {
	width: 100%;
	background-color: #fff;
	display: box;
	display: -webkit-box;
	font-size: 16px;
	height: 45px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-tab-content {
	display: -webkit-box
}

.ui-tab-content>li {
	-webkit-box-flex: 1;
	width: 100%
}

.ui-tab-nav li {
	height: 45px;
	line-height: 45px;
	min-width: 70px;
	box-flex: 1;
	-webkit-box-flex: 1;
	text-align: center;
	color: #777;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	border-bottom: 2px solid transparent;
	width: 100%
}

.ui-tab-nav li.current {
	color: #00a5e0;
	border-bottom: 2px #00a5e0 solid
}

.ui-tab-nav li:active {
	opacity: .8
}

.ui-loading-wrap {
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 40px
}

.ui-loading {
	width: 20px;
	height: 20px;
	display: block;
	background: url(../img/loading_sprite.png);
	-webkit-background-size: auto 20px;
	-webkit-animation: am-rotate 1s steps(12) infinite
}

.ui-loading-bright {
	width: 37px;
	height: 37px;
	display: block;
	background-image: url(../img/loading_sprite_white.png);
	-webkit-background-size: auto 37px;
	-webkit-animation: am-rotate2 1s steps(12) infinite
}

.ui-loading-wrap .ui-loading {
	margin: 10px
}

.ui-loading-block {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	background: rgba(0, 0, 0, .4);
	display: none;
	background: transparent
}

.ui-loading-block .ui-loading-cnt {
	width: 130px;
	height: 110px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-align: center;
	text-align: center;
	background: rgba(0, 0, 0, .65);
	border-radius: 6px;
	color: #fff;
	font-size: 16px
}

.ui-loading-block .ui-loading-bright {
	margin: 18px 0 8px
}

.ui-loading-block.show {
	display: -webkit-box;
	display: box
}

@
-webkit-keyframes am-rotate {
	from {background-position: 0 0
}

to {
	background-position: -240px 0
}

}
@
-webkit-keyframes am-rotate2 {
	from {background-position: 0 0
}

to {
	background-position: -444px 0
}

}
.ui-poptips {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 999;
	padding: 0 10px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-poptips-cnt {
	background-color: rgba(0, 0, 0, .6);
	line-height: 40px;
	height: 40px;
	color: #fff;
	font-size: 16px;
	text-align: center;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-poptips-cnt i {
	display: inline-block;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-poptips-cnt i:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	margin-right: 2px;
	margin-left: 4px;
	color: #fff;
	line-height: 40px
}

.ui-poptips-info i:before {
	content: ""
}

.ui-poptips-success i:before {
	content: ""
}

.ui-poptips-warn i:before {
	content: ""
}

.ui-dialog {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	background: rgba(0, 0, 0, .4);
	display: none
}

.ui-dialog.show {
	display: -webkit-box;
	display: box
}

.ui-dialog-hd {
	height: 48px;
	line-height: 48px;
	text-align: center;
	position: relative
}

.ui-dialog-cnt {
	border-radius: 6px;
	width: 270px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	pointer-events: auto;
	background-color: rgba(253, 253, 253, .95);
	position: relative;
	font-size: 16px
}

.ui-dialog-bd {
	min-height: 71px;
	border-top-left-radius: 6px;
	border-top-right-radius: 6px;
	padding: 18px;
	display: -webkit-box;
	display: box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-orient: vertical
}

.ui-dialog-bd>h4 {
	margin-bottom: 4px;
	width: 100%;
	text-align: center
}

.ui-dialog-bd>div, .ui-dialog-bd>ul {
	width: 100%
}

.ui-dialog-ft {
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-align: center;
	border-top: 1px solid #e0e0e0;
	height: 42px;
	line-height: 42px
}

.ui-dialog-close:before {
	font-family: iconfont !important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0, 0, 0, .5);
	content: "";
	color: #828282;
	display: block;
	line-height: 32px;
	position: absolute;
	top: 3px;
	right: 3px
}

.ui-dialog-close:active {
	opacity: .5
}

.ui-dialog-ft button {
	color: #00a5e0;
	text-align: center;
	border-right: 1px #e0e0e0 solid;
	width: 100%;
	line-height: 42px;
	background: transparent;
	display: block;
	margin: 0 !important;
	-webkit-box-flex: 1
}

.ui-dialog-ft button:active {
	background-color: rgba(0, 0, 0, .1) !important
}

.ui-dialog-ft button:first-child {
	border-bottom-left-radius: 6px
}

.ui-dialog-ft button:last-child {
	border-right: 0;
	border-bottom-right-radius: 6px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-dialog-ft {
		position: relative;
		border: 0;
		background-position: left top;
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}
	.ui-dialog-ft button {
		border-right: 0;
		background-position: right top;
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		background-repeat: repeat-y;
		-webkit-background-size: 1px 100%
	}
	.ui-dialog-ft button:last-child {
		background: 0 0
	}
}

.ui-selector header {
	padding: 6px 10px;
	color: #a6a6a6;
	overflow: hidden
}

.ui-selector header h3 {
	float: left
}

.ui-selector-content {
	background: #fff
}

.ui-selector-item p {
	margin-left: 10px;
	-webkit-box-flex: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-selector-item .ui-txt-info {
	margin: 0 10px;
	font-size: 12px
}

.ui-selector-item .ui-list-link li:after {
	display: none
}

.ui-selector-item h3:before {
	content: '';
	display: block;
	width: 0;
	height: 0;
	border-left: 6px solid;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	color: #a6a6a6;
	position: absolute;
	left: 25px;
	top: 15px;
	-webkit-transition: all .2s
}

.ui-selector-item.active h3:before {
	-webkit-transform: rotate(90deg)
}

.ui-selector-item.active h3 {
	border: 0;
	background-image: none
}

.ui-selector-item.active ul {
	display: block
}

.ui-selector-item ul {
	display: none
}

.ui-selector-item h3 {
	display: -webkit-box;
	font-size: 16px;
	padding-left: 54px;
	line-height: 44px;
	height: 44px;
	position: relative
}

.ui-actionsheet {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	opacity: 0;
	pointer-events: none;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: end;
	background: rgba(0, 0, 0, .4)
}

.ui-actionsheet.show {
	pointer-events: inherit;
	opacity: 1
}

.ui-actionsheet.show .ui-actionsheet-cnt {
	-webkit-transform: translateY(0);
	-webkit-transition-delay: .3s
}

.ui-actionsheet-cnt {
	font-size: 21px;
	position: fixed;
	bottom: 0;
	padding: 0 8px;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	text-align: center;
	-webkit-transform: translateY(100%);
	-webkit-transition-property: all;
	-webkit-transition-timing-function: ease-out;
	-webkit-transition-duration: .3s
}

.ui-actionsheet button, .ui-actionsheet h4 {
	background: rgba(255, 255, 255, .84);
	display: block;
	width: 100%;
	color: #0079ff;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-actionsheet button {
	line-height: 44px;
	height: 44px
}

.ui-actionsheet h4 {
	line-height: 24px;
	padding-left: 20px;
	padding-right: 20px;
	padding-top: 10px;
	padding-bottom: 10px;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.ui-actionsheet button:not (:last-child ){
	border-top: 1px #e0e0e0 solid
}

.ui-actionsheet button:last-child {
	margin: 8px 0;
	border-radius: 3px
}

.ui-actionsheet button:nth-last-child(2) {
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px
}

.ui-actionsheet button:active {
	opacity: .84
}

.ui-actionsheet h4 {
	font-size: 13px;
	color: #8a8a8a
}

.ui-actionsheet .ui-actionsheet-del {
	color: #fd472b
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-actionsheet button:not (:last-child ){
		border: 0;
		background-position: left top;
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5,
			transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}
}