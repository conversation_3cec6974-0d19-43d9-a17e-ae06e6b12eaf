#!/bin/bash

# SEO平台本地启动脚本
echo "🚀 启动SEO平台本地开发环境"
echo "================================"

# 设置PHP路径
export PATH="/opt/homebrew/opt/php@8.1/bin:$PATH"

# 检查PHP版本
echo "📋 检查PHP版本..."
php --version

# 检查数据库连接
echo "📋 检查数据库连接..."
mysql -u root -p123456 -e "SELECT 'Database connection OK' as status;" 2>/dev/null

# 设置文件权限
echo "📋 设置文件权限..."
chmod -R 755 runtime/
chmod 644 index.php

# 启动PHP内置服务器
echo "🌐 启动PHP开发服务器..."
echo "访问地址: http://localhost:8000"
echo "后台登录: http://localhost:8000/Login"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "================================"

php -S localhost:8000 -t .
