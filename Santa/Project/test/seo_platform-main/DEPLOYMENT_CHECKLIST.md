# SEO平台部署验证清单

## 🔍 部署前检查

### 环境要求
- [ ] PHP 7.4+ (支持PHP 8.0)
- [ ] MySQL 5.7+ 或 MariaDB 10.2+
- [ ] Nginx 或 Apache Web服务器
- [ ] PHP扩展：mysqli, curl, json, mbstring

### 文件检查
- [ ] 项目文件完整上传
- [ ] 配置文件权限正确设置
- [ ] runtime目录可写权限
- [ ] SQL文件已导入数据库

## 🚀 部署步骤验证

### 1. 数据库连接测试
```bash
# 测试数据库连接
mysql -u seo_platform -p seo_platform -e "SHOW TABLES;"
mysql -u seo_spider -p seo_spider -e "SHOW TABLES;"
```

### 2. Web服务器测试
```bash
# 检查Web服务器状态
systemctl status nginx  # 或 systemctl status apache2
systemctl status php7.4-fpm  # 或对应的PHP-FPM版本
```

### 3. 访问测试
- [ ] 访问首页：`http://your-domain.com/`
- [ ] 检查是否显示登录页面
- [ ] 测试静态资源加载（CSS/JS）

## 🔐 功能测试

### 登录功能
1. **访问后台登录**
   - URL: `http://your-domain.com/Login`
   - 默认可能需要查看数据库中的管理员账户

2. **查看默认管理员账户**
```sql
SELECT * FROM seo_users WHERE status = 1;
```

3. **Google验证码测试**
   - 检查验证码是否正常显示
   - 测试验证码验证功能

### 基本功能测试
- [ ] 登录后台管理系统
- [ ] 查看数据看板
- [ ] 测试菜单导航
- [ ] 检查权限控制功能

## 🛠 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库服务
systemctl status mysql

# 检查数据库用户权限
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User LIKE 'seo_%';"
```

#### 2. 页面显示空白
```bash
# 检查PHP错误日志
tail -f /var/log/php7.4-fpm.log

# 检查Web服务器错误日志
tail -f /var/log/nginx/error.log
```

#### 3. 权限问题
```bash
# 重新设置权限
./set_permissions.sh

# 检查Web服务器用户
ps aux | grep nginx  # 或 ps aux | grep apache
```

#### 4. 静态资源404
- 检查虚拟主机配置
- 确认public目录权限
- 验证URL重写规则

### 调试模式
临时开启调试模式（仅用于排错）：
```php
// 编辑 index.php，添加：
define('APP_DEBUG', true);
```

## 📋 部署完成确认

### 最终检查清单
- [ ] 数据库连接正常
- [ ] 后台登录成功
- [ ] 基本功能可用
- [ ] 静态资源加载正常
- [ ] 错误日志无严重错误
- [ ] 文件权限设置正确
- [ ] 安全配置已完成

### 生产环境优化
- [ ] 关闭调试模式
- [ ] 配置SSL证书
- [ ] 设置防火墙规则
- [ ] 配置日志轮转
- [ ] 设置定期备份

## 📞 技术支持

如遇到部署问题，请检查：
1. PHP版本兼容性
2. 数据库权限配置
3. Web服务器配置
4. 文件权限设置

建议保留部署日志以便问题排查。
