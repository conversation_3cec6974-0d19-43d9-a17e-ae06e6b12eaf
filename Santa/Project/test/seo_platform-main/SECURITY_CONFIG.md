# SEO平台安全配置指南

## 1. Google reCAPTCHA 配置

### 获取密钥
1. 访问 [Google reCAPTCHA 管理控制台](https://www.google.com/recaptcha/admin/create)
2. 创建新站点，选择 reCAPTCHA v2
3. 添加您的域名
4. 获取站点密钥和密钥

### 配置文件修改
编辑 `application/Common/Conf/config.php`：
```php
'SEO_GOOGLE_RECAPTCHA_PUBLIC_KEY' => '您的站点密钥',
'SEO_GOOGLE_RECAPTCHA_PRIVATE_KEY' => '您的密钥'
```

## 2. Google Authenticator 二次验证

### 默认配置
- 默认密钥：`YE5NKRPCABHLBGVZ`
- 建议在后台修改为自定义密钥

### 修改步骤
1. 登录后台管理
2. 进入：权限控制 → 管理员列表
3. 修改账户，设置新的二次验证密钥

### 推荐工具
- [Chrome 身份验证器插件](https://chromewebstore.google.com/detail/身份验证器/bhghoamapcdpbohphigoooaddinpkbai)
- Google Authenticator 手机应用

## 3. 内容端API安全

### 修改访问令牌
编辑 `application/Common/Conf/control.php`：
```php
'SEO_CONTROL_API_ACCESS_KEY' => '生成一个强随机字符串',
```

### 生成安全令牌
```bash
# 使用openssl生成随机字符串
openssl rand -base64 32

# 或使用PHP生成
php -r "echo base64_encode(random_bytes(32));"
```

## 4. 数据库安全

### 密码安全
- 修改默认数据库密码
- 使用强密码（包含大小写字母、数字、特殊字符）
- 定期更换密码

### 权限控制
- 为应用创建专用数据库用户
- 只授予必要的权限
- 禁用root用户远程访问

## 5. 文件权限安全

### 执行权限脚本
```bash
chmod +x set_permissions.sh
./set_permissions.sh
```

### 关键目录权限
- `runtime/`: 777 (可写)
- `application/Common/Conf/`: 600 (配置文件)
- `public/`: 755 (静态资源)

## 6. Web服务器安全

### 隐藏敏感目录
确保以下目录不能直接访问：
- `/application/`
- `/framework/`
- `/runtime/`
- `*.sql` 文件

### SSL证书
建议配置HTTPS：
```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    # ... 其他配置
}
```

## 7. 定期维护

### 日志监控
- 定期检查访问日志
- 监控异常访问模式
- 设置日志轮转

### 备份策略
- 定期备份数据库
- 备份配置文件
- 测试恢复流程

### 更新维护
- 定期更新PHP版本
- 更新Web服务器
- 关注安全补丁
