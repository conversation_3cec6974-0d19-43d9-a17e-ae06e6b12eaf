@charset "UTF-8"; 

a, a:focus {
	text-decoration: none
}

.xb-middle-inside, img {
	vertical-align: middle
}

blockquote, body, button, dd, dl, dt, fieldset, h1, h2, h3, h4, h5, h6,
	hr, input, lengend, li, ol, p, pre, td, textarea, th, ul {
	margin: 0;
	padding: 0
}

body, button, input, select, textarea {
	font: 12px/1 "微软雅黑", Arial, Tahoma, Helvetica, "\5b8b\4f53", sans-serif
}

body {
	position: relative
}

address, cite, dfn, em, i, var {
	font-style: normal
}

ol, ul {
	list-style: none
}

a:hover {
	text-decoration: underline;
	transition: all .5s ease 0s;
	color: #0B7DF2
}

a:focus {
	outline: 0;
	-moz-outline: none
}

button, input, select, textarea {
	font-size: 100%
}

input {
	outline: 0
}

table {
	border-collapse: collapse;
	border-spacing: 0
}

hr {
	border: none;
	height: 1px
}

textarea {
	resize: none
}

* {
	box-sizing: border-box
}

.xb-show {
	display: block
}

.xb-hidden, .xb-hide {
	display: none
}

.xb-out {
	display: table;
	width: 100%
}

.xb-inside {
	margin: 0 auto;
	width: 1100px;
	display: table
}

.bjy-admin-nav, .xb-h-10, .xb-h-100, .xb-h-15, .xb-h-20, .xb-h-25,
	.xb-h-30, .xb-h-35, .xb-h-45, .xb-h-5, .xb-h-50, .xb-h-55, .xb-h-60,
	.xb-h-65, .xb-h-70, .xb-middle-out {
	width: 100%
}

.xb-middle-out {
	display: table;
	height: 100%;
	text-align: center
}

.xb-middle-inside {
	display: table-cell
}

.xb-h-5 {
	height: 5px
}

.xb-h-10 {
	height: 10px
}

.xb-h-15 {
	height: 15px
}

.xb-h-20 {
	height: 20px
}

.xb-h-25 {
	height: 25px
}

.xb-h-30 {
	height: 30px
}

.xb-h-35 {
	height: 35px
}

.xb-h-40 {
	width: 100%;
	height: 40px
}

.xb-h-45 {
	height: 45px
}

.xb-h-50 {
	height: 50px
}

.xb-h-55 {
	height: 55px
}

.xb-h-60 {
	height: 60px
}

.xb-h-65 {
	height: 65px
}

.xb-h-70 {
	height: 70px
}

.xb-h-100 {
	height: 100px
}

.bjy-admin-nav {
	height: 30px;
	line-height: 30px;
	font-size: 14px
}

.bjy-public-jump {
	margin: 0 auto;
	padding: 50px 100px;
	width: 455px;
	height: 195px;
	border: 2px solid #00CCC0;
	border-radius: 4px;
	text-align: center
}

.bjy-public-jump .bjy-pj-word {
	width: 100%;
	height: 30px;
	line-height: 30px;
	font-size: 16px
}

.bjy-public-jump .bjy-pj-word b {
	color: #00CCC0
}

.bjy-emoji-box {
	display: none
}

.bjy-emoji-ico {
	background: 0 0;
	border: none
}

.bjy-emoji-imgs img {
	padding: 2px
}

.bjy-show-box {
	overflow-y: auto
}

.bjy-emoji-out3 .bjy-show-out {
	width: 490px;
	height: 115px;
	background: #fff;
	border-bottom: 1px solid #E1E1E1;
	z-index: 1;
	position: relative
}

.bjy-emoji-out3 .bjy-show-out .bjy-show-box {
	width: 490px;
	height: 115px;
	padding: 10px;
	overflow-y: auto
}

.bjy-emoji-out3 .bjy-show-out .bjy-emoji-ico {
	width: 25px;
	height: 25px;
	background: url(/tpl/default/User/Public/images/lqk-smile.png);
	cursor: pointer;
	position: absolute;
	left: 15px;
	top: 3px;
	z-index: 2
}

.bjy-emoji-out3 .bjy-show-out .bjy-emoji-box {
	padding: 0 5px 5px;
	width: 480px;
	background: #fff;
	border: 1px solid #eee;
	box-shadow: 2px 2px 6px #8E99A9;
	position: absolute;
	left: 0;
	top: 33px;
	z-index: 1
}

.bjy-emoji-out3 .bjy-show-out .bjy-emoji-box .bjy-e-triangle {
	position: absolute;
	left: 7px;
	top: -30px
}

.bjy-emoji-out3 .bjy-show-out .bjy-emoji-box img {
	margin: 2px;
	cursor: pointer
}

#bjy-chat-modal .modal-dialog {
	width: 710px;
	height: 570px;
	margin-top: 20px
}

#bjy-chat-modal .modal-dialog .modal-content {
	width: 710px;
	height: 570px;
	border-radius: 0;
	border: none
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-top {
	width: 710px;
	height: 45px;
	background: #00ccc0
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-top .bjy-t-myinfo {
	padding-left: 85px;
	width: 220px;
	height: 45px;
	line-height: 45px;
	font-weight: 600;
	font-size: 14px;
	float: left;
	position: relative
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-top .bjy-t-myinfo .bjy-t-avatar
	{
	width: 55px;
	height: 55px;
	border-radius: 50%;
	position: absolute;
	left: 20px;
	top: -20px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-top .bjy-t-title {
	width: 490px;
	height: 45px;
	line-height: 45px;
	text-align: center;
	float: left;
	font-size: 14px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-top .bjy-t-title .bjy-tt-name
	{
	font-weight: 600
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-top .bjy-t-title .bjy-tt-close
	{
	margin: 10px;
	width: 20px;
	height: 20px;
	background: url(/tpl/default/User/Public/images/f-x.png) no-repeat;
	float: right;
	cursor: pointer
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat {
	width: 710px;
	height: 525px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list
	{
	width: 220px;
	height: 525px;
	float: left;
	background: url(/Public/statics/rongcloud/images/bg_left.min.png);
	overflow-y: auto;
	overflow-x: hidden
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one
	{
	padding: 15px 5px 15px 10px;
	width: 220px;
	height: 65px;
	border-bottom: 2px solid #C3E8F0;
	cursor: pointer
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-avatar
	{
	margin-right: 10px;
	width: 35px;
	height: 35px;
	float: left;
	position: relative
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-avatar .bjy-floa-img
	{
	width: 35px;
	height: 35px;
	border-radius: 3px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-avatar .bjy-floa-approve
	{
	width: 15px;
	height: 15px;
	position: absolute;
	right: -2px;
	bottom: -2px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-avatar .bjy-flo-count
	{
	width: 15px;
	height: 15px;
	line-height: 15px;
	text-align: center;
	background: #ff4f5f;
	border-radius: 50%;
	color: #fff;
	position: absolute;
	right: -2px;
	top: -2px;
	display: none
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-avatar .xb-show
	{
	display: block
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-info
	{
	width: 160px;
	height: 35px;
	font-size: 14px;
	float: left
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-info .bjy-flo-username
	{
	width: 160px;
	height: 15px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-info .bjy-flo-username .bjy-flou-time
	{
	float: right;
	font-size: 12px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-fl-one .bjy-flo-info .bjy-flo-school
	{
	margin-top: 5px;
	width: 160px;
	height: 15px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-friend-list .bjy-flo-checked
	{
	background: #fff
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box {
	width: 490px;
	height: 525px;
	float: left
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history
	{
	padding: 10px;
	width: 490px;
	height: 325px;
	overflow-y: auto;
	overflow-x: hidden
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbh-one
	{
	margin-bottom: 20px;
	padding: 0 50px;
	width: 470px;
	position: relative
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbh-one .bjy-cbhl-avatar
	{
	width: 50px;
	height: 50px;
	position: absolute
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbh-one .bjy-cbhl-avatar .bjy-cbhla-img
	{
	width: 35px;
	height: 35px;
	border-radius: 3px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbh-one .bjy-cbhl-avatar .bjy-cbhla-approve
	{
	width: 15px;
	height: 15px;
	position: absolute
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbh-one .bjy-cbhl-avatar .bjy-cbhla-triangle
	{
	width: 10px;
	height: 20px;
	position: absolute
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbh-one .bjy-cbhl-content
	{
	width: 370px;
	line-height: 20px;
	padding: 10px;
	border-radius: 5px;
	font-size: 14px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-left .bjy-cbhl-avatar
	{
	left: 0;
	top: 0
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-left .bjy-cbhl-avatar .bjy-cbhla-approve
	{
	left: 22px;
	top: 22px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-left .bjy-cbhl-avatar .bjy-cbhla-triangle
	{
	left: 44px;
	top: 10px;
	background: url(/Public/statics/rongcloud/images/triangle-left-bjy.png)
		no-repeat
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-left .bjy-cbhl-content
	{
	background: #e6ecff
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-right .bjy-cbhl-avatar
	{
	right: -10px;
	top: 0
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-right .bjy-cbhl-avatar .bjy-cbhla-approve
	{
	right: 37px;
	top: 22px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-right .bjy-cbhl-avatar .bjy-cbhla-triangle
	{
	right: 50px;
	top: 10px;
	background: url(/Public/statics/rongcloud/images/triangle-right-bjy.png)
		no-repeat
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-history .bjy-cbhla-right .bjy-cbhl-content
	{
	background: #d7efff
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-middle
	{
	width: 490px;
	height: 30px;
	background: #eee;
	position: relative
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-sendbox
	{
	width: 490px;
	height: 115px
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-sendbox .bjy-cbs-content
	{
	padding: 10px;
	width: 490px;
	line-height: 20px;
	font-size: 14px;
	border: none
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-handle
	{
	padding-left: 310px;
	padding-bottom: 25px;
	width: 490px;
	height: 30px;
	overflow: hidden
}

#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-handle .bjy-cbh-close,
	#bjy-chat-modal .modal-dialog .modal-content .bjy-chat .bjy-chat-box .bjy-cb-handle .bjy-cbh-send
	{
	margin-left: 10px;
	width: 70px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	background: #00ccc0;
	font-size: 14px;
	color: #000;
	border-radius: 5px;
	float: left;
	cursor: pointer
}

fieldset{padding:.35em .625em .75em;margin:0 2px;border:1px dotted #438eb9}

legend{padding:.5em;border:0;width:auto}


/*@media screen and (min-width:980px){*/
/*	.spider {*/
/*		margin-left: 1.99%;*/
/*		!*padding-left:15px;*!*/
/*		!*padding-right: 15px;*!*/
/*	}*/
/*}*/


.chart-container {
	width: auto;
	height: 400px;
	margin-top: 20px;
}
/*.infobox{*/
/*	width:auto;*/
/*	border:0;*/
/*	border-radius:10px;*/
/*	-moz-border-radius:10px; !* 老的 Firefox *!*/
/*	-webkit-border-radius: 10px;*/
/*	background-color: #20c997;*/
/*}*/
/*.infobox > * {*/
/*	color:#ffffff;*/
/*}*/
/*.infobox-data-number{*/
/*	padding-left: 45px;*/
/*}*/

/*.infobox-content{*/
/*	padding-left: 30px;*/
/*	color: #ffffff;*/
/*}*/



/*fieldset {*/
/*	padding: .35em .625em .75em;*/
/*	margin: 0 2px;*/
/*	border: 1px dotted #438eb9;*/
/*}*/

/*legend {*/
/*	padding: .5em;*/
/*	border: 0;*/
/*	width: auto;*/
/*}*/

/*.spider-total {*/
/*	display: flex;*/
/*	flex-wrap: wrap;*/
/*	justify-content: center;*/
/*	gap: 20px; !* 基础间距 *!*/
/*	padding-bottom: 2em;*/
/*}*/

/*!* 大屏幕时增大间距 *!*/
/*@media screen and (min-width: 1400px) {*/
/*	.spider-total {*/
/*		gap: 40px;*/
/*	}*/
/*}*/

/*@media screen and (min-width: 1800px) {*/
/*	.spider-total {*/
/*		gap: 60px;*/
/*	}*/
/*}*/

/*!* 中等屏幕时保持一行，并减小间距 *!*/
/*@media screen and (max-width: 1200px) {*/
/*	.spider-total {*/
/*		gap: 10px;*/
/*	}*/
/*}*/

/*!* 小屏幕时调整为两列 *!*/
/*@media screen and (max-width: 768px) {*/
/*	.spider-total {*/
/*		gap: 10px;*/
/*		justify-content: space-around;*/
/*	}*/
/*	.spider {*/
/*		flex: 1 1 calc(50% - 20px); !* 两列布局，间距为 20px *!*/
/*		max-width: 100%;*/
/*	}*/
/*}*/

/*!* 超小屏幕时调整为一列 *!*/
/*@media screen and (max-width: 480px) {*/
/*	.spider {*/
/*		flex: 1 1 100%;*/
/*		max-width: 100%;*/
/*	}*/
/*}*/

/*.infobox {*/
/*	width: calc(33% - 2em); !* 使每个 .infobox 占三分之一的宽度，减去间距 *!*/
/*	border: 0;*/
/*	border-radius: 10px;*/
/*	background-color: #20c997;*/
/*	margin-bottom: 1em; !* 为每个 infobox 添加底部间距 *!*/
/*}*/

/*.infobox > * {*/
/*	color: #ffffff;*/
/*}*/

/*.infobox-data-number {*/
/*	padding-left: 45px;*/
/*}*/

/*.infobox-content {*/
/*	padding-left: 30px;*/
/*	color: #ffffff;*/
/*}*/


/* 移除容器的 max-width 限制 */
/*.layui-container {*/
/*	width: 100%;*/
/*	padding: 0; !* 移除左右内边距 *!*/
/*}*/
/*.spider-container {*/
/*	display: flex;*/
/*	flex-wrap: wrap;*/
/*	gap: 30px; !* 控制间距 *!*/
/*	justify-content: flex-start; !* 左对齐 *!*/
/*}*/

/*.spider-container {*/
/*	display: flex;*/
/*	flex-wrap: wrap;*/
/*	gap: 30px; !* 控制间距 *!*/
/*	justify-content: center; !* 居中对齐 *!*/
/*}*/
/*.spider-box {*/
/*	position: relative;*/
/*	flex: 1 1 150px; !* 每个box的最小宽度 *!*/
/*	max-width: 200px;*/
/*	text-align: center;*/
/*	padding: 20px;*/
/*	color: white;*/
/*	border-radius: 8px;*/
/*}*/

/*.layui-container {*/
/*	width: 100%;*/
/*	padding: 0; !* 移除左右内边距 *!*/
/*}*/
.spider-container {
	display: flex;
	flex-wrap: wrap;
	gap: 30px; /* 控制间距 */
	justify-content: center; /* 居中对齐 */
}
.spider-box {
	position: relative;
	flex: 0 1 calc(20% - 30px); /* 每行最多5个，减去间距 */
	max-width: calc(20% - 30px); /* 确保宽度一致 */
	text-align: center;
	padding: 20px;
	color: white;
	border-radius: 8px;
}
.spider-box h2 {
	font-size: 24px;
	margin: 0;
}
.spider-box p {
	font-size: 14px;
}
.spider-logo {
	position: absolute;
	bottom: 10px;
	right: 10px;
	width: 24px; /* 调整大小 */
	height: 24px; /* 调整大小 */
	object-fit: contain; /* 确保图片比例不失真 */
}

/*.spider-logo {*/
/*	position: absolute;*/
/*	bottom: 10px;*/
/*	right: 10px;*/
/*	width: 20px; !* Adjust size as needed *!*/
/*	height: 20px; !* Adjust size as needed *!*/
/*}*/
/*.baidu { background-color: #34495e; } !* 深灰蓝，稳重且高对比 *!*/
/*.sougou { background-color: #f39c12; } !* 柔和橙色，清新醒目 *!*/
/*.s360 { background-color: #27ae60; } !* 扁平化绿色，代表安全与环保 *!*/
/*.shenma { background-color: #9b59b6; } !* 柔和紫色，典雅独特 *!*/
/*.google { background-color: #2980b9; } !* 深蓝，清晰且品牌感强 *!*/
/*.bing { background-color: #3498db; } !* 天蓝色，科技感十足 *!*/
/*.yandex { background-color: #e74c3c; } !* 扁平红色，吸引注意力 *!*/
/*.cococ { background-color: #f1c40f; } !* 明亮黄，寓意积极和能量 *!*/
/*.naver { background-color: #2ecc71; } !* 鲜绿色，清新明快 *!*/
/*.total { background-color: #e67e22; } !* 温暖橙色，汇总突出 *!*/



.baidu { background-color: #57606f; } /* 深灰蓝，低调简约 */
.sougou { background-color: #ffa502; } /* 柔和橙，兼具活力与优雅 */
.s360 { background-color: #2ed573; } /* 苹果绿，清新自然 */
.shenma { background-color: #a29bfe; } /* 浅紫蓝，优雅平静 */
.google { background-color: #1e90ff; } /* 苹果蓝，突出科技感 */
.bing { background-color: #3742fa; } /* 深蓝色，稳重大气 */
.yandex { background-color: #ff4757; } /* 苹果红，突显热情 */
.cococ { background-color: #feca57; } /* 柔和黄，视觉温暖 */
.naver { background-color: #1dd1a1; } /* 浅薄荷绿，轻快清爽 */
.total { background-color: #ff6b81; } /* 柔粉红，突出总结的温和感 */

/* 内容布局 */
.content-row {
	display: flex;
	gap: 20px;
}

.layui-container {
	width: 100%;
	padding: 0; /* 移除左右内边距 */
	/*margin-top: 20px; !* 保证从顶部元素下方有间距 *!*/
}



.spider-grid {
	display: flex;
	flex-wrap: nowrap; /* 禁止换行 */
	gap: 30px; /* 控制子项之间的间距 */
	justify-content: flex-start; /* 左对齐 */
	align-items: flex-start; /* 顶部对齐 */
	overflow-x: auto; /* 如果内容超出容器宽度，启用水平滚动 */
	padding-left: 20px; /* 与左侧保持一定间距 */
	margin-left: auto; /* 让整个内容居左对齐 */
}

/*.spider-item {*/
/*	display: flex;*/
/*	align-items: center;*/
/*	justify-content: space-between; !* 左右两端对齐 *!*/
/*	padding: 10px 15px;*/
/*	border: 1px solid #eaeaea;*/
/*	border-radius: 8px;*/
/*	background-color: #f9f9f9;*/
/*	transition: transform 0.2s ease, box-shadow 0.2s ease;*/
/*	width: 180px; !* 固定宽度 *!*/
/*	max-width: 100%; !* 确保不超出容器 *!*/
/*	text-align: left; !* 左对齐内容 *!*/
/*	box-sizing: border-box; !* 包含内边距在宽度内 *!*/
/*	overflow: hidden; !* 避免内容溢出 *!*/
/*}*/

.spider-stats{
	padding-left: 20px;
}

.spider-container {
	display: flex;
	flex-wrap: wrap;  /* 允许换行 */
	gap: 10px;        /* 默认间距 */
	justify-content: space-between; /* 在大屏下，自动分布元素，避免挤在一起 */
}

.spider-item {
	display: flex;
	flex-direction: column;  /* 垂直布局 */
	align-items: center;     /* 内容居中 */
	justify-content: flex-start; /* 从上到下排列 */
	padding: 15px;
	border: 1px solid #eaeaea;
	border-radius: 8px;
	background-color: #f9f9f9;
	transition: transform 0.2s ease, box-shadow 0.2s ease;
	flex-grow: 1; /* 让元素动态调整宽度以填充多余空间 */
	flex-basis: calc(10% - 10px); /* 默认宽度，适配大屏 */
	max-width: calc(10% - 10px); /* 限制最大宽度，避免过大 */
	box-sizing: border-box;  /* 包含内边距和边框 */
}

.spider-item img {
	width: 30px;
	height: 30px;
	margin-bottom: 10px;
}

.spider-text {
	font-size: 14px;
	color: #333;
	margin-bottom: 5px;
	text-align: center;
}

.spider-count {
	font-weight: bold;
	color: #555;
	text-align: center;
}

/* 悬停交互效果 */
.spider-item:hover {
	transform: translateY(-3px); /* 上移效果 */
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* 添加阴影 */
	background-color: #ffffff;
	border-color: #dcdcdc;
}

.spider-item:hover .spider-text {
	color: #007aff; /* 苹果风格蓝色 */
}

.spider-item:hover .spider-count {
	color: #ff3b30; /* 苹果风格红色 */
}


/* 响应式布局调整 */
@media (max-width: 1400px) {
	.spider-item {
		flex-basis: calc(14.2857% - 10px); /* 每行7个 */
		max-width: calc(14.2857% - 10px);
	}
}

@media (max-width: 992px) {
	.spider-item {
		flex-basis: calc(20% - 10px); /* 每行5个 */
		max-width: calc(20% - 10px);
	}
}

@media (max-width: 768px) {
	.spider-item {
		flex-basis: calc(33.3333% - 10px); /* 每行3个 */
		max-width: calc(33.3333% - 10px);
	}
}

@media (max-width: 576px) {
	.spider-item {
		flex-basis: calc(50% - 10px); /* 每行2个 */
		max-width: calc(50% - 10px);
	}
}

