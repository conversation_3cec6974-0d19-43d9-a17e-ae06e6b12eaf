<link rel="stylesheet" href="https://cdn.staticfile.org/layui/2.6.8/css/layui.min.css" />
 <div class="layui-tab layui-tab-card" lay-filter="add-project-tab">
        <ul class="layui-tab-title">
            <li class="layui-this"><i class="layui-icon layui-icon-username"></i> 基础信息</li>
            <li><i class="layui-icon layui-icon-set"></i> SEO选项</li>
            <li><i class="layui-icon layui-icon-website"></i> 内链设置</li>
            <li><i class="layui-icon layui-icon-link"></i> 外链设置</li>
            <li><i class="layui-icon layui-icon-print"></i> 缓存设置</li>
            <li><i class="layui-icon layui-icon-rmb"></i> 广告设置</li>
            <li><i class="layui-icon layui-icon-note"></i> 推送设置</li>
            <li><i class="layui-icon layui-icon-layer"></i> 其他设置</li>
        </ul>
        <div class="layui-tab-content" style="height: 450px;">
            <form class="layui-form" action="{:U('platform/project/table?action=add')}" method="post">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form-pane">
                        <div class="layui-form-item">
                            <label class="layui-form-label" pane>项目类型</label>
                            <div class="layui-input-block">
                                <select name="project_type" lay-verify="required" lay-reqText="请选择项目类型！">
                                    <option value=""></option>
                                    <option value="0">泛目录</option>
                                    <option value="1">动态寄生虫</option>
                                    <option value="2">Global</option>
                                    <option value="3">URL重写</option>
                                    <option value="4">404劫持</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目标识</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_name" required  lay-verify="required" lay-reqText="项目标识必须填写" placeholder="新的项目" autocomplete="on" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item" id="singe-project">
                            <label class="layui-form-label">项目地址</label>
                            <div class="layui-input-inline" style="width: 695px;">
                                <input type="text" name="singe_project_url"  placeholder="http://www.example.com/" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">  <a href="javascript:;" title="批量添加项目" id="batch" style="text-decoration:none;"><i class="layui-icon layui-icon-add-circle-fine"></i></a></div>
                        </div>
                        <input type="hidden" name="mult_option" id="mult-option" value="0">
                        <div class="layui-form-item" id="mult-project" style="display: none;">
                            <label class="layui-form-label">项目地址</label>
                            <div class="layui-input-block">
                                <textarea name="mult_project_url" id="mult_project_url" placeholder="一行代表一个地址" class="layui-textarea" style="height: 250px;"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目备注</label>
                            <div class="layui-input-block">
                                <input type="text" name="rankname" required  lay-verify="required" lay-reqText="项目备注必须填写" placeholder="请输入項目备注"  autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目监控</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="monitor_status" lay-skin="switch" lay-text="开启|关闭">
                            </div>
                            <div class="layui-form-mid layui-word-aux">开启项目监控后，在“其他设置”选项中配置监控内容</div>

                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站编码</label>
                            <div class="layui-input-block">
                                <select name="project_charset" lay-verify="required" lay-reqText="请选择网站编码~">
                                    <option value=""></option>
                                    <option value="0">UTF-8</option>
                                    <option value="1">GBK</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>  <!-- 优化设置 -->
                <div class="layui-tab-item">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">词 库</label>
                            <div class="layui-input-block">
                                <div id="kw_group"></div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">模 板</label>
                            <div class="layui-input-block">
                                <div id="project_template"></div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" pane>标题</label>
                            <div class="layui-input-block">
                                <!--                                        <select name="title_style" lay-verify="required" multiple lay-reqText="标题样式还没有选择呐">-->
                                <!--                                            <option value=""></option>-->
                                <!--                                            <option value="0">{主关键词}</option>-->
                                <!--                                            <option value="1">{主关键词} - {副标题}</option>-->
                                <!--                                            <option value="2">{主关键词} - {关联关键词}</option>-->
                                <!--                                            <option value="3">{主关键词} -{关联关键词1}-{关联关键词2}</option>-->
                                <!--                                            <option value="4">{主关键词}:{文章标题}</option>-->
                                <!--                                        </select>-->
                                <div id="title_style"></div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label" pane>关键词</label>
                            <div class="layui-input-block">
                                <!--                                        <select name="keyword_style" lay-verify="required" lay-reqText="关键词对于SEO非常重要嚄~">-->
                                <!--                                            <option value=""></option>-->
                                <!--                                            <option value="0">{主关键词}</option>-->
                                <!--                                            <option value="2">{主关键词},{关联关键词}</option>-->
                                <!--                                            <option value="3">{主关键词},{关联关键词1},{关联关键词2}</option>-->
                                <!--                                            <option value="4">{随机关键词}</option>-->
                                <!--                                        </select>-->
                                <div id="keyword_style"></div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">描述</label>
                            <div class="layui-input-block">
                                <!--                                            <select name="description_style" lay-verify="required" lay-reqText="描述对于优化同样重要">-->
                                <!--                                                <option value=""></option>-->
                                <!--                                                <option value="0">{主关键词}{文章正文}</option>-->
                                <!--                                                <option value="2">主关键词随机插入正文</option>-->
                                <!--                                                <option value="3">{主关键词}</option>-->
                                <!--                                                <option value="4">{来自描述文件}</option>-->
                                <!--                                            </select>-->
                                <div id="description_style"></div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">TDK方式</label>
                            <div class="layui-input-block">
                                <select name="tdk_encode">
                                    <option  value="">请选择</option>
                                    <option  value="ascii">ASCII</option>
                                    <option  value="unicode">UNICODE</option>
                                    <option  value="none">None</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label title">TDK范围</label>
                            <div class="layui-input-block">
                                <input type="checkbox" id="tdk_encode_style" name="tdk_encode_style[]" title="Title" value="title">
                                <input type="checkbox" id="tdk_encode_style" name="tdk_encode_style[]" title="Keyword" value="keyword">
                                <input type="checkbox" id="tdk_encode_style" name="tdk_encode_style[]" title="Description" value="description">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label title">文章库</label>
                            <div class="layui-input-block">
                                <select name="article_source" id="project_article" lay-filter="required" lay-reqtext="请选择文章表~">
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label title">标题库</label>
                            <div class="layui-input-block">
                                <select name="title_source" id="project_title" lay-filter="required" lay-reqtext="请选择标题表~">
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label title">图片</label>
                            <div class="layui-input-block">
                                <select name="pic_source" id="project_pic" lay-filter="required" lay-reqtext="请选择图片源~">
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label title"  style="">页面语言(*)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="page_language"  placeholder="页面语言" lay-reqtext="页面语言不能为空~"value="<?php echo($info['seo_option']['page_language']);?>" autocomplete="off" class="layui-input" style="width: 80px;">
                                <!-- https://www.w3schools.com/tags/ref_language_codes.asp 这里有全部的ISO代码 符合这里即可 -->
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">模板混淆</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="encode_style[]" title="class混淆" value="class">
                                <input type="checkbox" name="encode_style[]" title="id混淆" value="id">
                                <input type="checkbox" name="encode_style[]" title="标签混淆" value="tag">
                            </div>
                            <div class="layui-form-mid layui-word-aux" id="encode_style_tips">模板混淆后，起到一定的防K作用</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">页面压缩</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" name="page_compress" lay-skin="switch" lay-text="ON|OFF" checked>
                            </div>
                            <div class="layui-form-mid layui-word-aux">压缩过的页面比原体积小 搜索引擎更喜欢</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">禁止快照</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" name="disable_snapshots" lay-skin="switch" lay-text="ON|OFF" checked=""/>
                            </div>
                            <div class="layui-form-mid layui-word-aux">快照禁止后在搜索引擎无法演示快照内容，但不影响排名</div>
                        </div>
                    </div>
                </div>
                <!-- 内链URL设置 -->
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>快速规则</label>
                        <div class="layui-input-block">
                            <select name="quick_rules" lay-filter="quick_rules">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">URL规则</label>
                        <div class="layui-input-block">
                            <textarea name="ilink_rules" id="url_rules" required  lay-verify="required" lay-reqText="URL规则必须填写" placeholder="一行代表一个规则 标签参照下方提示" class="layui-textarea" style="height: 200px;"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label title"  style="">标签提示</label>
                        <div class="layui-input-block">
                            <blockquote class="layui-elem-quote layui-quote-nm">
                                字符标签：{数字}、{字母}、{大写字母}、{大小写字母}、{大写字母数字}、{大小写字母数字}、{数字字母}、{随机字符}<br/>
                                时间标签：{日期}、{年}、{月}、{日}、{时}、{分}、{秒}<br/>
                                动态标签：数字和字母标签后面加数字是位数，如： {数字8}表示8个数字、{数字1-8}示随机1-8个数字
                            </blockquote>
                        </div>
                    </div>
                </div>
                <!-- 外链设置 -->
                <div class="layui-tab-item">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label" pane>外链类别</label>
                            <div class="layui-input-block">
                                <select name="elink_class" lay-verify="required" lay-reqText="外链对于SEOer也是重要的哦">
                                    <option value=""></option>
                                    <option value="0">使用系统外链</option>
                                    <option value="1">使用自定义外链表</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">自定义外链</label>
                            <div class="layui-input-block">
                                <textarea name="elink_rules" id="flink_rule" placeholder="一行代表一个规则" class="layui-textarea" style="height: 200px;"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 缓存设置 -->
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>缓存开关</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="cache_option" lay-skin="switch" lay-filter="cache_on" lay-text="开启|关闭" checked>
                        </div>
                        <div class="layui-form-mid layui-word-aux">缓存关闭后页面将动态更新</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>缓存方法</label>
                        <div class="layui-input-inline">
                            <select name="cache_type" id="cache_class" lay-filter="cache_class">
                                <option value="">请选择</option>
                                <optgroup label="局部缓存">
                                    <option value="0">缓存TKD</option>
                                    <option value="1" selected>缓存TKDB</option>
                                </optgroup>
                                <optgroup label="全局缓存">
                                    <option value="2">全部缓存</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="layui-form-mid layui-word-aux" id="cache_tips">请选择缓存方法</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">缓存周期</label>
                        <div class="layui-input-inline">
                            <input type="text" name="cache_expires" id="cache_expires" required  lay-verify="required|number" placeholder="请输入缓存周期，0为无限期" autocomplete="off" value="0" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux" id="cache_expires_tips">单位：天 如果大于缓存有效期则会强制删除缓存</div>
                    </div>
                </div>
                <!-- 广告设置 -->
                <div class="layui-tab-item">
                    <div class="layui-form-pane">
                        <div class="layui-form-item">
                            <label class="layui-form-label">地区屏蔽</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="ban_location_option" lay-skin="switch" lay-text="开启|关闭">
                            </div>
                            <div class="layui-form-mid layui-word-aux" id="ban_location_opt">开启后会在跳转的页面进行地区判断</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">地 区</label>
                            <div class="layui-input-block">
                                <input type="text" name="ban_location"  placeholder="请输入被屏蔽的地区 如北京|上海等" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux" id="ban_location_tips">被屏蔽的地区会显示404错误页面</div>
                        </div>
                        <!--                            <div class="layui-form-item">-->
                        <!--                                <label class="layui-form-label">404 页</label>-->
                        <!--                                <div class="layui-input-block">-->
                        <!--                                    <input type="text" name="error_page" placeholder="网站的404错误页" autocomplete="off" class="layui-input">-->
                        <!--                                </div>-->
                        <!--                            </div>-->

                        <div class="layui-form-item">
                            <label class="layui-form-label">广告JS</label>
                            <div class="layui-input-block">
                                <input type="text" name="ad_js" required  lay-reqText="你还没有填写js地址" lay-verify="required|url" placeholder="https://www.example.com/advertise.js" autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">错误页</label>
                            <div class="layui-input-block">
                                    <textarea name="error_page" id="error_page" required lay-reqText="错误页必填" placeholder="可以自定义一个错误页" class="layui-textarea" style="height: 200px;">
<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>404 Not Found</title>
</head><body>
<h1>Not Found</h1>
<p>The requested URL {$path}  was not found on this server.</p>
</body></html>
</textarea>
                            </div>
                        </div>


                    </div>
                </div>
                <!-- 推送设置 -->
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>神马推送</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="shenma_push" lay-skin="switch" lay-filter="shenma_push" lay-text="开启|关闭">
                        </div>
                        <div class="layui-form-mid layui-word-aux">把URL提交给神马搜索引擎，加快搜录</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">神马Token</label>
                        <div class="layui-input-block">
                            <input type="text" name="shenma_push_token" placeholder="神马推送token 需在神马站长平台获取" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>百度推送</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="baidu_push" lay-skin="switch" lay-filter="baidu_push" lay-text="开启|关闭">
                        </div>
                        <div class="layui-form-mid layui-word-aux">把URL提交给百度搜索引擎，加快搜录</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">百度Token</label>
                        <div class="layui-input-block">
                            <input type="text" name="baidu_push_token" placeholder="百度推送token 需在百度站长平台获取" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <!-- 异常报告 -->
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>跳转异常</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="jump_exception" lay-skin="switch" lay-filter="baidu_push" lay-text="开启|关闭">
                        </div>
                        <div class="layui-form-mid layui-word-aux">模拟跳转，获取返回结果 如有异常将会推送到已配置好的“消息通知”</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>模拟异常</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="spider_exception" lay-skin="switch" lay-filter="baidu_push" lay-text="开启|关闭">
                        </div>
                        <div class="layui-form-mid layui-word-aux">模拟抓取，获取返回结果 如有异常将会推送到已配置好的“消息通知”</div>

                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" pane>网站异常</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="website_exception" lay-skin="switch" lay-filter="baidu_push" lay-text="开启|关闭">
                        </div>
                        <div class="layui-form-mid layui-word-aux">网站异常，如 502 404 或链接错误将会推送到已配置好的“消息通知”</div>
                    </div>
                </div>
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md5" style="padding-left: 40%;">
                        <button class="layui-btn layui-btn-lg layui-btn-radius layui-btn-normal" lay-submit lay-filter="project-add">创建项目</button>
                    </div>
                </div>
            </form>
        </div>
        <!--    </div>-->
    </div>
</div>