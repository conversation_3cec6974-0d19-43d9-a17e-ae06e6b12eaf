<extend name="Public:base"/>
<block name="title"><?php echo($info['bind_domain']) ?> > - 配置详情</block>
<block name="content">
  <div class="page-header"><h1> <?php echo($info['bind_domain']) ?> &gt; 配置详情</h1></div>
  <style>
    .title{
      width: 150px;
    }

    .label-xxl{
      width: 170px;
    }


    .layui-input-block > input[type='text']{
      width: 30%;
    }
    .layui-input-block > textarea{
      width: 30%;
    }
    .layui-input-block > blockquote{
      width: 55%;
      margin-left: 40px;
    }
    #kw_group, #title_style, #keyword_style, #description_style, #project_template{
      width: 30%;
      margin-left: 40px;
    }
    .type_tips {
      margin-top:11px;
      margin-left: 5px;
    }
    /**
    切换接口类型css
     */

    #switch-type-dialog .layui-form-item {
      margin-bottom: 20px; /* 增加间距 */
    }

    #switch-type-dialog .layui-input-block {
      display: flex;
      justify-content: center; /* 强制居中 */
      align-items: center; /* 垂直居中 */
    }

    #switch-type-dialog .layui-btn {
      width: 150px; /* 设置按钮宽度 */
    }

    #switch-type-dialog .layui-text {
      text-align: center;
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    #switch-type-dialog .layui-form-label {
      font-weight: bold;
      color: #333;
    }

    #switch-type-dialog .layui-form-item .layui-input-block input[type="radio"] {
      margin-right: 20px;
    }

    #switch-type-dialog .layui-btn-normal {
      background-color: #28a745;
      border-color: #28a745;
    }

    #switch-type-dialog .layui-btn-normal:hover {
      background-color: #218838;
      border-color: #1e7e34;
    }

    /* 保证 infobox 的大小和图标都显示得很整洁 */
    .infobox-container {
      display: flex;
      justify-content: space-between;  /* 均匀分布 */
      flex-wrap: wrap;  /* 支持响应式折行 */
    }

    /* 调整 infobox 内的排版，使内容居中 */
    .infobox {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 15px;
      margin-right: 10px; /* 增加项之间的间距 */
      border-radius: 5px;
      color: #fff;
    }

    /* 保证 infobox 的大小和图标都显示得很整洁 */
    .infobox-container {
      display: flex;
      justify-content: space-between;  /* 均匀分布 */
      flex-wrap: wrap;  /* 支持响应式折行 */
    }

    /* 调整 infobox 内的图标和文字排版 */
    /*.infobox {*/
    /*  display: flex;*/
    /*  align-items: center;*/
    /*  padding: 10px 15px;*/
    /*  margin-right: 10px; !* 增加项之间的间距 *!*/
    /*  border-radius: 5px;*/
    /*  color: #fff;*/
    /*}*/

    /* 保证 infobox 的大小和图标都显示得很整洁 */
    .infobox-container {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 10px; /* 各项之间的间距 */
    }

    /* 基本的 infobox 样式 */
    .infobox {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 10px 15px;
      border-radius: 5px;
      color: #fff;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* 各个 infobox 背景颜色 */
    .infobox-orange {
      background-color: #f39c12; /* 橙色 */
    }

    .infobox-green {
      background-color: #27ae60; /* 绿色 */
    }

    .infobox-blue {
      background-color: #3498db; /* 蓝色 */
    }

    .infobox-blue2 {
      background-color: #1abc9c; /* 另一种蓝色 */
    }

    .infobox-grey {
      border-color: #1abc9c;
    }

    /* 图标样式 */
    .infobox-icon {
      font-size: 30px; /* 设置图标大小 */
      margin-right: 10px; /* 图标与内容之间的间隔 */
    }

    /* 数据的样式 */
    .infobox-data-number {
      font-size: 18px; /* 小字体 */
      font-weight: bold;
      display: block;
      text-align: center;
    }

    .infobox-data-number > a{
        color: #F2F2F2;
    }
    /* 项目名称的样式 */
    .infobox-content {
      font-size: 16px; /* 中号字体 */
      text-align: center;
    }

    /* 响应式设计，适配屏幕较小的设备 */
    @media (max-width: 768px) {
      .infobox-container {
        flex-direction: column;
      }

      .infobox {
        margin-bottom: 15px;
      }
    }

    .clear-cache-container {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ff4d4f;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .clear-cache-container:hover {
      background-color: #d9363e;
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .clear-cache-icon {
      font-size: 16px;
      color: #fff;
      transition: color 0.3s ease;
    }

    .clear-cache-container:hover .clear-cache-icon {
      color: #f5f5f5;
    }



  </style>
  <div class="col-xs-12">
    <div class="infobox-container" style="display: flex; justify-content: space-between;">
      <!-- 托管数量 -->
      <div class="infobox infobox-orange" style="flex: 1; margin-right: 10px;">
        <div class="infobox-icon">
          <i class="ace-icon fa fa-database"></i>
        </div>
        <div class="infobox-data">
          <span class="infobox-data-number"><a href="javascript:;" id="show-lite-total">加载中...</a></span>
          <div class="infobox-content">托管数量</div>
        </div>
      </div>

      <!-- 首页数量 -->
      <div class="infobox infobox-blue2" style="flex: 1; margin-right: 10px;">
        <div class="infobox-icon">
          <i class="ace-icon fa fa-print"></i>
        </div>
        <div class="infobox-data">
          <span class="infobox-data-number" id="index-total">加载中...</span>
          <div class="infobox-content">首页缓存数</div>
        </div>
      </div>

      <!-- 缓存数量 -->
      <div class="infobox infobox-green" style="flex: 1; margin-right: 10px;">
        <div class="infobox-icon">
          <i class="ace-icon fa fa-flask"></i>
        </div>
        <div class="infobox-data">
          <span class="infobox-data-number" id="lite-cache">加载中...</span>
          <div class="infobox-content">内容缓存数</div>
        </div>
      </div>

      <!-- 占用空间 -->
      <div class="infobox infobox-blue" style="flex: 1; margin-right: 10px;">
        <div class="infobox-icon">
          <i class="ace-icon fa fa-hdd-o"></i>
        </div>
        <div class="infobox-data">
          <span class="infobox-data-number" id="storage-total">加载中...</span>
          <div class="infobox-content">占用空间</div>
        </div>
      </div>

      <!-- Sitemap 数量 -->
      <div class="infobox infobox-blue2" style="flex: 1; margin-right: 10px;">
        <div class="infobox-icon">
          <i class="ace-icon fa fa-sitemap"></i>
        </div>
        <div class="infobox-data">
          <span class="infobox-data-number" id="sitemap-total">加载中...</span>
          <div class="infobox-content">Sitemap </div>
        </div>
      </div>
    </div>
  </div>




  <form class="layui-form" action="{:U('platform/project/port_edit?cid=')}<?php echo(intval($_GET['cid']));?>" method="post">

    <fieldset>
      <legend>首页设置</legend>
      <div class="layui-form-item">
        <label class="layui-form-label title" pane>首页劫持开关</label>
        <div class="layui-input-block">
          <input type="checkbox" name="index_jet" lay-skin="switch" lay-filter="index_jet" lay-text="开启|关闭" <?php if($info['seo_option']['index_jet'] == 'on'):?> checked<?php endif;?>>
        </div>
        <div class="layui-form-mid layui-word-aux">开启后可以做首页类型的关键词</div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label title" pane>首页内容替换</label>
        <div class="layui-input-block">
          <input type="checkbox" name="replace_home_link" lay-skin="switch" lay-filter="index_jet" lay-text="开启|关闭" <?php if($info['seo_option']['replace_home_link'] == 'on'):?> checked<?php endif;?>>
        </div>
        <div class="layui-form-mid layui-word-aux">开启后可以首页内容替换为内链（规则在内容设置）</div>
      </div>
    </fieldset>


      <fieldset>
        <legend>内容设置</legend>

        <div class="layui-form-item" style="">
          <label class="layui-form-label" style="width:150px;">清除内容</label>
          <div class="layui-input-block">
            <button class="layui-btn layui-btn-danger layui-btn-sm" id="clear_content_cache" style="border-radius: 5px;">清除内容缓存</button>
            <button class="layui-btn layui-btn-danger layui-btn-sm" id="clear_sitemap_cache" style="border-radius: 5px;">清除sitemap缓存</button>
<!--            <button class="layui-btn layui-btn-danger layui-btn-sm" id="clear_sitemap_cache" style="border-radius: 5px;">清除托管数量历史</button>-->

          </div>
          <div class="layui-form-mid layui-word-aux" id="clean_tips">缓存无效或需要清理时使用，请勿随意清除缓存</div>

        </div>

        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">接口类型</label>
          <div class="layui-input-inline">
            <?php if($info['type'] == 0):?><span class="layui-badge layui-bg-cyan type_tips">普通类型</span><?php else:?><span class="layui-badge type_tips">TL 对应</span><?php endif;?>
            <button style="margin-left: 20px;margin-bottom: 2px;" type="button" class="layui-btn layui-btn-radius layui-btn-xs" id="switch-Type">修改接口类型</button>
          </div>
        </div>

        <?php if($info['type'] == 0):?>
        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">关键词库</label>
          <div class="layui-input-block">
            <div id="kw_group"></div>
          </div>
        </div>
        <?php else:?>
        <div class="layui-form-item">
          <label class="layui-form-label title">关键词表</label>
          <div class="layui-input-inline">
            <select name="keyword" lay-verify="required">
              <option value=""></option>
            </select>
          </div>
        </div>
        <?php endif;?>

        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">模板 (*)</label>
          <div class="layui-input-block">
            <div id="project_template"></div>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">标题样式</label>
          <div class="layui-input-block">
            <div id="title_style"></div>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">关键词样式</label>
          <div class="layui-input-block">
            <div id="keyword_style"></div>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">描述样式</label>
          <div class="layui-input-block">
            <div id="description_style"></div>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">TDK编码方式</label>
          <div class="layui-input-inline">
            <select name="tdk_encode">
              <option  value="">请选择</option>
              <option  value="ascii">ASCII</option>
              <option  value="unicode">UNICODE</option>
              <option  value="none">None</option>
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title">TDK编码范围</label>
          <div class="layui-input-block">
            <input type="checkbox" id="tdk_encode_style" name="tdk_encode_style[]" title="Title" value="title">
            <input type="checkbox" id="tdk_encode_style" name="tdk_encode_style[]" title="Keyword" value="keyword">
            <input type="checkbox" id="tdk_encode_style" name="tdk_encode_style[]" title="Description" value="description">
          </div>
          <div class="layui-form-mid layui-word-aux" id="tdk_encode_tips">每个搜索引擎识别的方式不同，根据搜索引擎规则选择</div>
        </div>


        <div class="layui-form-item">
          <label class="layui-form-label title">文章库源</label>
          <div class="layui-input-inline">
            <select name="article_source" id="project_article" lay-filter="required" lay-reqtext="请选择文章表~">
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title">标题库源</label>
          <div class="layui-input-inline">
            <select name="title_source" id="project_title" lay-filter="required" lay-reqtext="请选择标题表~">
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title">图片源</label>
          <div class="layui-input-inline">
            <select name="pic_source" id="project_pic" lay-filter="required" lay-reqtext="请选择图片源~">
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">页面语言(*)</label>
          <div class="layui-input-block">
            <input type="text" name="page_language"  placeholder="页面语言" lay-verify="required" lay-reqtext="页面语言不能为空~" value="<?php echo($info['seo_option']['page_language']);?>" autocomplete="off" class="layui-input" style="width: 80px;">
            <!-- https://www.w3schools.com/tags/ref_language_codes.asp 这里有全部的ISO代码 符合这里即可 -->
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label" style="width:150px;">蜘蛛直引规则 (*)</label>
          <div class="layui-input-inline layui-input-wrap">
            <select name="spider_rule_set" lay-filter="spider_rule_set" lay-verify="required" >
            </select>
            <div class="layui-form-mid layui-text-em" style="margin-left: 10px;">蜘蛛直引规则对于全局劫持蜘蛛时输出的链接，在 <a href="{:U('platform/project/url_rules')}">URL规则</a> 中设置</div>
          </div>
        </div>


        <?php if($info['type'] == 1):?>
        <div class="layui-form-item">
          <label class="layui-form-label title" pane>内链强验证</label>
          <div class="layui-input-block">
            <input type="checkbox" name="tl_url_must_verify" lay-skin="switch" lay-filter="must_verify" lay-text="开启|关闭" <?php if($info['seo_option']['tl_url_must_verify'] == 'on'):?> checked<?php endif;?>>
          </div>
          <div class="layui-form-mid layui-word-aux">开启URL强验证后，不匹配URL格式将输出 HTTP 404的响应状态(包括跳转)</div>
        </div>
        <?php endif;?>

        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">内链设置 (*)</label>
          <div class="layui-input-block">
            <textarea name="ilink_rules" lay-filter="required" id="v_url_rules" lay-verify="required" lay-reqtext="内链规则不能为空~" placeholder="一行代表一个规则 标签参照下方提示" class="layui-textarea" style="height: 200px;"><?php echo(unserialize(base64_decode($info['ilink_rules'])));?></textarea>
          </div>
          <div class="layui-form-mid layui-word-aux" id="link-quick" style="margin-left: 11.5%;"><button type="button" class="layui-btn layui-btn-primary layui-btn-xs" lay-verify="include-rules" id="include-spider-rules">引用直引规则至内链</button></div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">标签提示</label>
          <div class="layui-input-block">
            <?php if($info['type'] == 0) :?>
            <blockquote class="layui-elem-quote layui-quote-nm">
              字符标签：{数字}、{字母}、{大写字母}、{大小写字母}、{大写字母数字}、{大小写字母数字}、{数字字母}、{随机字符}<br/>
              时间标签：{日期}、{年}、{月}、{日}、{时}、{分}、{秒}<br/>
              动态标签：数字和字母标签后面加数字是位数，如： {数字8}表示8个数字、{数字1-8}示随机1-8个数字
            </blockquote>
            <?php else:?>
            <blockquote class="layui-elem-quote layui-quote-nm">
<!--              字符标签：{数字}、{字母}、{大写字母}、{大小写字母}、{大写字母数字}、{大小写字母数字}、{数字字母}、{随机字符}<br/>-->
<!--              时间标签：{日期}、{年}、{月}、{日}、{时}、{分}、{秒}<br/>-->
<!--              动态标签：数字和字母标签后面加数字是位数，如： {数字8}表示8个数字、{数字1-8}示随机1-8个数字<br/>-->
              链接标签：{tl} 由当前词库的link随机生成
            </blockquote>
            <?php endif;?>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">广告JS</label>
          <div class="layui-input-block">
            <input type="text" name="ad_js"  placeholder="广告JS" lay-verify="required" lay-reqtext="广告JS不能为空~" value="<?php echo($info['advert_option']['ad_js']);?>" autocomplete="off" class="layui-input" >
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">HTTPS 信任列表</label>
          <div class="layui-input-block">
            <textarea name="trustedHttpsDomains" id="trustedHttpsDomains"  placeholder="一行一个域名，该域名不再使用内容端接受到的协议头指定，而直接使用HTTPS协议，可为空" class="layui-textarea" style="height: 200px;"><?php echo implode("\n", $info['seo_option']['trustedHttpsDomains']); ?></textarea>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title"  style="">HTTPS 信任说明</label>
          <div class="layui-input-block">
            <blockquote class="layui-elem-quote layui-quote-nm">
              由于存在多级 CDN/Nginx 代理，容器可能无法获取原始请求的协议头（如未传递 X-Forwarded-Proto 等代理头）
              您可以在此手动指定正确的协议，避免模板标签引用错误的协议前缀。
            </blockquote>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title">AMP 配置开关</label>
          <div class="layui-input-block">
            <input type="checkbox" name="amp_switch" lay-skin="switch" lay-filter="amp_option" lay-text="开启|关闭" <?php if($info['seo_option']['amp_switch'] == 'on'):?> checked<?php endif;?>>
          </div>
          <div class="layui-form-mid layui-word-aux">开启后，将显著提升网站在 Google 移动端搜索结果中的排名表现</div>
        </div>

        <div class="layui-form-item" id="amp-domain" <?php if($info['seo_option']['amp_switch'] != 'on'):?>style="display:none"<?php endif;?>>
        <label class="layui-form-label title">AMP 访问域名</label>
        <div class="layui-input-block">
          <input type="text" name="amp_domain" placeholder="AMP 访问域名，需要携带协议 https://" value="<?php echo($info['seo_option']['amp_domain']);?>" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-mid layui-word-aux">Google 允许 AMP 页面部署在不同的域名上，但 AMP 页面的 canonical 链接必须与源站保持一致。</div>
        </div>


        <div class="layui-form-item">
          <label class="layui-form-label title">GSC 验证配置开关</label>
          <div class="layui-input-block">
            <input type="checkbox" name="gsc_switch" lay-skin="switch" lay-filter="gsc_switch" lay-text="开启|关闭" <?php if($info['seo_option']['gsc_switch'] == 'on'):?> checked<?php endif;?>>
          </div>
          <div class="layui-form-mid layui-word-aux">开启后，你将无需手动上传文件进行 Google Search Console 网站所有权的验证</div>
        </div>

        <div class="layui-form-item" id="google-verification-file" <?php if($info['seo_option']['gsc_switch'] != 'on'):?>style="display:none"<?php endif;?>>
        <label class="layui-form-label title">GSC 验证文件名</label>
        <div class="layui-input-block">
          <input type="text" name="gsc_filename" placeholder="填入需要验证的文件名" value="<?php echo($info['seo_option']['gsc_filename']);?>" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-mid layui-word-aux">通过 Google 网站所有权验证后，您将能使用 Google 站长平台（GSC）管理您的网站。请注意，每个 Google 账号对应的验证文件名都是唯一的。</div>
        </div>


        <div class="layui-form-item">
          <label class="layui-form-label title">Sitemap 生成条数</label>
          <div class="layui-input-inline" style="width: 200px;">
            <input type="number" name="sitemapItemLimit"
                   value="<?php echo($info['seo_option']['sitemapItemLimit']);?>"
                   required lay-verify="required|number"
                   placeholder="输入条数，不可大于9999999"
                   autocomplete="off"
                   class="layui-input"
                   style="text-align: center;"
            >
          </div>
          <div class="layui-input-inline" style="width: 50px; line-height: 38px;">
            <span>条</span>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title">页面压缩</label>
          <div class="layui-input-block">
            <input type="checkbox"  name="page_compress" lay-skin="switch" lay-text="ON|OFF"<?php if($info['seo_option']['page_compress'] == 'on'):?> checked<?php endif;?> />
          </div>
          <div class="layui-form-mid layui-word-aux">压缩过的页面比原体积小 搜索引擎更喜欢</div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label title">禁止快照</label>
          <div class="layui-input-block">
            <input type="checkbox" name="disable_snapshots" lay-skin="switch" lay-text="ON|OFF" <?php if($info['seo_option']['disable_snapshots'] == 'on'):?> checked<?php endif;?>/>
          </div>
          <div class="layui-form-mid layui-word-aux">快照禁止后在搜索引擎无法演示快照内容，但不影响排名</div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title" pane>缓存开关</label>
          <div class="layui-input-block">
            <input type="checkbox" name="cache_on" lay-skin="switch" lay-filter="cache_option" lay-text="开启|关闭" <?php if($info['cache_option']['cache_on'] == 'on'):?> checked<?php endif;?>>
          </div>
          <div id="cache_option_tips" class="layui-form-mid layui-word-aux">缓存关闭后页面将动态更新</div>
        </div>
<!--        <div class="layui-form-item">-->
<!--          <label class="layui-form-label title" pane>缓存方法</label>-->
<!--          <div class="layui-input-inline">-->
<!--            <select name="cache_type" id="cache_class" lay-filter="cache_class">-->
<!--              <option value="">请选择</option>-->
<!--              <optgroup label="局部缓存">-->
<!--                <option value="0">缓存TKD</option>-->
<!--                <option value="1">缓存TKDB</option>-->
<!--              </optgroup>-->
<!--              <optgroup label="全局缓存">-->
<!--                <option value="2">全部缓存</option>-->
<!--              </optgroup>-->
<!--            </select>-->
<!--          </div>-->
<!--          <div class="layui-form-mid layui-word-aux" id="cache_tips">请选择缓存方法</div>-->
<!--        </div>-->

        <div class="layui-form-item">
          <label class="layui-form-label title">缓存方法</label>
          <div class="layui-input-inline" style="width: 260px;">
            <select name="cache_type" id="cache_class" lay-filter="cache_class">
              <option value="">请选择缓存方式</option>
              <optgroup label="局部缓存">
                <option value="0">缓存 TKD</option>
                <option value="1">缓存 TKDB</option>
              </optgroup>
              <optgroup label="全局缓存">
                <option value="2">全部缓存</option>
              </optgroup>
            </select>
          </div>
          <div class="layui-form-mid layui-word-aux" id="cache_tips">
            「TKD」为标题描述关键词缓存，「TKDB」包含内容缓存，「全部缓存」用于整页输出缓存
          </div>
        </div>

<!--        <div class="layui-form-item">-->
<!--          <label class="layui-form-label title">缓存周期</label>-->
<!--          <div class="layui-input-block">-->
<!--            <input type="text" name="cache_expires" id="v_cache_expires" value="<?php echo($info['cache_option']['cache_expires']);?>" required  lay-verify="required|number" placeholder="请输入缓存周期，0为无限期" autocomplete="off" value="0" class="layui-input">-->
<!--          </div>-->
<!--          <div class="layui-form-mid layui-word-aux" id="v_cache_expires_tips">单位：天 如果大于缓存有效期则会强制删除缓存 0 为直接缓存</div>-->
<!--        </div>-->


        <div class="layui-form-item">
          <label class="layui-form-label title">缓存周期</label>
          <div class="layui-input-inline" style="width: 200px;">
            <input type="number" name="cache_expires" id="v_cache_expires"
                   value="<?php echo($info['cache_option']['cache_expires']);?>"
                   required lay-verify="required|number"
                   placeholder="输入天数，0 为永久"
                   autocomplete="off"
                   class="layui-input"
                   style="text-align: center;"
            >
          </div>
          <div class="layui-input-inline" style="width: 50px; line-height: 38px;">
            <span>天</span>
          </div>
          <div class="layui-form-mid layui-word-aux" id="v_cache_expires_tips">
            0 为永久缓存，填入大于 0 的整数以设定缓存周期
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label title">模板混淆</label>
          <div class="layui-input-block">
            <input type="checkbox" id="encode_style" name="encode_style[]" title="class混淆" value="class">
            <input type="checkbox" id="encode_style" name="encode_style[]" title="id混淆" value="id">
            <input type="checkbox" id="encode_style" name="encode_style[]" title="标签混淆" value="tag">
          </div>
          <div class="layui-form-mid layui-word-aux" id="encode_style_tips">模板混淆后，起到一定的防K作用</div>
        </div>

        <div class="layui-form-item">
          <div class="layui-input-block">
            <button class="layui-btn layui-btn-danger" lay-submit lay-filter="formDemo">保存</button>
            <a href="{:U('platform/project/port_index')}" class="layui-btn" target="_blank">返回</a>
          </div>
        </div>
      </fieldset>
    </form>

<!--    <div id="switch-type-dialog" style="display: none;">-->
<!--      <div class="layui-form" style="margin-left: 30%;">-->
<!--        <?php if($info['type'] == 0):?>-->
<!--            <input type="radio" name="switch-type-value" value="1" title="TL类型">-->
<!--            <input type="radio" name="switch-type-value" value="0" title="普通类型" checked>-->
<!--        <?php else:?>-->
<!--            <input type="radio" name="switch-type-value" value="1" title="TL类型" checked>-->
<!--            <input type="radio" name="switch-type-value" value="0" title="普通类型">-->
<!--        <?php endif;?>-->
<!--        <button type="button" class="layui-btn layui-btn-xs">确定切换</button>-->
<!--      </div>-->
<!--    </div>-->

    <div id="switch-type-dialog" style="display: none; margin-left: -80px;" >
      <form class="layui-form" lay-filter="switchForm" style="padding: 20px;" action="">
        <div class="layui-form-item">
          <div class="layui-input-block" style="display: flex; justify-content: center;">
            <input type="radio" name="switch-type" lay-filter="switch-type-value" value="1" title="TL类型" />
            <input type="radio" name="switch-type" lay-filter="switch-type-value" value="0" title="普通类型" />
          </div>
        </div>
        <div class="layui-form-item" style="margin-top: 20px;">
          <div class="layui-input-block" style="display: flex; justify-content: center;">
            <button type="button" id="submitSwitch" lay-submit lay-filter="submitSwitch" class="layui-btn layui-btn-normal" style="width: 150px;">确定切换</button>
          </div>
        </div>
        <div class="layui-form-item" style="margin-top: 10px;">
          <div class="layui-input-block" style="display: flex; justify-content: center;">
            <p class="layui-text" style="font-size: 14px; color: #999;">在切换接口类型后，您需要重新设置词库和内链</p>
          </div>
        </div>
      </form>
    </div>
  </div>
</block>

<block name="js">
  <script src="__PUBLIC__/statics/xm-select/xm-select.js"></script>
  <script>
    // 初始化结果变量
    var reslut = null;

    const template_selector = xmSelect.render({
      el: '#project_template',
      toolbar: {show: true},
      theme: {
        color: '#8799a3',
      },
      size: 'small',
      data: [],
      name: 'project_template'
    });

    const keyword_selector = xmSelect.render({
      el: '#kw_group',
      toolbar: {show: true},
      theme: {
        color: '#0081ff',
      },
      size: 'small',
      data: [],
      name: 'keyword'
    });

    // 判断项目类型并根据不同类型请求不同参数
    <?php if($info['type'] == 1): ?>
    const collection_url = '{:U("Platform/ProjectApi/mix?type=tl")}';
    const keyword_model = true;
    <?php else: ?>
    const collection_url = '{:U("Platform/ProjectApi/mix")}';
    const keyword_model = false;
    <?php endif; ?>

    var encodeType = [];

    // 获取已配置的项目数据
    function fetchConfiguredData() {
      return $.ajax({
        url: "{:U('platform/project/port_edit?kwa=1&cid=')}<?php echo(intval($_GET['cid']));?>",
        type: "GET",
        dataType: "json"
      });
    }

    // 获取源数据
    function fetchSourceData() {
      return $.ajax({
        url: collection_url,
        type: 'GET',
        dataType: 'json'
      });
    }

    // 切换接口类型

    const dialog = null;
    $("#switch-Type").on('click',function(){
      var dialogxxx = layer.open({
        type: 1, // page 层类型
        area: ['400px', '250px'],
        title: '切换接口类型',
        shade: 0.6, // 遮罩透明度
        shadeClose: true, // 点击遮罩区域，关闭弹层
        maxmin: false, // 允许全屏最小化
        anim: 0, // 0-6 的动画形式，-1 不开启
        content: $("#switch-type-dialog"),
        success:function (){
          layui.form.val('switchForm', {
            'switch-type': keyword_model ? '1' : '0', // 根据是否是 TL 类型来设置默认值
          });
        }
      });

      layui.form.on('submit(submitSwitch)',function (data) {
        console.log(data.field);
        $.post("{:U('platform/project/port_edit?cid=')}<?php echo(intval($_GET['cid']));?>&action=switch",data.field,function (result){
          if (result.code === 500){
            layer.close(dialogxxx);
            layer.msg(result.message, {icon: 0});
          }else{
            layer.close(dialogxxx);
            layer.msg(result.message, {icon: 1},function (){
                window.location.reload();
            });
          }
        })
      })
    })

    // 快随选项链接规则
    $("#link-quick").on('click',function(){
        const text_rules = $("textarea[name='ilink_rules']").val();
        const current_rules = $("select[name='spider_rule_set']").find('option:selected');
        if(text_rules !== '' || text_rules.length > 0){
              // 已经有规则 提示用户是否需要覆盖
          layer.confirm('当前已设置内链 是否将 <span class="red">' + current_rules.text() + ' </span> 覆盖到内链设置 ？' , {
            // layer.confirm("项目为 TL 时请确保规则数据源与当前数据表一致 " + ' <br/> 是否将 ' + rule_selector + '),{
              btn: ['覆盖', '不覆盖'] //按钮
            }, function() {
              getLinkContent(current_rules.val()) ?? layer.msg('应用成功',{icon: 1});

            }, function() {
              layer.msg('取消');
            });
        }else {
          getLinkContent(current_rules.val());
        }
    });


    function getLinkContent(ur_alias) {
      $.post("{:U('Platform/Project/url_rules?action=get_alias')}", {ur_alias: ur_alias}, function (data) {
        if (data.code === 200) {
          $("textarea[name='ilink_rules']").val(data.data);
          return true;
        }
      });
    }
    // 初始化数据
    // 初始化数据
    async function initializeData() {
      try {
        // 启动加载动画
        layer.load(5, { shade: [0.5, "#5588AA"] });

        // 获取并处理已配置的数据
        const configuredData = await fetchConfiguredData();
        reslut = configuredData;

        if (reslut.encode_style) {
          encodeType = reslut.encode_style.split(",");
          encodeType.forEach(value => {
            $("input[id='encode_style'][value='" + value + "']").prop("checked", true);
          });
        }

        $("select[name='tdk_encode']").val(reslut.tdk_encode);

        // 设置其他选项
        var tdk_encode_style_Type = reslut.tdk_encode_style ? reslut.tdk_encode_style.split(",") : [];
        tdk_encode_style_Type.forEach(value => {
          $("input[id='tdk_encode_style'][value='" + value + "']").prop("checked", true);
        });

        var keyword_style = xmSelect.render({
          el: '#keyword_style',
          toolbar: {show: true},
          autoRow: true,
          size: 'small',
          name: 'keyword_style',
          layVerify: 'required',
          layVerType: 'msg',
          initValue: reslut.keyword_style,
          tips: '可选择多个关键词样式 程序随机调用',
          data: [
            {name: '主关键词', value: 1},
            {name: '主关键词,关联关键词', value: 2},
            {name: '主关键词,关联关键词1,关联关键词2', value: 3},
            {name: '随机关键词', value: 4},
          ]
        });

        var description_style = xmSelect.render({
          el: '#description_style',
          toolbar: {show: true},
          autoRow: true,
          size: 'small',
          name: 'description_style',
          layVerify: 'required',
          layVerType: 'msg',
          tips: '可选择多个描述样式 程序随机调用',
          initValue: reslut.description_style,
          data: [
            {name: '主关键词+文章正文', value: 1},
            {name: '主关键词随机插入正文', value: 2},
            {name: '主关键词', value: 3},
            {name: '随机关键词堆砌', value: 4},
            {name: '来自描述文件', value: 5},
          ]
        });

        var title_style = xmSelect.render({
          el: '#title_style',
          toolbar: {show: true},
          layVerify: 'required',
          layVerType: 'msg',
          tips: '可选择多个标题样式 程序随机调用',
          autoRow: true,
          size: 'small',
          name: 'title_style',
          initValue: reslut.title_style,
          data: [
            {name: '主关键词', value: 1},
            {name: '主关键词 - 副标题', value: 2},
            {name: '主关键词 - 关联关键词', value: 3},
            {name: '主关键词 -关联关键词1-关联关键词2', value: 4},
            {name: '主关键词:文章标题', value: 5},
          ]
        });

        template_selector.update({ initValue: reslut.project_template });

        if(!keyword_model){
          keyword_selector.update({ initValue: reslut.keyword });
        }
        layui.form.render();

        // 获取并处理源数据
        const sourceData = await fetchSourceData();
        const contentData = JSON.parse(sourceData.data.content);
        const picData = JSON.parse(sourceData.data.pic);
        const templateData = sourceData.data.template;
        const keywordData = sourceData.data.keyword;
        const rulesData = sourceData.data.rules;

        // 渲染 article 选择框
        $.each(contentData.article, function(index, item) {
          $("select[name='article_source']").append('<option value="' + item.table + '">' + item.table + '</option>');
        });

        if (reslut.article_source) {
          $("select[name='article_source']").val(reslut.article_source);
        }

        // 渲染 title 选择框
        $.each(contentData.title, function(index, item) {
          $("select[name='title_source']").append('<option value="' + item.table + '">' + item.table + '</option>');
        });

        $.each(rulesData, function(index, item) {
          $("select[name='spider_rule_set']").append('<option value="' + item.value + '">' + item.name + '</option>');
        });

        if (reslut.spider_rule_set) {
          $("select[name='spider_rule_set']").val(reslut.spider_rule_set);
        }

        if (reslut.title_source) {
          $("select[name='title_source']").val(reslut.title_source);
        }

        $.each(picData, function(index, item) {
          $("select[name='pic_source']").append('<option value="' + item.value + '">' + item.name + '</option>');
        });

        if (reslut.pic_source) {
          // 确保 select 的值设置为对应的 value
          $("select[name='pic_source']").val(reslut.pic_source).trigger('change'); // 使用 trigger('change') 触发选择变化事件
        }

        template_selector.update({ data: templateData, autoRow: true });

        if (keyword_model) {
          $.each(keywordData, function(index, item) {
            $("select[name='keyword']").append('<option value="' + item.value + '">' + item.name + '</option>');
          });
          if (reslut.keyword) {
            $("select[name='keyword']").val(reslut.keyword);
          }
        } else {
          keyword_selector.update({ data: keywordData, autoRow: true });
        }




        layui.form.render('select');
      } catch (error) {
        console.error("Error initializing data:", error);
      } finally {
        // 关闭加载动画
        layer.closeAll("loading");
      }
    }

    // 调用初始化函数
    initializeData();

    layui.use(['form'], function(){
      var form = layui.form;

      form.on('switch(cache_option)', function(data){
        if (data.elem.checked) {
          $('#cache_option_tips').text('在下面选项中设置缓存周期及缓存方法');
          $("input[id='v_cache_expires']").prop('disabled', false);
          $("select[id='cache_class']").prop('disabled', false);
          form.render(); // 重新渲染 layui 表单
        } else {
          $('#cache_option_tips').text('缓存已关闭，页面将动态刷新');
          $('#v_cache_expires_tips').text('缓存已关闭，该选项已不可用');
          $('#cache_tips').text('缓存已关闭，该选项已不可用');
          $("input[id='v_cache_expires']").prop('disabled', true);
          $("select[id='cache_class']").prop('disabled', true);
          form.render(); // 重新渲染 layui 表单
        }
      });

      form.on('switch(gsc_switch)', function(data){
        if(data.elem.checked){
          document.getElementById('google-verification-file').style.display = '';
        } else {
          document.getElementById('google-verification-file').style.display = 'none';
        }
      });

      form.on('switch(amp_option)', function(data){
        if(data.elem.checked){
          document.getElementById('amp-domain').style.display = '';
        } else {
          document.getElementById('amp-domain').style.display = 'none';
        }
      });



    });

    $("select[name='cache_type']").find("option[value='<?php echo($info['cache_option']['cache_type']); ?>']").prop("selected",true);

    $("select[name='tdk_encode']").find("option[value='<?php echo($info['seo_option']['tdk_encode']); ?>']").prop("selected",true);


    layui.form.on('select(cache_class)', function(data){
      // console.log(data);cache_on
      // var cache_on = $("#cache_on").prop("checked")
      switch (data.value) {
        case "0":
          $("#cache_tips").text('仅缓存标题、关键词、描述。')
          break;
        case "1":
          $("#cache_tips").text('仅缓存标题、关键词、描述、内容')
          break;
        case "2":
          $("#cache_tips").text('缓存页面的所有内容')
          break;
        default:
          $("#cache_tips").text('请选择缓存方法')
          break;
      }
    });

    $.get("{:U('platform/project/port_edit?info=base&&cid=')}<?php echo(intval($_GET['cid']));?>",function (data) {
      $("#lite-cache").text(formatNumber(data.cache_total))
      $("#index-total").text(formatNumber(data.index_total))
      $("#show-lite-total").text(formatNumber(data.hosting_total))
      $("#sitemap-total").text(formatNumber(data.sitemap))
      $("#storage-total").text(data.storage)
    })

    // $.get("{:U('platform/project/port_edit?info=get_total&cid=')}<?php echo(intval($_GET['cid']));?>",function (data) {
    //   $("#show-lite-total").text(formatNumber(data.total))
    // })

    $("#show-lite-total").on('click',function(){
      $.get("{:U('platform/project/port_edit?info=cat&&cid=')}<?php echo(intval($_GET['cid']));?>",function(data){
        if(data.code !== 200){
          layer.msg(data.message)
        }else {
          layer.open({
            type: 1 //Page层类型
            ,
            area: ['650px', '650px']
            ,
            title: '查看托管列表(仅显示500条数据)'
            ,
            shade: 0.6 //遮罩透明度
            ,
            maxmin: false //允许全屏最小化
            ,
            anim: 2 //0-6的动画形式，-1不开启
            ,
            content: '<div class="layui-row table-container">\n' +
                    '    <div class="layui-col-md12">\n' +
                    '        <!-- 表头容器 -->\n' +
                    '        <div class="table-header" style="display: flex !important; justify-content: space-between !important; align-items: center !important; padding: 15px 10px; background-color: #f2f2f2; border-bottom: 2px solid #e6e6e6;">\n' +
                    '            <h3 style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">:)</h3>\n' +
                    '            <button id="export-btn" class="layui-btn layui-btn-normal layui-btn-sm" style="display: flex; align-items: center;">\n' +
                    '                <i class="layui-icon layui-icon-export" style="font-size: 18px; margin-right: 5px;"></i>\n' +
                    '                导出域名\n' +
                    '            </button>\n' +
                    '        </div>\n' +
                    '        <!-- 表格 -->\n' +
                    '        <table class="layui-table" style="margin-top: 0;">\n' +
                    '            <thead>\n' +
                    '                <tr>\n' +
                    '                    <th>域名</th>\n' +
                    '                    <th>日期</th>\n' +
                    '                </tr>\n' +
                    '            </thead>\n' +
                    '            <tbody id="top10-table">\n' +
                    '                <!-- 动态数据填充 -->\n' +
                    '                \n' +
                    '            </tbody>\n' +
                    '        </table>\n' +
                    '    </div>\n' +
                    '</div>'
          });
        }
        function renderTable(data) {
          var tbody = document.getElementById('top10-table');
          tbody.innerHTML = '';

          data.data.forEach(function(item, index) {
            var date = new Date(item.time * 1000); // UNIX 时间戳以秒为单位，需要乘以 1000
            var formattedTime = date.toLocaleString(); // 转换为本地日期时间字符串
            var tr = document.createElement('tr');
            tr.innerHTML = `<td>${item.url}</td><td>${formattedTime}</td>`;
            tbody.appendChild(tr);
          });
        }

        renderTable(data);


        document.getElementById('export-btn').addEventListener('click', function () {
          // 获取表格中的域名列
          const rows = document.querySelectorAll('#top10-table tr');
          const domains = Array.from(rows).map(row => {
            const cells = row.querySelectorAll('td');
            return cells[0]?.textContent.trim(); // 提取第1列数据（域名）
          }).filter(domain => domain); // 过滤掉空值

          // 创建 Blob 对象
          const blob = new Blob([domains.join('\n')], { type: 'text/plain;charset=utf-8' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = "<?php echo($info['bind_domain']) ?>" + '~导出域名.txt'; // 文件名
          link.click();
          URL.revokeObjectURL(link.href); // 释放 URL
        });
      })
    });

    function formatNumber(num) {
      // 如果参数为 null 或 undefined，直接返回空字符串或其他默认值
      if (num == null) {
        return '';
      }
      
      num = num.toString();
      let isNegative = false;
      
      // 处理负数
      if (num[0] === '-') {
        isNegative = true;
        num = num.substring(1);
      }
    
      let decimalPart = '';
      if (num.indexOf('.') !== -1) {
        const parts = num.split('.');
        decimalPart = '.' + parts[1];
        num = parts[0];
      }
      
      // 插入逗号分隔符
      let array = num.split('');
      let index = -3;
      while (array.length + index > 0) {
        array.splice(index, 0, ',');
        index -= 4;
      }
      
      let result = array.join('') + decimalPart;
      if (isNegative) {
        result = '-' + result;
      }
      return result;
    };
    // 提交修改
    layui.form.on('submit(formDemo)',function (result) {
      var _data = result.field;
      layer.load(5, { shade: [0.5, "#5588AA"] });
      if(keyword_model){
        console.log(_data);
      }
      $.ajax({
        url:"{:U('platform/project/port_edit?cid=')}<?php echo(intval($_GET['cid']));?>",
        type:'post',
        data:_data,
        success:function(result){
          if(result.code == 200) {
            layer.closeAll("loading");
            layer.msg(result.msg, {icon: 1, time: 3000},function(){
              window.location.reload();
            })
          }

          if(result.code == 404) {
            layer.closeAll("loading");
            layer.msg(result.msg);
          }
        },error: function (xhr, ajaxOptions, thrownError) {
          console.log(xhr);
          layer.closeAll("loading");
        }
      });
      return false;
    })
  </script>
</block>