<extend name="Public:base"/>
<block name="title">链接池管理- 目录反代管理</block>
<block name="content">
    <div class="page-header"><h1> 链接池管理 &gt; 目录反代管理</h1></div>
    <div class="col-xs-12">
        <div class="tabbable">
            <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
                <li class="active"><a href="#home" data-toggle="tab">链接池列表</a></li>
                <li><a href="javascript:;" onclick="add()">新增外链</a></li>
            </ul>
            <div class="tabbable">
                <div class="tab-content">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>链接ID</th>
                            <th>链接域名</th>
                            <th>链接规则</th>
                            <th>链接等级</th>
                            <th>添加时间</th>
                            <th>操作</th>
                        </tr>
                        <?php if(!empty($data)) : ?>
                        <?php foreach($data as $key=>$value) : ?>
                        <?php $rule = unserialize($data[$key]['srules']);?>
                        <?php $count_rules = count($rule);?>
                        <tr>
                            <td><?php echo($data[$key]['sid']);?></td>
                            <td><a href="javascript:;" name="domain"><?php echo($data[$key]['sdomain']);?></a></td>
                            <td>
                                <details>
                                    <summary>该域名有<b><?php echo($count_rules);?></b>个链接规则</summary>
                                    <ul>
                                        <?php foreach($rule as $keys => $values) : ?>
                                        <li><?php echo($values);?></li>
                                        <?php endforeach;?>
                                    </ul>
                                </details>
                            </td>
                            <td><code>
                                <?php if($data[$key]['slevel'] == 0):?>
                                <?php echo('低');?>
                                <?php elseif($data[$key]['slevel'] == 1): ?>
                                <?php echo('中');?>
                                <?php else :?>
                                <?php echo('高');?>
                            <?php endif;?>
                            </code></td>
                            <td><?php echo(word_time($data[$key]['addtime']));?></td>
                            <td><a class="btn-xs btn-primary" data-link-sid="<?php echo($data[$key]['sid']);?>" name="ViewLinks" href="javascript:void(0);">View</a>&nbsp;<a class="btn-xs btn-danger" name="DeleteSite" data-link-sid="<?php echo($data[$key]['sid']);?>" href="javascript:void(0);">Delete</a></td>
                        </tr>
                        <?php endforeach;?>
                        <?php else:?>
                        <td colspan="6" class="center"><b class="red">暂时还没有站点~</b></td>
                        <?php endif;?>
                    </table>
                    <nav style="text-align: center">
                        {$page}
                    </nav>
                </div>
            </div>
        </div>

        <div class="modal fade" id="bjy-add" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                        <h4 class="modal-title" id="myModalLabel"> 添加链接</h4></div>
                    <div class="modal-body">
                        <form id="bjy-form" class="form-inline" action="{:U('Platform/Project/link?action=add')}" method="post">
                            <table class="table table-striped table-bordered table-hover table-condensed">
                                <tr>
                                    <th width="15%">链接地址：</th>
                                    <td><input class="input-medium" type="text" name="slink_domain" style="width: 100%;" placeholder="需带http协议"></td>
                                </tr>
                                <tr>
                                    <th width="15%">快速规则：</th>
                                    <td><select class="form-control" title="可选择一个快速规则！" name="quick_rules" id="quick_rules"></select></td>
                                </tr>
                                <tr>
                                    <th width="15%">链接规则：</th>
                                    <td>
                                        <textarea name="slink_rules" id="spider_source" class="form-control resizable processed" rows="10" style="width: 100%;" title="编辑链接规则" placeholder="链接规则,一行代表一个规则..支持标签
{string} 随机字符串
{date} 年月日数字 201904221432
{number} 随机纯数字 09221
{url_ext} 随机文件后缀 .html .shtml .aspx .asp .php	"></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <th width="15%">链接等级：</th>
                                    <td>
                                        <select name="slink_level" title="选择链接等级..." style="width: 100%;">
                                            <option value="0">低</option>
                                            <option value="1">中</option>
                                            <option value="2">高</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th></th>
                                    <td><input class="btn btn-success" type="submit" value="添加"></td>
                                </tr>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- 查看链接 -->
        <div class="modal fade" id="View-Links" tabindex="-1" role="dialog" aria-labelledby="View-Links" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                        <h4 class="modal-title" id="ViewSites"> 查看链接</h4></div>
                    <div class="modal-body">
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tr>
                                <th width="15%">提示：</th>
                            <td><span class="label label-warning" style="width: 100%;">珍惜生命，谨慎编辑！！</span></td>
                            </tr>
                            <tr>
                                <th width="15%">链接ID：</th>
                                <td><input class="input-medium" type="text" id="view_sid" style="width: 100%;" placeholder="填写网址名字" disabled></td>
                            </tr>
                            <tr>
                                <th width="15%">网址：</th>
                                <td><input class="input-medium" type="text" id="view_links" style="width: 100%;" placeholder="填写网址名字" disabled></td>
                            </tr>
                            <tr>
                                <th width="15%">链接规则：</th>
                                <td>
                                     <textarea name="slink_rules" id="view_rules" class="form-control resizable processed" rows="10" style="width: 100%;" title="编辑链接规则" placeholder="链接规则,一行代表一个规则..支持标签
{string} 随机字符串
{date} 年月日数字 201904221432
{number} 随机纯数字 09221
{url_ext} 随机文件后缀 .html .shtml .aspx .asp .php	"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <th></th>
                                <td><input class="btn btn-success" id="edit_slink" type="button" value="编辑"></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

</block>
<block name="js">
    <script>

        $("#quick_rules").change(function(){
            $.ajax({
                url : '{:U("Platform/Project/url_rules?action=get")}',
                type : 'post',
                data : {ur_id:$(this).val()},
                success : function(da){
                    if(da.code == 200) {
                        $('#spider_source').empty();
                        $('#spider_source').val(da.data);
                    }else{
                        alert('该条ID规则可能已删除，无法获取！')
                    }
                },
            });
        });

        // 查看链接
        $("a[name='ViewLinks']").click(function(){
            sid_hide = $(this).attr('data-link-sid')
            $.post("{:U('Platform/Project/link?action=view')}", {sid: sid_hide}, function(data, textStatus, xhr) {
                // 请求成功
                if(data.code === 200) {
                    $("#view_links").val(data.data.sdomain);
                    if(data.data.srules == ""){
                        $("#view_rules").html("当前链接规则已失效！");
                    }else{
                        $("#view_rules").html(data.data.srules);
                    }

                    $("#view_sid").val(data.data.sid);
                }else if(data.code === 404){
                        alert('没有找到链接!!');
                        return false;
                }

                    // 选项
                    $("#View-Links").modal('show');

        });
            });

        // 删除链接
        $("a[name='DeleteSite']").click(function(){
            sid_hide = $(this).attr('data-link-sid');
            if(confirm("是否删除ID为" + sid_hide + "的外链？")){
                // console.log('删除');

                $.post("{:U('Platform/Project/link?action=delete')}", {sid: sid_hide}, function(data, textStatus, xhr) {
                        if(data.code === 200){
                            alert(data.msg);
                            document.location.reload();
                        }

                        if(data.code === 404){
                            alert(data.msg);
                            document.location.reload();
                        }
                });
            }

        });

        // 保存链接
        $("#edit_slink").click(function(){
            if(confirm("是否编辑当前链接属性，链接变动可能会导致失链情况，继续吗？")){
                // 获取链接规则
                rules = $("#view_rules").val();
                sid = $("#view_sid").val();
                // 组装数据
                Upload = {
                    'sid' : sid,
                    'rules' : rules
                };
                $.post("{:U('Platform/Project/link?action=edit')}",Upload,function(data){
                    if(data.code === 200){
                        alert(data.msg);
                        document.location.reload();
                    }

                    if(data.code === 302){
                        alert(data.msg);
                        return false;
                    }

                    if(data.code === 404){
                        alert(data.data);
                        return false;
                    }
                });
                //alert(view_rules + viwe_sid);
            }
        });


        // 添加菜单
        function add() {
            quick_rules = $("select[name='quick_rules']").val();
            $("select[name='quick_rules']").empty();
            $.ajax({
                url:'{:U("Platform/Project/url_rules?action=list")}',
                type:"post",
                cache: false,
                error:function(){
                    alert('加载出错，请检查URL链接规则配置！');
                    return false;
                },
                success:function(data) {
                    if (data.code == 500) {
                        alert('加载出错，请检查URL链接规则配置！');
                        return false;
                    }

                    if (data.code == 200) {
                        var modelList = data.data;
                        var option = "<option title='选择一个规则' value='-1' disabled>选择一个规则</option>";

                        if (modelList && modelList.length != 0) {
                            for (var i = 0; i < modelList.length; i++) {
                                option +="<option title=\"" + modelList[i].ur_name + "\" value=\""+modelList[i].ur_id+"\">"+ modelList[i].ur_name +  "</option>";
                            }
                            $("select[name='quick_rules']").append(option);
                        }
                    }
                }
            });
            $('#bjy-add').modal('show');
        }

        // 修改菜单
    </script>
</block>