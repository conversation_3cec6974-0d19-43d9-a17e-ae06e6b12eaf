<extend name="Public:base"/>
<block name="title">站点管理 - IIS模块</block>
<block name="content">
    <div class="page-header"><h1> 站点管理 &gt; IIS模块</h1></div>
    <div class="col-xs-12">
        <div class="tabbable">
            <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
                <li class="active"><a href="#home" data-toggle="tab">站点列表</a></li>
                <li><a href="javascript:;" onclick="add()">批量添加站点</a></li>
            </ul>
            <div class="tabbable">
                <div class="tab-content">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>站点ID</th>
                            <th>站点名称</th>
                            <th>站点URL</th>
                            <th>SITEID</th>
                            <th>备注</th>
                            <th>JS地址</th>
                            <th>模板名</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                        <?php if(!empty($data)) : ?>
                        <?php foreach($data as $key=>$value) : ?>
                        <tr>
                            <td><?php echo($data[$key]['id']);?></td>
                            <td><?php echo($data[$key]['name']);?></td>
                            <td><a href="javascript:;" name="domain"><?php echo($data[$key]['url']);?></a></td>
                            <td><code><?php echo($data[$key]['siteid']);?></code></td>
                            <td><?php echo($data[$key]['rankname']);?></td>
                            <td><?php echo($data[$key]['js_jump']);?></td>
                            <td><?php echo($data[$key]['tpl_name']);?></td>
                            <td><b class="red"><?php echo(word_time($data[$key]['addtime']));?></b></td>
                            <td><a class="btn-xs btn-primary" data-site-sid="<?php echo($data[$key]['id']);?>" name="ViewSite" href="javascript:void(0);">View</a>&nbsp;<a class="btn-xs btn-danger" name="DeleteSite" data-site-sid="<?php echo($data[$key]['id']);?>" href="javascript:void(0);">Delete</a></td>
                        </tr>
                        <?php endforeach;?>
                        <?php else:?>
                        <td colspan="9" class="center"><b class="red">暂时还没有站点~</b></td>
                        <?php endif;?>
                    </table>
                    <nav style="text-align: center">
                        {$page}
                    </nav>
                </div>
            </div>
        </div>
        <!-- 编辑站点 -->
        <div class="modal fade" id="View-Sites" tabindex="-1" role="dialog" aria-labelledby="ViewSites" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                        <h4 class="modal-title" id="ViewSites"> 查看站点</h4></div>
                    <div class="modal-body">
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tr>
                                <th width="15%">提示：</th>
                                <td><span class="label label-warning" style="width: 100%;">编辑后约60秒才能更新到控制端，请耐心等待！！</span></td>
                            </tr>
                            <tr>
                                <th width="15%">网站名称：</th>
                                <td><input class="input-medium" type="text" name="view_titles" style="width: 100%;" placeholder="填写网址名字" disabled></td>
                            </tr>
                            <tr>
                                <th width="15%">网站地址：</th>
                                <td><input class="input-medium" type="text" name="view_url" style="width: 100%;" placeholder="填写网站地址 需加根目录地址 /" disabled></td>
                            </tr>
                            <tr>
                                <th width="15%">JS地址：</th>
                                <td><input class="input-medium" type="text" name="view_js_jump" style="width: 100%;"  placeholder="排名出了之后跳哪里？"></td>
                            </tr>
                            <tr>
                                <th width="15%">服务端地址：</th>
                                <td><input class="input-medium" type="text" name="view_reserver" style="width: 100%;" placeholder="起个牛逼的名字吧~" disabled=""></td>
                            </tr>
                            <tr>
                                <th width="15%">模板名：</th>
                                <td><input class="input-medium" type="text" name="view_tpl_name" style="width: 100%;" value="default.html" placeholder="需先上传模板后才填写哦~"></td>
                            </tr>
                            <tr>
                                <th width="15%">目录前缀：</th>
                                <td><input class="input-medium" type="text" name="view_dir_prefix" style="width: 100%;"  value="app,hot" placeholder="生成的目录前缀"></td>
                            </tr>
                            <tr>
                                <th width="15%">蜘蛛许可：</th>
                                <td>
                                    <textarea name="view_spider_rules" id="spider_source" class="form-control resizable processed" rows="5" style="width: 100%;" title="编辑链接规则" placeholder="允许访问的蜘蛛列表">
</textarea>
                                </td>
                            <tr>
                                <th width="15%">Web容器：</th>
                                <td>
                                    <div class="form-inline" id="edit">
                                        <label class="radio-inline">
                                            <input type="radio" name="webServerOptions" id="edit_0" value="0"> Nginx
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" name="webServerOptions" id="edit_1" value="1"> IIS
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" name="webServerOptions" id="edit_2" value="2"> Apache
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th width="15%">404错误页：</th>
                                <td><input class="input-medium" type="text" name="view_error_page" style="width: 100%;" placeholder="地区屏蔽404跳转地址"></td>
                            </tr>
                            <tr>
                                <th width="15%">地区屏蔽：</th>
                                <td><input class="input-medium" type="text" name="view_no_access" style="width: 100%;" placeholder="程序默认屏蔽菲律宾/柬埔寨/香港,仅支持省级地区 如：四川、北京、天津等……"></td>
                            </tr>

                            <tr>
                                <th width="15%">备注：</th>
                                <td><input class="input-medium" type="text" name="view_remakes" style="width: 100%;" placeholder="你对它做了什么？"></td>
                            </tr>
                            <tr>
                                <th width="15%">百度Keys：</th>
                                <td><input class="input-medium" type="text" name="view_baidu_token" style="width: 100%;" placeholder="百度token，需认证百度站长获取"></td>
                            </tr>
                            <tr>
                                <th></th>
                                <td><input class="btn btn-success" id="edit_sites" type="button" value="保存"></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加站点 -->
        <div class="modal fade" id="Add-Sites" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                        <h4 class="modal-title" id="myModalLabel"> 添加站点</h4></div>
                    <div class="modal-body">
                        <form id="bjy-form" class="form-inline" action="{:U('Platform/Proxy/site?action=add')}" method="post">
                            <table class="table table-striped table-bordered table-hover table-condensed">
                                <tr>
                                    <th width="15%">网站名称：</th>
                                    <td><input class="input-medium" type="text" name="title" style="width: 100%;" placeholder="填写网址名字"></td>
                                </tr>
                                <tr>
                                    <th width="15%">网站地址：</th>
                                    <td><input class="input-medium" type="text" name="url" style="width: 100%;" placeholder="填写网站地址 需加根目录地址 /"></td>
                                </tr>
                                <tr>
                                    <th width="15%">备注：</th>
                                    <td><input class="input-medium" type="text" name="rankname" style="width: 100%;" placeholder="起个牛逼的名字吧~"></td>
                                </tr>
                                <tr>
                                    <th width="15%">JS地址：</th>
                                    <td><input class="input-medium" type="text" name="js_jump" style="width: 100%;"  placeholder="排名出了之后跳哪里？"></td>
                                </tr>
                                <tr>
                                    <th width="15%">模板名：</th>
                                    <td><input class="input-medium" type="text" name="tpl_name" style="width: 100%;" value="default.html" placeholder="需先上传模板后才填写哦~"></td>
                                </tr>
                                <tr>
                                    <th width="15%">目录前缀：</th>
                                    <td><input class="input-medium" type="text" name="dir_prefix" style="width: 100%;"  value="app,hot" placeholder="生成的目录前缀"></td>
                                </tr>
                                <tr>
                                    <th width="15%">Web容器：</th>

                                    <td>
                                        <div class="form-inline" id="add">
                                            <label class="radio-inline">
                                                <input type="radio" name="webServerOptions" id="inlineRadio1" value="0" checked> Nginx
                                            </label>
                                            <label class="radio-inline">
                                                <input type="radio" name="webServerOptions" id="inlineRadio2" value="1"> IIS
                                            </label>
                                            <label class="radio-inline">
                                                <input type="radio" name="webServerOptions" id="inlineRadio3" value="2"> Apache
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th width="15%">蜘蛛许可：</th>
                                    <td>
                                    <textarea name="spider_rules" id="spider_source" class="form-control resizable processed" rows="5" style="width: 100%;" title="编辑链接规则" placeholder="允许访问的蜘蛛列表
SogouSpider
BaiduSpider
360Spider
YiSouSpider	">
SogouSpider
BaiduSpider
360Spider
YiSouSpider</textarea>
                                    </td>
                                <tr>
                                    <th width="15%">404错误页：</th>
                                    <td><input class="input-medium" type="text" name="error_page" style="width: 100%;" placeholder="地区屏蔽404跳转地址"></td>
                                </tr>
                                <tr>
                                    <th width="15%">地区屏蔽：</th>
                                    <td><input class="input-medium" type="text" name="no_access" style="width: 100%;" placeholder="程序默认屏蔽菲律宾/柬埔寨/香港,仅支持省级地区 如：四川、北京、天津等……"></td>
                                </tr>
                                <!--<tr>
                                    <th width="15%">百度Keys：</th>
                                    <td><input class="input-medium" type="text" name="baidu_token" style="width: 100%;" placeholder="百度token，需认证百度站长获取"></td>
                                </tr>-->
                                <input type="hidden" name="sid_hide"/>
                                <tr>
                                    <th></th>
                                    <td><input class="btn btn-success" type="submit" value="添加"></td>
                                </tr>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
</block>
<block name="js">
    <script>
        // 编辑网站
        $("a[name='ViewSite']").click(function(){
            // 获取当前点击的列表ID
            site_sid = $(this).attr('data-site-sid');
            view_title = $("input[name='view_titles']");
            view_url = $("input[name='view_url']");
            view_js_jump = $("input[name='view_js_jump']");
            view_tpl_name = $("input[name='view_tpl_name']");
            view_reserver = $("input[name='view_reserver']");
            view_dir_prefix = $("input[name='view_dir_prefix']");
            view_error_page = $("input[name='view_error_page']");
            view_no_access = $("input[name='view_no_access']");
            view_remakes = $("input[name='view_remakes']");
            view_baidu_token = $("input[name='view_baidu_token']");
            sid_hide = $("input[name='sid_hide']").val(site_sid);
            /*
            view_url = $("input[name='view_url']");
            view_url = $("input[name='view_url']");
            */
            $.post("{:U('Platform/Proxy/site?action=view')}", {sid: site_sid}, function(data, textStatus, xhr) {
                // 请求成功
                if(data.code === 200){
                    view_js_jump.val(data.data.js_jump);
                    view_url.val(data.data.url);
                    view_title.val(data.data.name);
                    view_tpl_name.val(data.data.tpl_name);
                    view_dir_prefix.val(data.data.dir_prefix);
                    view_remakes.val(data.data.rankname);
                    view_error_page.val(data.data.error_page);
                    view_no_access.val(data.data.no_access);
                    view_baidu_token.val(data.data.baidu_token);
                    view_reserver.val("http://www.reproxy.top/caipiao/" + data.data.siteid);
                    view_web_server_types = data.data.webserver_classe;
                    // Nginx
                    if(view_web_server_types == "0"){
                        //edit
                        $("#edit_0").attr("checked", true);
                    }

                    if(view_web_server_types == "1"){
                        $("#edit_1").attr("checked", true);

                    }

                    if(view_web_server_types == "2"){
                        $("#edit_2").attr("checked", true);

                    }

                    // 选项
                    $("#View-Sites").modal('show');

                    // 后端没有数据
                }

            });



        });


        // 编辑事件

        $("#edit_sites").click(function() {
            if(confirm("是否修改网站配置？")){
                chencke_types = "";
                if($("#edit_0").prop("checked")){
                    chencke_types = 0;
                }
                if($("#edit_1").prop("checked")){
                    chencke_types = 1;
                }
                if($("#edit_2").prop("checked")){
                    chencke_types = 2;
                }
                update_data = {
                    sid : $("input[name='sid_hide']").val(),
                    js_jump : view_js_jump.val(),
                    rankname : view_remakes.val(),
                    dir_prefix : view_dir_prefix.val(),
                    tpl_name : view_tpl_name.val(),
                    no_access : view_no_access.val(),
                    error_page : view_error_page.val(),
                    baidu_token : view_baidu_token.val(),
                    webserver_classe : chencke_types
                }

                for(var i in update_data) {

                    if(update_data[i] === ""){
                        alert("请填写完整信息后在修改!");
                        return false;
                    }

                }

                //console.log(update_data);

                $.post("{:U('Platform/Proxy/site?action=edit')}", update_data, function(data, textStatus, xhr) {
                    if(data.code === 200){
                        alert(data.msg);
                        window.location.reload();
                    }

                    if(data.code === 302){
                        alert(data.msg);
                        return false;
                    }
                });


            }
        });

        // 删除网站监听事件

        $("a[name='DeleteSite']").click(function(event) {
            xsid = $(this).attr('data-site-sid');
            if(confirm("你是否真的要删除ID为【" +  xsid + "】的网站配置文件\n如果删除将不能恢复，你确定继续吗？")){
                if(confirm("在后台删除后同步控制端需1分钟的时间，你确定继续吗？")){
                    $.post("{:U('Platform/Proxy/site?action=delete')}", {sid: xsid}, function(data, textStatus, xhr) {
                        if(data.code === 200){
                            alert(":( 删除成功，请等待控制端删除...!");
                            window.location.reload();
                        }else{
                            alert(":) 删除失败，可能是人品问题~");
                            window.location.reload();
                        }
                    });
                }
            }
        });





        // 添加网站
        $("#bjy-form").submit(function(event) {
            event.preventDefault();
            postData = {
                title : $("input[name='title']").val(),
                url : $("input[name='url']").val(),
                rankname : $("input[name='rankname']").val(),
                js_jump : $("input[name='js_jump']").val(),
                tpl_name : $("input[name='tpl_name']").val(),
                dir_prefix : $("input[name='dir_prefix']").val(),
                no_access  : $("input[name='no_access']").val(),
                error_page  : $("input[name='error_page']").val(),
                //baidu_token : $("input[name='baidu_token']").val(),
                webserver_classe : $("#add > label >input[type='radio']:checked").val()
            };

            for(var i in postData) {
                if(postData[i] == ""){
                    alert("请填写完整信息后在提交!");
                    return false;
                }

            }

            $.post("{:U('Platform/Proxy/site?action=add')}", postData, function(data, textStatus, xhr) {
                if(data.code === 404){
                    alert(data.msg);
                    return false;
                }

                if(data.code === 200){
                    alert(data.msg);
                    window.location.reload();
                }
            });
        });

        // 添加菜单
        function add() {
            $('#Add-Sites').modal('show');
        }

        // 修改菜单
        function edit(obj) {
            var ruleId = $(obj).attr('ruleId');
            var ruletitle = $(obj).attr('ruletitle');
            $("input[name='id']").val(ruleId);
            $("input[name='title']").val(ruletitle);
            $('#bjy-edit').modal('show');
        }
    </script>
</block>