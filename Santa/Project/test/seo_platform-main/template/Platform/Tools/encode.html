<extend name="Public:base"/>
<block name="title">Unicode 在线编码转换</block>
<block name="content">
    <style type="text/css">
        .leftBtn{margin-left: 6.0%;}
    </style>
    <div class="page-header">
        <h1> <i class="fa fa-cog icon-test"></i>&nbsp;辅助工具 &gt; Unicode 在线编码转换</h1>
    </div>
    <div class="col-xs-12">
        <div class="form-inline">

        <div class="row clearfix tc">
            <textarea  id="source" name="content" style="width: 100%;" class="form-control" rows="15" placeholder="转换内容"></textarea>
        </div>
        </div>
            <div class="form-group form-inline" style="margin-top: 10px;" align="center">
            <button type="button" style="width: 30%;" class="leftBtn btn btn-primary btn-lg" onclick="action('CONVERT_FMT1')" value="uglify">转换(<u>E</u>ncoding)</button>
            <button type="button" style="width: 30%;" class="leftBtn btn btn-danger btn-lg" onclick="action('RECONVERT')" value="uglify">还原(<u>R</u>econvert)</button>
        </div>
        <div class="form-inline">
        <div class="row clearfix tc">
            <textarea id="show2" class="form-control" style="width: 100%;" name="result"  rows="15" cols="135" placeholder="转换结果"></textarea></div>
        </div>
    </div>


    </div>
    </div>
    <script language="javascript" type="text/javascript">
        var oSource = document.getElementById("source");
        var oShow2 = document.getElementById("show2");
        var oTt = document.getElementById("tt");
        function action(pChoice) {
            switch (pChoice) {
                case "CONVERT_FMT1":
                    oShow2.value = ascii(oSource.value);
                    break;
                case "CONVERT_FMT2":
                    oShow2.value = unicode(oSource.value);
                    break;
                case "RECONVERT":
                    oShow2.value = reconvert(oSource.value);
                    break;
            }
        }
        function ascii(str) {
            return str.replace(/[^\u0000-\u00FF]/g,
                function($0) {
                    return escape($0).replace(/(%u)(\w{4})/gi, "\&#x$2;")
                });
        }
        function unicode(str) {
            return str.replace(/[^\u0000-\u00FF]/g,
                function($0) {
                    return escape($0).replace(/(%u)(\w{4})/gi, "\\u$2")
                });
        }
        function reconvert(str) {
            str = str.replace(/(\\u)(\w{4})/gi,
                function($0) {
                    return (String.fromCharCode(parseInt((escape($0).replace(/(%5Cu)(\w{4})/g, "$2")), 16)));
                });
            str = str.replace(/(&#x)(\w{4});/gi,
                function($0) {
                    return String.fromCharCode(parseInt(escape($0).replace(/(%26%23x)(\w{4})(%3B)/g, "$2"), 16));
                });
            return str;
        }

        function cleartext(){
            document.getElementById('source').value='';
        }


    </script>
    </div>
</block>
<block name="js">
</block>