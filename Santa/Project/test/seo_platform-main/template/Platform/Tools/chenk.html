<extend name="Public:base"/>
<block name="title">Referer 模拟</block>
<block name="content">
    <div class="page-header">
        <h1> <i class="fa fa-cog icon-test"></i>&nbsp;辅助工具 &gt; Referer模拟</h1>
    </div>
    <div class="col-xs-12">
        <form class="form-inline">
            <div class="form-group" style="width: 5%;">
                 <select name="referer_type" id="referer_type" title="来源地址">
                     <option value="0">搜狗搜索</option>
                     <option value="1">百度搜索</option>
                     <option value="2">神马SM</option>
                     <option value="3">360搜索</option>
                </select>
            </div>
            <div class="form-group" style="width: 80%;">
                <input type="url" class="form-control" style="width:100%;height: 30px; text-align: center" name="chenkUrl" id="chenkUrl" placeholder="Referer 模拟请求地址...">
            </div>
            <div class="form-group" style="width: 5%;">
                <select name="ua_type" id="ua_type" title="User-Agent">
                    <option value="0">Desktop</option>
                    <option value="1">Mobile</option>
                    <option value="2">UC浏览器</option>
                </select>
            </div>
            <div class="form-group" style="margin-left:3px;width: 9%">
                <button type="button"  id="_404_chenk" style="height: 30px;line-height: 1px;" class="btn btn-default">模拟一下</button>
            </div>
        </form>
            <br/>
                <textarea id="source" class="form-control resizable processed" rows="30" readonly>等待模拟...</textarea>
    </div>
</block>
<block name="js">
    <script>

        $("form").submit(function(e){
            var input_text = $('#chenkUrl').val();
            if(input_text == ''){
                alert('输入URL后进行模拟!');
                return false;
            }else if(objExp.test(input_text) != true){
                alert('请检查是否输入正确的URL!!');
                return false;
            }
            });
            

    $("#_404_chenk").click(function(e){

            if($('#chenkUrl').val() == ''){
                alert('输入URL后进行模拟!');
                return false;
            }

        requeset_data = {
            "referer_type" : $("#referer_type").val(),
            "requeset_uri" : $("#chenkUrl").val(),
            "ua_type" : $("#ua_type").val()
        };
        function htmlspecialchars(str) {
            var s = "";
            if (str.length == 0) return "";
            for   (var i=0; i<str.length; i++)
            {
                switch (str.substr(i,1))
                {
                    case "<": s += "&lt;"; break;
                    case ">": s += "&gt;"; break;
                    case "&": s += "&amp;"; break;
                    case " ":
                        if(str.substr(i + 1, 1) == " "){
                            s += " &nbsp;";
                            i++;
                        } else s += " ";
                        break;
                    case "\"": s += "&quot;"; break;
                    default: s += str.substr(i,1); break;
                }
            }
            return s;
        }
        $("#source").html("正在努力模拟抓取中，稍等片刻...");
         $.ajax({
             url : "{:U('Platform/Tools/chenk')}",
             type : "POST",
             data : requeset_data,
             dataType : "html",
             success : function(data){
                 //console.log(data);
                 $("#source").html(htmlspecialchars(data));
             }

         })
    });

    </script>
</block>