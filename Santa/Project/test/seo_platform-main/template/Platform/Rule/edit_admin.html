<extend name="Public:base"/>
<block name="title">修改管理员</block>
<block name="css">
    <icheckcss/>
</block>
<block name="content">
    <div class="page-header"><h1><i class="fa fa-home"></i> 首页 &gt; 后台管理 &gt; 修改管理员</h1></div>
    <div class="col-xs-12">
        <div class="tabbable">
            <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
                <li><a href="{:U('Platform/Rule/admin_user_list')}">管理员列表</a></li>
                <li class="active"><a href="{:U('Platform/Rule/add_admin')}">修改管理员</a></li>
            </ul>
            <div class="tab-content">
                <form class="form-inline" method="post"><input type="hidden" name="id" value="{$user_data['id']}">
                    <table class="table table-striped table-bordered table-hover table-condensed">
                        <tr>
                            <th>管理组</th>
                            <td>
                                <foreach name="data" item="v"> {$v['title']} <input class="xb-icheck" type="checkbox"
                                                                                    name="group_ids[]"
                                                                                    value="{$v['id']}"
                                    <in name="v['id']" value="$group_data"> checked="checked"</in>
                                    > &emsp;
                                </foreach>
                            </td>
                        </tr>
                        <tr>
                            <th>姓名</th>
                            <td><input class="input-medium" type="text" name="username"
                                       value="{$user_data['username']}"></td>
                        </tr>
                        <tr>
                            <th>手机号</th>
                            <td><input class="input-medium" type="text" name="phone" value="{$user_data['phone']}"></td>
                        </tr>
                        <tr>
                            <th>邮箱</th>
                            <td><input class="input-medium" type="text" name="email" value="{$user_data['email']}"></td>
                        </tr>
                        <tr>
                            <th>修改密码</th>
                            <td><input class="input-medium" type="text" name="password">如不改密码；留空即可</td>
                        </tr>
                        <tr>
                            <th>OTP 密钥</th>
                            <td><input class="input-lg" type="text" name="txt" value="{$user_data['google_keys']}"></td>
                        </tr>
                        <tr>
                            <th>二维码</th>
                            <td><div id="qrcode"></div></td>
                        </tr>
                        <tr>
                            <th>状态</th>
                            <td><span class="inputword">允许登录</span> <input class="xb-icheck" type="radio"
                                                                               name="status" value="1"
                                <eq name="user_data['status']" value="1"> checked="checked"</eq>
                                > &emsp; <span class="inputword">禁止登录</span> <input class="xb-icheck" type="radio"
                                                                                        name="status" value="0"
                                <eq name="user_data['status']" value="0"> checked="checked"</eq>
                                >
                            </td>
                        </tr>
                        <tr>
                            <th></th>
                            <td><input class="btn btn-success" type="submit" value="修改"></td>
                        </tr>
                    </table>
                </form>
                <jquery />
                <script src="https://cdn.staticfile.org/jquery.qrcode/1.0/jquery.qrcode.min.js"></script>
                <script>
                    $('#qrcode').qrcode({width: 268,height: 232,text: "<?php echo($base64_images);?>"});
                </script>
            </div>
        </div>
    </div>
</block>
<block name="js">
    <icheckjs color="blue"/>
</block>