<extend name="Public:base"/>
<block name="title">站点管理 - 目录反代管理</block>
<block name="content">
    <div class="page-header"><h1> 站点管理 &gt; 目录反代管理</h1></div>
    <div class="col-xs-12">
        <div class="tabbable">
            <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
                <li class="active"><a href="#home" data-toggle="tab">站点列表</a></li>
                <li><a href="javascript:;" onclick="add()">添加站点</a></li>
            </ul>
            <div class="tab-content">
                <table class="table table-striped table-bordered table-hover table-condensed">
                    <tr>
                        <th>用户组名</th>
                        <th>操作</th>
                    </tr>
                    <foreach name="data" item="v">
                        <tr>
                            <td>{$v['title']}</td>
                            <td><a href="javascript:;" ruleId="{$v['id']}" ruleTitle="{$v['title']}"
                                   onclick="edit(this)">修改</a> | <a
                                    href="javascript:if(confirm('确定删除？'))location='{:U('Platform/Rule/delete_group',array('id'=>$v['id']))}'">删除</a>
                                | <a href="{:U('Platform/Rule/rule_group',array('id'=>$v['id']))}">分配权限</a> | <a
                                        href="{:U('Platform/Rule/check_user',array('group_id'=>$v['id']))}">添加成员</a>
                            </td>
                        </tr>
                    </foreach>
                </table>
            </div>
        </div>
    </div>
    <div class="modal fade" id="bjy-add" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="myModalLabel"> 添加用户组</h4></div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="{:U('Platform/Rule/add_group')}" method="post">
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tr>
                                <th width="15%">用户组名：</th>
                                <td><input class="input-medium" type="text" name="title"></td>
                            </tr>
                            <tr>
                                <th></th>
                                <td><input class="btn btn-success" type="submit" value="添加"></td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="bjy-edit" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="myModalLabel"> 修改规则</h4></div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="{:U('Platform/Rule/edit_group')}" method="post">
                        <input type="hidden" name="id">
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tr>
                                <th width="12%">规则名：</th>
                                <td><input class="input-medium" type="text" name="title"></td>
                            </tr>
                            <tr>
                                <th></th>
                                <td><input class="btn btn-success" type="submit" value="修改"></td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</block>
<block name="js">
    <script>
        // 添加菜单
        function add() {
            $("input[name='title']").val('');
            $('#bjy-add').modal('show');
        }

        // 修改菜单
        function edit(obj) {
            var ruleId = $(obj).attr('ruleId');
            var ruletitle = $(obj).attr('ruletitle');
            $("input[name='id']").val(ruleId);
            $("input[name='title']").val(ruletitle);
            $('#bjy-edit').modal('show');
        }
    </script>
</block>