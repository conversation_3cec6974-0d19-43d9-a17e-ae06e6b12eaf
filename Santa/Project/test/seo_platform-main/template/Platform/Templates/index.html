<extend name="Public:base"/>
<block name="title">模板管理</block>
<block name="content">
    <div class="page-header">
        <h1><i class="fa fa-server icon-test"></i> 通用设置 &gt; 模板管理</h1>
    </div>
    <div class="col-xs-12">
        <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
            <li class="active"> <a href="#home" data-toggle="tab">模板列表</a></li>
            <li><a href="javascript:;" id="add_api">新增接口</a></li>
        </ul>
        <div class="tabbable">
            <div class="tab-content">
                <table class="table table-bordered table-hover">
                    <tbody>
                    <tr>
                        <th>模板名称</th>
                        <th>添加时间</th>
                        <th>模板分组</th>
                        <th>推送模板</th>
                    </tr>
                    <?php if(!empty($data)) : ?>
                    <?php foreach($data as $key=>$value) : ?>
                    <tr>
                        <td><?php echo($data[$key]['ids']);?></td>
                        <td><?php echo($data[$key]['name']);?></td>
                        <td><a href="javascript:;"><?php echo($data[$key]['domain']);?></a></td>
                        <td><?php echo(word_time($data[$key]['create_times']));?></td>
                    </tr>
                    <?php endforeach;?>
                    <?php else:?>
                    <td colspan="4" class="center"><b class="red">暂时还未添加模板!</b></td>
                    <?php endif;?>
                    </tbody>
                </table>
                <nav style="text-align: center;">
                    {$page}
                </nav>
            </div>
        </div>
    </div>
    <div class="modal fade" id="add_window" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="myModalLabel">新增接口</h4>
                </div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="{:U('platform/server/api','action=add')}" method="post">
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tbody>
                            <tr>
                                <th width="12%">接口名：</th>
                                <td> <input class="input-medium" type="text" name="name" placeholder="接口名称，不可重复" required="1" /></td>
                            </tr>
                            <tr>
                                <th width="15%">访问域名：</th>
                                <td> <input class="input-medium" type="text" name="domain" placeholder="接口部署域名" required="2" /></td>
                                <input type="hidden" name="access_token" value="<?php echo(md5(time() . U('platform/server/api','action=add')));?>" />
                            </tr>
                            <tr>
                                <th></th>
                                <td> <input class="btn btn-success" type="submit" value="新增" /></td>
                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</block>
<block name="js">
    <script type="text/javascript">
        $(function(){
            $("#add_api").click(function(event) {
                $("#add_window").modal('show');
            });

            // 启用接口
            $("a[name='start-interface']").click(function(event) {
                var ids = $(this).attr('data-role-ids');
                $.post("{:U('platform/server/api','action=select_domain')}", {interfaces_id: ids}, function(data, textStatus, xhr) {
                    console.log(data);
                });
            });
            // 停用接口
            $("a[name='stop-interface']").click(function(event) {
                alert("停用接口!");
            });
        });
    </script>
</block>