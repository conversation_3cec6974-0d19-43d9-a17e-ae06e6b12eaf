<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title><block name="title"></block> - <PERSON> SEO工作平台</title>
<meta name="keywords" content="" />
<meta name="description" content="" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/layui/2.6.8/css/layui.min.css" />
<link href="__ADMIN_ACEADMIN__/css/bootstrap.min.css" rel="stylesheet" />
<link rel="stylesheet"
	href="__ADMIN_ACEADMIN__/css/font-awesome.min.css" />
	<link rel="stylesheet"
		href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css" />
<!--[if IE 7]>
    <link rel="stylesheet" href="__ADMIN_ACEADMIN__/css/font-awesome-ie7.min.css" />
    <![endif]-->

	<link rel="stylesheet" href="__ADMIN_ACEADMIN__/css/ace.min.css" />
	<link rel="stylesheet" href="__ADMIN_ACEADMIN__/css/ace-skins.min.css"/>
<!--[if lte IE 8]>
    <link rel="stylesheet" href="__ADMIN_ACEADMIN__/css/ace-ie.min.css" />
    <![endif]-->
<!--[if lt IE 9]>
    <script src="__ADMIN_ACEADMIN__/js/html5shiv.js"></script>
    <script src="__ADMIN_ACEADMIN__/js/respond.min.js"></script>
    <![endif]-->
	<script src="__ADMIN_ACEADMIN__/js/ace-extra.min.js"></script>
<link rel="stylesheet" href="__PUBLIC_CSS__/base.css" />
<style type="text/css">
/*#sidebar .nav-list {*/
/*	overflow-y: auto;*/
/*}*/

.b-nav-li {
	padding: 5px 0;
}
</style>
<block name="css"></block>
</head>

<body>
	<div class="navbar navbar-default" id="navbar">
		<script type="text/javascript">
        try{ace.settings.check('navbar' , 'fixed')}catch(e){}
    </script>
		<div class="navbar-container" id="navbar-container">
			<div class="navbar-header pull-left">
				<a href="{:U('platform/index/index')}" class="navbar-brand"> <small>
					<i class="fa fa-tachometer"></i> Jackie SEO工作平台 <small style="font-size: 12px;"> —— 你只管做，其他的交给我</small>
				</small>
				</a>
				<!-- /.brand -->
			</div>
			<div class="navbar-header pull-right" role="navigation">
				<ul class="nav ace-nav">
					<li class="light-blue"><a data-toggle="dropdown" href="#"
						class="dropdown-toggle"> <img class="nav-user-photo"
							src="__ADMIN_ACEADMIN__/avatars/user.jpg" alt="Jason's Photo" />
							<span class="user-info"> <small>欢迎大佬, </small>
								{$_SESSION['user']['username']}
						</span> <i class="icon-caret-down"></i>
					</a>

						<ul
							class="user-menu pull-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
							<li class="divider"></li>
							<li><a id="sys-clear-cache" href="javascript:;"> <i
									class="icon-bolt"></i> 清理缓存
							</a></li>

							<li><a href="{:U('Platform/index/logout')}"> <i
									class="icon-off"></i> 退出系统
							</a></li>
						</ul></li>
				</ul>
				<!-- /.ace-nav -->
			</div>
		</div>
	</div>

	<div class="main-container" id="main-container">
		<script type="text/javascript">
        try{ace.settings.check('main-container' , 'fixed')}catch(e){}
    </script>

		<div class="main-container-inner">
			<a class="menu-toggler" id="menu-toggler" href="#"> <span
				class="menu-text"></span>
			</a>

			<div class="sidebar" id="sidebar">
				<script type="text/javascript">
                try{ace.settings.check('sidebar' , 'fixed')}catch(e){}
            </script>
				<div class="sidebar-shortcuts" id="sidebar-shortcuts">
					<div class="sidebar-shortcuts-large" id="sidebar-shortcuts-large">
						<button class="btn btn-success">
							<i class="icon-signal"></i>
						</button>

						<button class="btn btn-info">
							<i class="icon-pencil"></i>
						</button>

						<button class="btn btn-warning">
							<i class="icon-group"></i>
						</button>

						<button class="btn btn-danger">
							<i class="icon-cogs"></i>
						</button>
					</div>

					<div class="sidebar-shortcuts-mini" id="sidebar-shortcuts-mini">
						<span class="btn btn-success"></span> <span class="btn btn-info"></span>

						<span class="btn btn-warning"></span> <span class="btn btn-danger"></span>
					</div>
				</div>
				<!-- #sidebar-shortcuts -->
				<ul class="nav nav-list">
					<foreach name="nav_data" item="v"> <empty
						name="v['_data']">
					<li class="b-nav-li"><a href="{:U($v['mca'])}"> <i
							class="fa fa-{$v['ico']} icon-test"></i> <span class="menu-text">
								{$v['name']} </span>
					</a></li>
					<else />
					<li class="b-has-child"><a href="#"
						class="dropdown-toggle b-nav-parent"> <i
							class="fa fa-{$v['ico']} icon-test"></i> <span class="menu-text">
								{$v['name']} </span> <b class="arrow icon-angle-down"></b>
					</a>
						<ul class="submenu">
							<foreach name="v['_data']" item="n">
							<li class="b-nav-li <if condition="
								(MODULE_NAME.'/'.CONTROLLER_NAME.'/'.ACTION_NAME) eq $n['mca']">active</if>">
								<a href="{:U($n['mca'])}"> <i
									class="icon-double-angle-right"></i> {$n['name']}
							</a>
							</li>
							</foreach>
						</ul></li>
					</empty> </foreach>
				</ul>
				<div class="sidebar-collapse" id="sidebar-collapse">
					<i class="icon-double-angle-left"
						data-icon1="icon-double-angle-left"
						data-icon2="icon-double-angle-right"></i>
				</div>

				<script type="text/javascript">
                try{ace.settings.check('sidebar' , 'collapsed')}catch(e){console.log(e)}
            </script>
			</div>
			<div class="main-content">
				<div class="page-content">
					<block name="content"></block>
				</div>
				<!-- /.page-content -->
			</div>
			<!-- /.main-content -->
		</div>
		<!-- /.main-container-inner -->

		<a href="#" id="btn-scroll-up"
			class="btn-scroll-up btn btn-sm btn-inverse"> <i
			class="icon-double-angle-up icon-only bigger-110"></i>
		</a>
	</div>

	<!--[if !IE]> -->
	<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/2.1.4/jquery.min.js"></script>
	<!-- <![endif]-->

	<!--[if IE]>
<script src="__PUBLIC__/statics/js/jquery-1.10.2.min.js"></script>
<![endif]-->

	<!--[if !IE]> -->
	<script type="text/javascript">
    window.jQuery || document.write("<script src='__ADMIN_ACEADMIN__/js/jquery-2.0.3.min.js'>"+"<"+"script>");
</script>
	<!-- <![endif]-->

	<!--[if IE]>
<script type="text/javascript">
    window.jQuery || document.write("<script src='__ADMIN_ACEADMIN__/js/jquery-1.10.2.min.js'>"+"<"+"script>");
</script>
<![endif]-->

	<script type="text/javascript">
    if("ontouchend" in document) document.write("<script src='__ADMIN_ACEADMIN__/js/jquery.mobile.custom.min.js'>"+"<"+"script>");
</script>
	<script src="__ADMIN_ACEADMIN__/js/typeahead-bs2.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/bootstrap.min.js"></script>
	<!-- page specific plugin scripts -->

	<!--[if lte IE 8]>
<script src="__ADMIN_ACEADMIN__/js/excanvas.min.js"></script>
<![endif]-->
	<script src="__PUBLIC__/statics/jquery-ui/jquery-ui.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/jquery.ui.touch-punch.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/jquery.slimscroll.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/jquery.easy-pie-chart.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/jquery.sparkline.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/flot/jquery.flot.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/flot/jquery.flot.pie.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/flot/jquery.flot.resize.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/ace-elements.min.js"></script>
	<script src="__ADMIN_ACEADMIN__/js/ace.min.js"></script>
	<script src="__PUBLIC_JS__/base.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/layui/2.6.8/layui.min.js"></script>
	<block name="js"></block>
	<script>
    $(function(){
        $('.b-has-child .active').parents('.b-has-child').eq(0).find('.b-nav-parent').click();
    })

	$("#sys-clear-cache").on("click",function(){
		$.get("{:U('platform/index/clear')}",function(res){
			if(res.code === 200){
				layui.use(function() {
					var layer = layui.layer;
					var util = layui.util;
					// 事件

					layer.alert(res.message, {icon: 1}, function(index){
						window.location.reload();
						layer.close(index);
					});
				})
			}
		})
	})
</script>
</body>
</html>

