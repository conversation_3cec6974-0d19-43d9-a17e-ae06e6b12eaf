<link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/layui/2.6.8/css/layui.min.css"/>
<div class="layui-container" style="margin-top: 20px;">
    <form class="layui-form" lay-filter="example-form" action="" method="post">
        <!-- 第一行：多列布局 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <label class="layui-form-label">站群分类</label>
                <div class="layui-input-block">
                    <select name="category_id">
                        <notempty name="WebsiteCategoryData">
                            <option value="">请选择站群分类</option>
                            <volist name="WebsiteCategoryData" id="category">
                                <option value="{$category.category_id}" <eq name="WebsiteData.category_id" value="$category.category_id">selected</eq>>
                                {$category.category_name}
                                </option>
                            </volist>
                            <else />
                            <option value="-1">新增分类后才可以选择</option>
                        </notempty>
                    </select>
                    <div class="layui-word-aux">请选择站群分类</div>
                </div>
            </div>
            <div class="layui-col-md4">
                <label class="layui-form-label">TDK 模板</label>
                <div class="layui-input-block">
                    <select name="profile_id">
                        <notempty name="WebsiteProfileData">
                            <option value="">请选择站群分类</option>
                            <volist name="WebsiteProfileData" id="profile">
                                <option value="{$profile.id}" <eq name="WebsiteData.profile_id" value="$profile.id">selected</eq>>
                                {$profile.display_name}
                                </option>
                            </volist>
                            <else />
                            <option value="-1">新增TDK模板后才可以选择</option>
                        </notempty>
                    </select>
                    <div class="layui-word-aux">请选择TDK调用模板</div>
                </div>
            </div>
        </div>

        <!-- 第二行：多行文本框 -->
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">绑定的域名</label>
            <div class="layui-input-block">
                <textarea name="bind_domains" placeholder="fanyuming.com&#13;fanyuming2.net" class="layui-textarea" style="height: 45%;">{$BindDomains}</textarea>
                <div class="layui-word-aux">输入绑定的域名，不需要带*号 一行一个域名</div>
            </div>
        </div>

        <!-- 第三行：按钮组 -->
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit lay-filter="form-submit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/2.1.4/jquery.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/layui/2.6.8/layui.min.js"></script>
<script>

    layui.use('form', function () {
        var form = layui.form;
        var layer = layui.layer;
        // 监听提交
        form.on('submit(form-submit)', function (data) {
            $.post('{:U("platform/website/siteEdit", array("mid" => $mid))}',data.field,function (response) {
                if(response.code === 200){
                    layer.msg(response.message);
                    setTimeout(function() {
                        var index = parent.layer.getFrameIndex(window.name); // 获取当前弹层的索引
                        parent.layer.close(index); // 关闭当前弹层
                        parent.location.reload(); // 刷新父页面
                    }, 3000); // 延迟3秒（3000毫秒）
                }else{
                    layer.msg(response.message);
                }
            })
            return false;
        });
    });
</script>