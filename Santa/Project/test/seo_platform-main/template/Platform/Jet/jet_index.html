<extend name="Public:base"/>
<block name="title">首页劫持统计</block>
<block name="content">
    <div class="page-header">
        <h1> 首页&gt; 代码劫持统计</h1>
    </div>
    <div class="col-xs-12">
        <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
            <li class="active"> <a href="#home" data-toggle="tab">全部列表</a></li>
        </ul>
        <div class="tabbable">
            <div class="tab-content">
                <foreach name="data" item="v"></foreach>
                <table class="table table-striped table-bordered table-hover table-condensed">
                    <tbody>
                    <tr>
                        <th>劫持ID</th>
                        <th>执行URL</th>
                        <th>客户端UA</th>
                        <th>发生时间</th>
                        <th>跳转域名</th>
                        <th>劫持URL</th>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>4</td>
                        <td>3</td>
                        <td>2</td>
                        <td>1</td>
                        <td>3</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="modal fade" id="bjy-add" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="myModalLabel"> 添加权限</h4>
                </div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="{:U('Platform/Rule/add')}" method="post">
                        <input type="hidden" name="pid" value="0" />
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tbody>
                            <tr>
                                <th width="12%">权限名：</th>
                                <td> <input class="input-medium" type="text" name="title" /></td>
                            </tr>
                            <tr>
                                <th>权限：</th>
                                <td> <input class="input-medium" type="text" name="name" /> 输入模块/控制器/方法即可 例如 Platform/Rule/index</td>
                            </tr>
                            <tr>
                                <th></th>
                                <td> <input class="btn btn-success" type="submit" value="添加" /></td>
                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="bjy-edit" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="myModalLabel"> 修改权限</h4>
                </div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="{:U('Platform/Rule/edit')}" method="post">
                        <input type="hidden" name="id" />
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tbody>
                            <tr>
                                <th width="12%">权限名：</th>
                                <td> <input class="input-medium" type="text" name="title" /></td>
                            </tr>
                            <tr>
                                <th>权限：</th>
                                <td> <input class="input-medium" type="text" name="name" /> 输入模块/控制器/方法即可 例如 Platform/Rule/index</td>
                            </tr>
                            <tr>
                                <th></th>
                                <td> <input class="btn btn-success" type="submit" value="修改" /></td>
                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</block>
<block name="js">
    <script>
        // 添加菜单
        function add(){
            $("input[name='title'],input[name='name']").val('');
            $("input[name='pid']").val(0);
            $('#bjy-add').modal('show');
        }

        // 添加子菜单
        function add_child(obj){
            var ruleId=$(obj).attr('ruleId');
            $("input[name='pid']").val(ruleId);
            $("input[name='title']").val('');
            $("input[name='name']").val('');
            $('#bjy-add').modal('show');
        }

        // 修改菜单
        function edit(obj){
            var ruleId=$(obj).attr('ruleId');
            var ruletitle=$(obj).attr('ruletitle');
            var ruleName=$(obj).attr('ruleName');
            $("input[name='id']").val(ruleId);
            $("input[name='title']").val(ruletitle);
            $("input[name='name']").val(ruleName);
            $('#bjy-edit').modal('show');
        }
    </script>
</block>