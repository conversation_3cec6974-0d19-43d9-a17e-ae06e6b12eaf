<extend name="Public:base"/>
<block name="title"><?php $types = $_GET['types']; if($types == 0) : echo ('全部列表'); ?><?php else:?><?php echo($list[$types]); ?><?php endif;?> - 今日蜘蛛爬行统计</block>
<block name="content">
    <div class="page-header">
        <h1> 站群管理 &gt; 今日蜘蛛爬行统计</h1>
    </div>
    <div class="col-xs-12">
        <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
            <?php $types = $_GET['types'];?>
            <li id="all_list" <?php if($types == 0): echo("class=\"active\""); ?> <?php endif;?>> <a href="{:U('Platform/Zqmanager/log','types=0')}">全部列表</a></li>
            <li id="baiduspider" <?php if($types == 1): echo("class=\"active\""); ?> <?php endif;?>> <a href="{:U('Platform/Zqmanager/log','types=1')}">百度蜘蛛</a></li>
            <li id="sogouspider" <?php if($types == 2): echo("class=\"active\""); ?> <?php endif;?>> <a href="{:U('Platform/Zqmanager/log','types=2')}">搜狗蜘蛛</a></li>
            <li id="haosouspider" <?php if($types == 3): echo("class=\"active\""); ?> <?php endif;?>> <a href="{:U('Platform/Zqmanager/log','types=3')}">360蜘蛛</a></li>
            <li id="smspider" <?php if($types == 4): echo("class=\"active\""); ?> <?php endif;?>> <a href="{:U('Platform/Zqmanager/log','types=4')}">神马蜘蛛</a></li>
        </ul>
        <style type="text/css">
            .center{
                text-align: center;
            }
            .glist{
                border-right: 15px;
                line-height: 15px;
                display: inline-block;
            }
        </style>
        <div class="tabbable">
            <div class="tab-content">
                <table class="table table-bordered table-hover">
                    <tr>
                        <th>ID</th>
                        <th>域名</th>
                        <th>蜘蛛类型</th>
                        <th>爬行 UrI</th>
                        <th>完整 User-Agent</th>
                        <th>蜘蛛 IP</th>
                        <th>来源</th>
                        <th>爬行时间</th>
                    </tr>
                    <?php if(!empty($data)) : ?>
                    <?php foreach($data as $key=>$value) : ?>
                    <tr>
                        <td><?php echo($data[$key]['zq_id']);?></td>
                        <td><a href="javascript:;" name="domain"><?php echo($data[$key]['zq_domain']);?></a></td>
                        <td><b class="blue"><?php echo($lists[$data[$key]['zq_spider_types']]);?></b></td>
                        <td><a href="javascript:;" title="点击访问" name="urls"><?php echo($data[$key]['zq_request_uri']);?></a></td>
                        <td> <details><summary>查看完整UA</summary><ul><li><?php echo(htmlentities($data[$key]['zq_full_ua']));?></li></ul></details></td>
                        <td><a href="javascript:;" name="query_location" title="单击追踪IP地理位置" data-client-ip="<?php echo($data[$key]['zq_real_ip']);?>"><?php echo($data[$key]['zq_real_ip']);?></a></td>
                        <td><?php echo($data[$key]['zq_remark']);?></td>
                        <td><b class="red"><?php echo(word_time($data[$key]['zq_request_times']));?></b></td>
                    </tr>
                    <?php endforeach;?>
                    <?php else:?>
                    <td colspan="8" class="center"><b class="red">未找到相关数据!</b></td>
                    <?php endif;?>
                </table>
                <nav style="text-align: center">
                    {$page}
                </nav>
            </div>
        </div>
    </div>

    <div class="modal fade" id="View-Location" tabindex="-1" role="dialog" aria-labelledby="View-Location" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="height: 600px;width: 600px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="ViewSites">追踪地理位置</h4></div>
                    <div class="modal-body">
                        <iframe id ="ip138_api" style="height: 520px;width: 570px;" src="about:blank;" width='100%' height='100%'></iframe>
                    </div>
            </div>
        </div>
    </div>
</block>
<block name="js">
    <script type="text/javascript">
            $("a[name='query_location']").click(function(){
                real_client_ip = $(this).attr("data-client-ip");
                ip138_url = "http://ip138.com/iplookup.asp?ip=" + real_client_ip;
                $("#ip138_api").attr('src',ip138_url);
                $("#View-Location").modal('show');
            });
    </script>
</block>