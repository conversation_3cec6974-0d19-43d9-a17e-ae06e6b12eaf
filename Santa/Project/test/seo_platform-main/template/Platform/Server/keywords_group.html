<extend name="Public:base"/>
<block name="title">关键词组管理</block>
<block name="content">
    <div class="page-header">
        <h1> 服务端管理&gt; 关键词组管理</h1>
    </div>
    <div class="col-xs-12">
        <ul class="nav nav-tabs padding-12 tab-color-blue background-blue" id="myTab">
            <li class="active"> <a href="#home" data-toggle="tab">关键词列表</a></li>
        </ul>
        <div class="tabbable">
            <div class="tab-content">
                <table class="table table-striped table-bordered table-hover table-condensed">
                    <tbody>
                    <tr>
                        <th>词库ID</th>
                        <th>关键词分类</th>
                        <th>创建时间</th>
                        <th>主词总数</th>
                        <th>附词总数</th>
						<th>词库标识</th>
                        <th>操作</th>
                    </tr>
                    <?php if(is_array($datas)) : 
                        foreach($datas as $key=>$val):
                    ?>
                    <tr>
                        <td><?php echo ($datas[$key]["keywords_gid"]);?></td>
                        <td><?php echo ($datas[$key]["keywords_name"]);?></td>
                        <td><?php echo date('Y-m-d H:i:s', $datas[$key]["keywords_create_time"]);?></td>
                        <td><?php if(!empty($datas[$key]["title_count"])):?><?php echo ($datas[$key]["title_count"]);?><?php else: ?>0<?php endif;?></td>
                        <td><?php if(!empty($datas[$key]["lf_count"])):?><?php echo ($datas[$key]["lf_count"]);?><?php else: ?>0<?php endif;?></td>
						<td><?php if(!empty($datas[$key]["keywords_remarks"])):?><?php echo ($datas[$key]["keywords_remarks"]);?><?php else: ?>0<?php endif;?></td>
                        <td><a href="javascript:void(0);">关键词导入</a>|<a href="javascript:void(0);">更新缓存</a></td>
                </tr>
                <?php endforeach;?>
                <?php endif;?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="modal fade" id="bjy-add" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"> &times;</button>
                    <h4 class="modal-title" id="myModalLabel"> 添加词库</h4>
                </div>
                <div class="modal-body">
                    <form id="bjy-form" class="form-inline" action="{:U('Platform/Rule/add')}" method="post">
                        <input type="hidden" name="pid" value="0" />
                        <table class="table table-striped table-bordered table-hover table-condensed">
                            <tbody>
                            <tr>
                                <th width="12%">词库标识：</th>
                                <td> <input class="input-medium" type="text" name="title" /></td>
                            </tr>
                            <tr>
                                <th>上传文件：</th>
                                <td> <input class="input-medium" type="text" name="name" /> 输入模块/控制器/方法即可 例如 Platform/Rule/index</td>
                            </tr>
                            <tr>
                                <th></th>
                                <td> <input class="btn btn-success" type="submit" value="创建词库" /></td>
                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</block>
<block name="js">
    <script>
        // 添加菜单
        function add(){
            $("input[name='title'],input[name='name']").val('');
            $("input[name='pid']").val(0);
            $('#bjy-add').modal('show');
        }

        // 添加子菜单
        function add_child(obj){
            var ruleId=$(obj).attr('ruleId');
            $("input[name='pid']").val(ruleId);
            $("input[name='title']").val('');
            $("input[name='name']").val('');
            $('#bjy-add').modal('show');
        }

        // 修改菜单
        function edit(obj){
            var ruleId=$(obj).attr('ruleId');
            var ruletitle=$(obj).attr('ruletitle');
            var ruleName=$(obj).attr('ruleName');
            $("input[name='id']").val(ruleId);
            $("input[name='title']").val(ruletitle);
            $("input[name='name']").val(ruleName);
            $('#bjy-edit').modal('show');
        }
    </script></block>