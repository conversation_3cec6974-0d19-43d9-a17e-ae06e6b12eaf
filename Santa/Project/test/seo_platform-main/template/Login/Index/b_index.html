<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<logincss />
<jquery />
<layer />
<title>SEO管控平台系统登陆 - SEO Work Platform System V 3.0</title>
<meta name="keyword" content="seo工作平台,seo Platform,黑帽seo,白帽seo,灰帽seo" />
<meta name="description" content="适用于网站优化工作人员" />
<meta name="author" content="X37 Team Inc." />
<link href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<meta property="og:title" content="SEO管控平台系统登陆" class="next-head" class="next-head"/>
<meta property="og:site_name" content="SEO管控平台系统登陆" class="next-head" class="next-head"/>
<meta property="og:image" content="__PUBLIC__/logo.jpg"/>
<meta property="twitter:title" content="SEO管控平台系统登陆" class="next-head"/>
<meta property="twitter:site" content="SEO管控平台系统登陆" class="next-head"/>
<meta property="twitter:card" content="summary_large_image" class="next-head"/>
<meta name="robots" content="index" class="next-head"/>
<meta property="og:description" content="SEO管控平台系统登陆 - SEO Work Platform System V 3.0" class="next-head"/>
<meta property="twitter:description" content="SEO管控平台系统登陆 - SEO Work Platform System V 3.0" class="next-head"/>
<script src="https://recaptcha.net/recaptcha/api.js" async defer></script>
	<style>
		.g-recaptcha{
			transform:scale(0.77);
			-webkit-transform:scale(0.77);
			transform-origin:0 0;
			-webkit-transform-origin:0 0;
		}
	</style>
</head>
<body>
	<div class="login-page">
		<div class="form">
			<h3>[root@localhost ~]#</h3>
			<hr />
			<form class="login-form" method="POST" id="login" action="{:U('login/index/index')}">
				<input type="text" name="username" placeholder="帐号 Username" required autofocus />
				<input type="password" name="password" placeholder="密码 Password" />
				<div class="g-recaptcha" data-sitekey="{$pubkey}"></div>
				<br/>
                     {__TOKEN__} <br />
				<button id="submit" class="">
					<b>Login</b>
				</button>
			</form>
			<p class="message">Power by X37 Team Inc.</p>
		</div>
	</div>
	<script type="text/javascript">
            $('#login').submit(function(event) {
				event.preventDefault();
				if ($("input[name='username']").val() == '') {
					layer.msg('请填写帐号后在提交', {icon: 2, time: 700});
					return false;
				}

				if ($("input[name='password']").val() == '') {
					layer.msg('请填写密码后在提交', {icon: 2, time: 700});
					return false;
				}

				if ($("input[name='verify']").val() == '') {
					layer.msg('请填写密码后在提交', {icon: 2, time: 700});
					return false;
				}
				var verify = $("textarea[name='g-recaptcha-response']").val();

				if (verify.length <= 0) {
					layer.msg('请完成人机验证', {icon: 2, time: 700});
					return false;
				}

				i_username = $("input[name='username']").val();
				i_password = $("input[name='password']").val();
				i_verify = $("input[name='verify']").val();

				auth = {
					username: i_username,
					password: i_password,
					recaptcha: verify
				};

				$.post("{:U('login/index/index')}", auth, function (data, textStatus, xhr) {
					// 失败
					if (data.code === 404) {
						layer.msg(data.msg, {icon: 2, time: 1000},function(){
							// 不管是验证码错误还是密码错误 都需要重写获取验证码
							var timestamp = new Date().getTime();
							$("img[name='verify_images']").attr("src", "{:U('login/index/verify')}&" + $("input[name='verify']").val() + '?' + timestamp);
						});
						return false;
					}
					// 成功
					if(data.code === 200){
						layer.msg(data.msg, {icon: 16, time: 3000},function(){
							window.location.href = data.redirect;
						})
					}
				});
			});
    </script>
</body>
</html>