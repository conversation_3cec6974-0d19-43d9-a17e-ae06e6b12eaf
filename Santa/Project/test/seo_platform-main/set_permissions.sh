#!/bin/bash

# SEO平台文件权限设置脚本
# 使用方法: chmod +x set_permissions.sh && ./set_permissions.sh

echo "开始设置SEO平台文件权限..."

# 设置基本目录权限
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 设置可写目录权限
chmod -R 777 runtime/
chmod -R 755 public/

# 设置配置文件权限（更安全）
chmod 600 application/Common/Conf/*.php

# 设置入口文件权限
chmod 644 index.php

# 设置SQL文件权限（防止直接访问）
chmod 600 *.sql

echo "文件权限设置完成！"

# 显示关键目录权限
echo "关键目录权限检查："
ls -la runtime/
ls -la application/Common/Conf/
ls -la public/

echo "请确保Web服务器用户（如www-data, nginx, apache）对runtime目录有写权限"
echo "建议执行: chown -R www-data:www-data runtime/"
