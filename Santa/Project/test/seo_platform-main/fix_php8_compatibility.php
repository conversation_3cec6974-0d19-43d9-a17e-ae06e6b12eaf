<?php
/**
 * PHP 8 兼容性修复脚本
 * 将花括号数组/字符串访问语法改为方括号语法
 */

function fixCurlyBraces($directory) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory)
    );
    
    $phpFiles = new RegexIterator($iterator, '/\.php$/');
    $fixedFiles = 0;
    $totalReplacements = 0;
    
    foreach ($phpFiles as $file) {
        $filePath = $file->getPathname();
        $content = file_get_contents($filePath);
        $originalContent = $content;
        
        // 修复 $variable{index} 为 $variable[index]
        $pattern = '/(\$[a-zA-Z_][a-zA-Z0-9_]*)\{([^}]+)\}/';
        $replacement = '$1[$2]';
        $content = preg_replace($pattern, $replacement, $content);
        
        if ($content !== $originalContent) {
            $replacements = substr_count($originalContent, '{') - substr_count($content, '{');
            if ($replacements > 0) {
                file_put_contents($filePath, $content);
                echo "修复文件: " . $filePath . " (替换了 {$replacements} 处)\n";
                $fixedFiles++;
                $totalReplacements += $replacements;
            }
        }
    }
    
    echo "\n修复完成!\n";
    echo "修复文件数: {$fixedFiles}\n";
    echo "总替换数: {$totalReplacements}\n";
}

// 修复framework目录
echo "开始修复PHP 8兼容性问题...\n";
fixCurlyBraces(__DIR__ . '/framework');

echo "\n检查是否还有遗漏的花括号语法...\n";
exec('grep -r \'\\$[a-zA-Z_][a-zA-Z0-9_]*{\' framework --include="*.php"', $output);
if (empty($output)) {
    echo "✅ 所有花括号语法已修复完成!\n";
} else {
    echo "⚠️  还有以下文件需要手动检查:\n";
    foreach ($output as $line) {
        echo $line . "\n";
    }
}
?>
