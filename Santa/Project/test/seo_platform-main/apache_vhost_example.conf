<VirtualHost *:80>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    DocumentRoot /path/to/seo_platform-main
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/seo_platform_error.log
    CustomLog ${APACHE_LOG_DIR}/seo_platform_access.log combined

    # 目录权限
    <Directory /path/to/seo_platform-main>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    # URL重写（需要启用mod_rewrite）
    <IfModule mod_rewrite.c>
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php?/$1 [QSA,PT,L]
    </IfModule>

    # 安全配置
    <DirectoryMatch "/(application|framework|runtime|template)/">
        Require all denied
    </DirectoryMatch>

    <FilesMatch "\.(sql|log|conf)$">
        Require all denied
    </FilesMatch>
</VirtualHost>
