server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /path/to/seo_platform-main;
    index index.php index.html index.htm;

    # 日志配置
    access_log /var/log/nginx/seo_platform_access.log;
    error_log /var/log/nginx/seo_platform_error.log;

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;  # 或者 unix:/var/run/php/php7.4-fpm.sock
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # URL重写规则（ThinkPHP框架）
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    location ~ /(application|framework|runtime|template)/ {
        deny all;
    }

    # 禁止访问敏感文件
    location ~* \.(sql|log|conf)$ {
        deny all;
    }
}
