#!/bin/bash

# SEO平台快速部署脚本
# 使用前请先修改配置变量

echo "🚀 SEO平台快速部署脚本"
echo "=========================="

# 配置变量（请根据实际情况修改）
DB_ROOT_PASSWORD="your_mysql_root_password"
DOMAIN_NAME="your-domain.com"
PROJECT_PATH="/var/www/seo_platform"
WEB_USER="www-data"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root用户运行此脚本"
    exit 1
fi

# 1. 创建数据库
log_info "创建数据库和用户..."
mysql -u root -p${DB_ROOT_PASSWORD} << EOF
CREATE DATABASE IF NOT EXISTS seo_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS seo_spider CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

CREATE USER IF NOT EXISTS 'seo_platform'@'localhost' IDENTIFIED BY 'seo_platformv3.0';
CREATE USER IF NOT EXISTS 'seo_spider'@'localhost' IDENTIFIED BY 'seo_spiderv3.0';

GRANT ALL PRIVILEGES ON seo_platform.* TO 'seo_platform'@'localhost';
GRANT ALL PRIVILEGES ON seo_spider.* TO 'seo_spider'@'localhost';
FLUSH PRIVILEGES;
EOF

if [ $? -eq 0 ]; then
    log_info "数据库创建成功"
else
    log_error "数据库创建失败"
    exit 1
fi

# 2. 导入SQL文件
log_info "导入数据库结构..."
if [ -f "seo_platform0330-25.sql" ]; then
    mysql -u seo_platform -pseo_platformv3.0 seo_platform < seo_platform0330-25.sql
    if [ $? -eq 0 ]; then
        log_info "数据库导入成功"
    else
        log_error "数据库导入失败"
        exit 1
    fi
else
    log_error "SQL文件不存在"
    exit 1
fi

# 3. 设置文件权限
log_info "设置文件权限..."
chmod +x set_permissions.sh
./set_permissions.sh

# 设置所有者
chown -R ${WEB_USER}:${WEB_USER} .
chown -R ${WEB_USER}:${WEB_USER} runtime/

# 4. 创建Nginx虚拟主机配置
log_info "创建Nginx虚拟主机配置..."
cat > /etc/nginx/sites-available/seo_platform << EOF
server {
    listen 80;
    server_name ${DOMAIN_NAME};
    root ${PROJECT_PATH};
    index index.php index.html index.htm;

    access_log /var/log/nginx/seo_platform_access.log;
    error_log /var/log/nginx/seo_platform_error.log;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~ /\. {
        deny all;
    }

    location ~ /(application|framework|runtime|template)/ {
        deny all;
    }

    location ~* \.(sql|log|conf)$ {
        deny all;
    }
}
EOF

# 启用站点
ln -sf /etc/nginx/sites-available/seo_platform /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

# 5. 验证部署
log_info "验证部署..."
if systemctl is-active --quiet nginx && systemctl is-active --quiet mysql; then
    log_info "服务运行正常"
else
    log_warn "请检查服务状态"
fi

# 6. 显示部署信息
echo ""
echo "🎉 部署完成！"
echo "=========================="
echo "访问地址: http://${DOMAIN_NAME}"
echo "后台登录: http://${DOMAIN_NAME}/Login"
echo ""
echo "📋 后续步骤："
echo "1. 修改 application/Common/Conf/control.php 中的API地址"
echo "2. 配置Google reCAPTCHA密钥"
echo "3. 修改默认的API访问令牌"
echo "4. 查看数据库中的管理员账户信息"
echo ""
echo "📖 详细文档："
echo "- 安全配置: SECURITY_CONFIG.md"
echo "- 部署检查: DEPLOYMENT_CHECKLIST.md"
echo ""
log_warn "请及时修改默认密码和配置！"
